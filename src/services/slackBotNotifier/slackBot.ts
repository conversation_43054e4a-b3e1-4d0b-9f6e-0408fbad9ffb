import { logger } from '@/clean/lib/logger';
import { WebClient } from '@slack/web-api';
import { slackTexts } from '@/constants';

interface SlackBlock {
  type: string;
  text?: {
    type: string;
    text: string;
    emoji?: boolean;
  };
}

interface SlackMessageProps {
  text: string;
  blocks: SlackBlock[];
  attachments?: {
    color: string;
    text: string;
  }[];
}

interface SlackFileProps {
  content: string;
  filename: string;
  title: string;
  filetype?: string;
}

export async function slackChannelNotifier({
  message,
  file,
  BotToken,
  ChannelId,
}: {
  message: SlackMessageProps;
  file?: SlackFileProps;
  BotToken: string;
  ChannelId: string;
}) {
  const slackClient = new WebClient(BotToken);
  let success = false;

  try {
    const channelId = ChannelId;
    if (channelId) {
      try {
        await slackClient.chat.postMessage({
          channel: channelId,
          ...message,
        });

        if (file) {
          try {
            await slackClient.files.uploadV2({
              channel_id: channelId,
              content: file.content,
              filename: file.filename,
              title: file.title,
              filetype: file.filetype,
            });
          } catch (uploadError) {
            logger.error(`[slackChannelNotifier] Error uploading file to channel ${channelId}:`, uploadError);
            return success;
          }
        }
        success = true;
      } catch (channelError) {
        logger.error(`[slackChannelNotifier] Error sending message to channel ${channelId}:`, channelError);
        return success;
      }
    } else {
      logger.error('[slackChannelNotifier] Failed to send notifications. Channel ID missing');
    }

    return success;
  } catch (error: any) {
    logger.error('[slackChannelNotifier] Error sending Slack notifications:', error);
    return false;
  }
}

interface SlackErrorNotificationProps {
  headerText: string;
  userName: string;
  errorMessage: string;
  bottomText: string;
}

export function createSlackErrorNotification({
  headerText,
  userName,
  errorMessage,
  bottomText,
}: SlackErrorNotificationProps): SlackMessageProps {
  return {
    text: errorMessage,
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: headerText,
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: slackTexts.summaryTitle(userName),
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: errorMessage,
        },
      },
    ],
    attachments: [
      {
        color: '#ff0000',
        text: bottomText,
      },
    ],
  };
}

interface SlackSummaryNotificationProps {
  headerText: string;
  summaryTitle: string;
  summaryMessage: string;
  hasErrors: boolean;
  bottomText: string;
}

export function createSlackSummaryNotification({
  headerText,
  summaryTitle,
  summaryMessage,
  hasErrors,
  bottomText,
}: SlackSummaryNotificationProps): SlackMessageProps {
  return {
    text: summaryMessage,
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: headerText,
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: summaryTitle,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: summaryMessage,
        },
      },
    ],
    attachments: [
      {
        color: hasErrors ? '#ff0000' : '#36a64f',
        text: bottomText,
      },
    ],
  };
}

export async function getSlackUserIdByEmail({
  email,
  BotToken,
}: {
  email: string;
  BotToken: string;
}): Promise<string | null> {
  const slackClient = new WebClient(BotToken);

  try {
    const response = await slackClient.users.lookupByEmail({ email });
    return response.user?.id || null;
  } catch (error) {
    logger.error(`[getSlackUserIdByEmail] Error fetching Slack user ID for email ${email}:`, error);
    return null;
  }
}
