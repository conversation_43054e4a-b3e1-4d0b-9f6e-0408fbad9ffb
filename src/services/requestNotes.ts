import { Types } from 'mongoose';
import { RequestNoteMongo, RequestNoteMongoI } from '../models/requestNotesSchema';

import httpStatus from 'http-status';
import { ApiError } from '@/utils/apiError.utils';

export const createNote = async (
  data: { content: string; admissionRequest?: Types.ObjectId },
  userId: Types.ObjectId
): Promise<RequestNoteMongoI> => {
  return RequestNoteMongo.create({
    ...data,
    author: userId,
  });
};

export const getNotesByUser = async (userId: Types.ObjectId): Promise<RequestNoteMongoI[]> => {
  return RequestNoteMongo.find({ author: userId }).populate('admissionRequest');
};

export const getNoteById = async (
  noteId: Types.ObjectId,
  userId: Types.ObjectId
): Promise<RequestNoteMongoI | null> => {
  const note = await RequestNoteMongo.findById(noteId).populate('admissionRequest');

  if (!note) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Note not found');
  }

  if (note.author.toString() !== userId.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not own this note');
  }

  return note;
};

export const updateNoteById = async (
  noteId: Types.ObjectId,
  userId: Types.ObjectId,
  updateData: { content?: string; admissionRequest?: Types.ObjectId }
): Promise<RequestNoteMongoI | null> => {
  const note = await RequestNoteMongo.findById(noteId);

  if (!note) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Note not found');
  }

  if (note.author.toString() !== userId.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not own this note');
  }

  return RequestNoteMongo.findByIdAndUpdate(noteId, updateData, {
    new: true,
    runValidators: true,
  });
};

export const deleteNoteById = async (noteId: Types.ObjectId, userId: Types.ObjectId): Promise<void> => {
  const note = await RequestNoteMongo.findById(noteId);

  if (!note) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Note not found');
  }

  if (note.author.toString() !== userId.toString()) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not own this note');
  }

  await RequestNoteMongo.findByIdAndDelete(noteId);
};

export const getNotesByAdmissionRequest = async (
  admissionRequestId: Types.ObjectId,
  page: number = 1,
  limit: number = 10
): Promise<{
  notes: RequestNoteMongoI[];
  total: number;
  page: number;
  totalPages: number;
}> => {
  const skip = (page - 1) * limit;

  const [notes, total] = await Promise.all([
    RequestNoteMongo.find({ admissionRequest: admissionRequestId })
      .populate('author', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    RequestNoteMongo.countDocuments({ admissionRequest: admissionRequestId }),
  ]);

  const totalPages = Math.ceil(total / limit);

  return {
    notes,
    total,
    page,
    totalPages,
  };
};
