import { Types } from 'mongoose';
import QRScanHistory, { QRScanHistoryActionType } from '../models/qrScanHistorySchema';
import { PhysicalVehicleStatus } from '../models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';
import { DocumentMongoI } from '../models/documentSchema';
import { getUrlSingleFile } from '../aws/s3';

interface PopulatedPhotoDocument extends DocumentMongoI {
  url?: string;
}

interface CreateQRScanHistoryParams {
  vehicleId: Types.ObjectId;
  userId: Types.ObjectId;
  statusChangedFrom: PhysicalVehicleStatus;
  statusChangedTo: PhysicalVehicleStatus;
  location?: string;
  deviceInfo?: string;
  actionToken?: string;
  actionType: QRScanHistoryActionType;
  notes?: string;
  photoDocId?: Types.ObjectId; // Reference to already uploaded photo
  vendorRegion?: string;
  vendorWorkshopName?: string;
  isAdminCorrection?: boolean;
  vendorUserName?: string;
}

export const createQRScanHistoryEntry = async ({
  vehicleId,
  userId,
  statusChangedFrom,
  statusChangedTo,
  location,
  deviceInfo,
  actionToken,
  actionType,
  notes,
  photoDocId,
  vendorRegion,
  vendorWorkshopName,
  isAdminCorrection,
  vendorUserName,
}: CreateQRScanHistoryParams): Promise<Types.ObjectId> => {
  try {
    const scanEntry = new QRScanHistory({
      vehicleId,
      userId,
      scanTime: new Date(),
      statusChangedFrom,
      statusChangedTo,
      location,
      deviceInfo,
      actionToken,
      actionType,
      notes,
      photo: photoDocId,
      vendorRegion,
      vendorWorkshopName,
      isAdminCorrection,
      vendorUserName,
    });

    await scanEntry.save();
    logger.info(`[QR] History entry created for vehicle: ${vehicleId}, action: ${actionType}`);
    return scanEntry._id;
  } catch (error) {
    logger.error('[QR] Error creating QR scan history entry:', error);
    throw error;
  }
};

export const getQRScanHistoryForVehicle = async (vehicleId: Types.ObjectId) => {
  try {
    const historyItems = await QRScanHistory.find({ vehicleId })
      .populate('userId', 'name email')
      .populate({
        path: 'photo',
        select: 'path originalName',
      })
      .sort({ scanTime: -1 })
      .lean();
    for (const item of historyItems) {
      if (
        item.photo &&
        typeof item.photo === 'object' &&
        'path' in item.photo &&
        typeof (item.photo as any).path === 'string'
      ) {
        const photoDocument = item.photo as unknown as PopulatedPhotoDocument;
        try {
          const photoUrl = await getUrlSingleFile(photoDocument.path);
          if (photoUrl) {
            photoDocument.url = photoUrl;
          }
        } catch (urlError) {
          logger.error(
            `[getQRScanHistoryForVehicle] Error generating URL for photo path ${photoDocument.path}: ${urlError instanceof Error ? urlError.message : 'Unknown error'}`
          );
        }
      }
    }
    return historyItems;
  } catch (error) {
    logger.error(
      `[getQRScanHistoryForVehicle] Error fetching QR scan history: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    );
    throw error;
  }
};

export const getRecentQRScanActivity = async (limit = 50) => {
  try {
    const recentActivity = await QRScanHistory.find()
      .sort({ scanTime: -1 })
      .limit(limit)
      .populate('userId', 'firstName lastName email')
      .populate('vehicleId', 'carNumber brand model')
      .lean();

    logger.info(`[getRecentQRScanActivity] Retrieved ${recentActivity.length} recent QR scan activities`);
    return recentActivity;
  } catch (error) {
    logger.error(
      `[getRecentQRScanActivity] Error fetching recent QR scan activity: ${error instanceof Error ? error.message : error}`,
      error
    );
    throw error;
  }
};

/**
 * Gets statistics on QR scans - might be useable later on for monitoring
 */
export const getQRScanStats = async () => {
  try {
    const totalScans = await QRScanHistory.countDocuments();
    const statusChanges = await QRScanHistory.countDocuments({
      actionType: QRScanHistoryActionType.STATUS_CHANGE,
    });
    const scansByDay = await QRScanHistory.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$scanTime' },
            month: { $month: '$scanTime' },
            day: { $dayOfMonth: '$scanTime' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': -1, '_id.month': -1, '_id.day': -1 } },
      { $limit: 30 },
    ]);

    return {
      totalScans,
      statusChanges,
      scansPerDay: scansByDay.map((item) => ({
        date: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
        count: item.count,
      })),
    };
  } catch (error) {
    logger.error(
      `[getQRScanStats] Error fetching QR scan statistics: ${error instanceof Error ? error.message : error}`,
      error
    );
    throw error;
  }
};
