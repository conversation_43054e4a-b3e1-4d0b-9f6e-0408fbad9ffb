import { logger } from '../clean/lib/logger';
import { createHash } from 'crypto';
import { repoGetDocumentData, repoDeleteDocument } from '../clean/data/s3Repositories';
import {
  parseStructuredTextFromSource,
  StructuredSource,
  AllowedMimeType,
} from '../services/ocr/llmClients/geminiClient';
import StockVehicle from '../models/StockVehicleSchema';
import Document from '../models/documentSchema';
import {
  slackChannelNotifier,
  getSlackUserIdByEmail,
  createSlackSummaryNotification,
} from '../services/slackBotNotifier/slackBot';
import {
  vehicleDocumentsSlackTexts,
  SLACK_NOTIFIER_BOT_TOKEN,
  CountriesEnum,
  GEMINI_MODEL,
  GEMINI_BACKOFF_MODEL,
} from '../constants';
import { Types } from 'mongoose';
import {
  VehicleDocumentBatchPayload,
  ProcessedDocumentResult,
  DocumentCategory,
  ProcessedDocumentStatus,
} from '../types&interfaces/vehicleDocuments';
import { ObjectSchema, Schema, SchemaType } from '@google/generative-ai';
import * as cheerio from 'cheerio';
import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { HttpsProxyAgent, HttpsProxyAgentOptions } from 'https-proxy-agent';
import { getCurrentDateTime } from './timestamps';
import { checkAndUpdateVehicleStatus } from './stockVehicles/vehicleFunctions';
import {
  DocumentValidators,
  DocumentProcessingError,
  DocumentProcessingErrorType,
} from './validators/documentValidators';

// Interface for VIN fetch result with detailed status
interface VinFetchResult {
  vin: string | null;
  success: boolean;
  errorType?: 'SERVICE_DOWN' | 'VIN_NOT_FOUND' | 'UNKNOWN_ERROR';
  errorMessage?: string;
}

// VIN validation and extraction regex constants
const VIN_ALLOWED_CHARS = '[A-HJ-NPR-Z0-9]';
const VIN_EXACT_REGEX = new RegExp(`^${VIN_ALLOWED_CHARS}{17}$`, 'i');
const VIN_WORD_REGEX = new RegExp(`\\b(${VIN_ALLOWED_CHARS}{17})\\b`, 'gi');

// TO USE PROXY WITH VERIFY=FALSE
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

/**
 * Parses HTML content to extract the VIN value.
 * @param {string} htmlContent The HTML content as a string.
 * @returns {string | null} The extracted VIN, or null if not found.
 */
function parseVinFromHtml(htmlContent: string): string | null {
  if (!htmlContent) {
    return null;
  }
  const $ = cheerio.load(htmlContent);
  let vinText: string = '';
  $('li').each((index, element) => {
    const strongTag = $(element).find('strong');
    if (strongTag.length && strongTag.text().trim() === 'Número de serie:') {
      let fullText = $(element).text();
      vinText = fullText.replace(strongTag.text(), '').replace(':', '').trim();
      if (VIN_EXACT_REGEX.test(vinText)) {
        return false;
      }
      vinText = '';
    }
    return true;
  });
  if (vinText) {
    return vinText.toUpperCase();
  }
  const text = $('body').text().replace(/\s+/g, ' ');
  const vinRegex = VIN_WORD_REGEX;
  const match = vinRegex.exec(text);
  if (match && match[1] && VIN_EXACT_REGEX.test(match[1])) {
    return match[1].toUpperCase();
  }
  let vinInput = $('input[name="vin"]').first();
  if (!vinInput.length) {
    vinInput = $('input[id="vin"]').first();
  }
  if (vinInput.length) {
    const vinValue = vinInput.val();
    if (typeof vinValue === 'string' && VIN_EXACT_REGEX.test(vinValue)) {
      return vinValue.toUpperCase();
    }
  }
  return null;
}

const createFileHash = (buffer: Buffer): string => {
  return createHash('sha256').update(buffer).digest('hex');
};

class VehicleDocumentProcessingService {
  /**
   * Sanitizes validation errors to prevent exposing internal implementation details
   * to client-facing responses. Replaces specific technical errors with generic
   * "document validation failed" messages.
   */
  private sanitizeValidationErrorsForClient(errors: string[]): string[] {
    return DocumentValidators.sanitizeValidationErrorsForClient(errors);
  }

  public async processVehicleDocumentBatch(payload: VehicleDocumentBatchPayload): Promise<void> {
    const { documents, userId, userName, userEmail } = payload;
    logger.info(
      `[VehicleDocService] Starting batch processing for ${documents.length} docs. User: ${userName}`
    );

    const slackUserIdOrNull = await getSlackUserIdByEmail({
      email: userEmail,
      BotToken: SLACK_NOTIFIER_BOT_TOKEN,
    });
    const slackUserId = slackUserIdOrNull || undefined;

    const processingResults: ProcessedDocumentResult[] = [];
    const categoryCounts: Record<string, number> = {};

    for (const docInfo of documents) {
      categoryCounts[docInfo.documentCategory] = (categoryCounts[docInfo.documentCategory] || 0) + 1;
      let result: ProcessedDocumentResult = {
        originalFileName: docInfo.originalFileName,
        s3Key: docInfo.s3Key,
        status: 'error',
      };
      try {
        logger.info(
          `[VehicleDocService] Processing file: ${docInfo.originalFileName} (s3Key: ${docInfo.s3Key})`
        );
        const fileBase64 = await repoGetDocumentData(docInfo.s3Key);
        if (!fileBase64)
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.PROCESSING_ERROR,
            message: 'No se pudo obtener datos del archivo desde S3.',
          });
        const fileBuffer = Buffer.from(fileBase64, 'base64');
        if (fileBuffer.byteLength > 5 * 1024 * 1024)
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.FILE_TOO_LARGE,
            message: 'El archivo excede el límite de 5MB.',
            status: 'skipped',
          });

        const fileHash = createFileHash(fileBuffer);

        // OCR to extract VIN and category-specific data, including embedded authenticity markers
        const ocrData = await this.extractOCRData(docInfo, fileBase64, payload.country);
        result.vin = ocrData?.vin?.trim() || null;
        logger.info(
          `[VehicleDocService] OCR processing successful for ${docInfo.originalFileName}. Final VIN: ${result.vin}`
        );

        let vehicle;
        if (
          docInfo.documentCategory === DocumentCategory.PLATES_FRONT ||
          docInfo.documentCategory === DocumentCategory.PLATES_BACK
        ) {
          const platesFromOCR = ocrData.plates?.trim() || undefined;
          result.plates = platesFromOCR;
          vehicle = await StockVehicle.findOne({ 'carPlates.plates': platesFromOCR });
          if (!vehicle)
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.VEHICLE_NOT_FOUND,
              message: `Vehículo con placas ${platesFromOCR} no encontrado.`,
            });
        } else if (docInfo.documentCategory === DocumentCategory.TENENCIA) {
          // Fallback strategy for TENENCIA: try VIN first, then plates
          const vinFromOCR = ocrData.vin?.trim() || undefined;
          const platesFromOCR = ocrData.plates?.trim() || undefined;

          result.vin = vinFromOCR;
          result.plates = platesFromOCR;

          if (vinFromOCR) {
            // Try to find by VIN first
            vehicle = await StockVehicle.findOne({ vin: vinFromOCR });
            if (vehicle) {
              logger.info(`[VehicleDocService] Found vehicle for TENENCIA by VIN: ${vinFromOCR}`);
            }
          }

          if (!vehicle && platesFromOCR) {
            // Fallback to plates if VIN search failed
            vehicle = await StockVehicle.findOne({ 'carPlates.plates': platesFromOCR });
            if (vehicle) {
              logger.info(
                `[VehicleDocService] Found vehicle for TENENCIA by plates (fallback): ${platesFromOCR}`
              );
            }
          }

          if (!vehicle) {
            const identifierUsed = vinFromOCR || platesFromOCR || 'N/A';
            const identifierType = vinFromOCR ? 'VIN' : platesFromOCR ? 'placas' : 'identificador';
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.VEHICLE_NOT_FOUND,
              message: `Vehículo con ${identifierType} ${identifierUsed} no encontrado.`,
            });
          }
        } else {
          vehicle = await StockVehicle.findOne({ vin: result.vin });
          if (!vehicle)
            throw new DocumentProcessingError({
              type: DocumentProcessingErrorType.VEHICLE_NOT_FOUND,
              message: `Vehículo con VIN ${result.vin} no encontrado.`,
            });
        }
        result.vehicleCarNumber = vehicle.carNumber;

        const existingDocument = await Document.findOne({
          vehicleId: vehicle._id,
          documentCategory: docInfo.documentCategory,
          fileHash: fileHash,
        });
        if (existingDocument)
          throw new DocumentProcessingError({
            type: DocumentProcessingErrorType.DUPLICATE_DOCUMENT,
            message: 'Documento duplicado para este vehículo y categoría.',
            status: 'duplicate',
          });

        const newDocument = new Document({
          originalName: docInfo.originalFileName,
          path: docInfo.s3Key,
          mimeType: docInfo.contentType,
          vehicleId: vehicle._id,
          userId: new Types.ObjectId(userId),
          documentCategory: docInfo.documentCategory,
          vin: result.vin,
          fileHash: fileHash,
          status: 'processed',
        });
        await newDocument.save();
        result.documentId = newDocument._id.toString();

        const oldDocIdsToMove: Types.ObjectId[] = [];
        const updateSuccess = this.assignOcrDataToVehicle({
          vehicle,
          documentCategory: docInfo.documentCategory,
          newDocumentId: newDocument._id,
          ocrData,
          oldDocIdsToMove,
        });

        if (updateSuccess) {
          if (oldDocIdsToMove.length > 0) {
            const docsToMove = await Document.find({ _id: { $in: oldDocIdsToMove } });
            for (const oldDoc of docsToMove) {
              if (oldDoc.fileHash !== undefined) {
                oldDoc.fileHash = undefined;
                await oldDoc.save();
              }
              vehicle.oldDocuments.push({ path: oldDoc.path, originalName: oldDoc.originalName });
            }
          }
          vehicle.updateHistory.push({
            userId: new Types.ObjectId(userId),
            step: 'DOCUMENTACIÓN',
            description: `Se asignó el documento ${docInfo.originalFileName} (${docInfo.documentCategory}).`,
            time: getCurrentDateTime(),
          });
          checkAndUpdateVehicleStatus(vehicle, userId);
          await vehicle.save();
          logger.info(
            `[VehicleDocService] Successfully updated vehicle ${vehicle.carNumber} with document ${newDocument._id} for category ${docInfo.documentCategory}`
          );
          result.status = 'success';
        }
        processingResults.push(result);
      } catch (err: any) {
        const procError =
          err instanceof DocumentProcessingError
            ? err
            : new DocumentProcessingError({
                type: DocumentProcessingErrorType.PROCESSING_ERROR,
                message: (err as Error).message,
              });
        if (docInfo.s3Key) await repoDeleteDocument(docInfo.s3Key);
        result.status = procError.status as ProcessedDocumentStatus;
        result.error = {
          type: procError.type,
          message: procError.message,
          technicalMessage: procError.technicalMessage,
        };
        processingResults.push(result);
      }
    }

    // Calculate counts from actual results instead of using separate counters
    const successCount = processingResults.filter((r) => r.status === 'success').length;
    const duplicateCount = processingResults.filter((r) => r.status === 'duplicate').length;
    const skippedCount = processingResults.filter((r) => r.status === 'skipped').length;
    const errorCount = processingResults.filter((r) => r.status === 'error').length;

    // Total count of any kind of processing failure (errors + skipped + duplicates)
    const totalFailureCount = processingResults.filter((r) => r.status !== 'success').length;

    this.sendSummarySlackNotification(
      processingResults,
      payload,
      { successCount, errorCount: totalFailureCount },
      categoryCounts,
      slackUserId
    );
    logger.info(
      `[VehicleDocService] Batch processing finished for user ${userName}. Success: ${successCount}, Errors: ${errorCount}, Skipped: ${skippedCount}, Duplicates: ${duplicateCount}, Total Failures: ${totalFailureCount}`
    );
  }

  /* eslint-disable max-params */
  private async sendSummarySlackNotification(
    results: ProcessedDocumentResult[],
    payload: VehicleDocumentBatchPayload,
    counts: { successCount: number; errorCount: number },
    categoryCounts: Record<string, number>,
    slackUserId?: string
  ): Promise<void> {
    /* eslint-enable max-params */
    try {
      const { successCount, errorCount } = counts;
      const totalFiles = results.length;
      const userNameForSlack = slackUserId ? `<@${slackUserId}>` : payload.userName;

      let summaryMessage = `Resumen de carga masiva de documentos vehiculares para ${userNameForSlack}:\n`;
      summaryMessage += `*Total de archivos procesados*: ${totalFiles}\n`;
      summaryMessage += `*Por categoría*:\n`;
      for (const [category, count] of Object.entries(categoryCounts)) {
        summaryMessage += `  - ${category}: ${count}\n`;
      }
      summaryMessage += `*Procesados con éxito*: ${successCount}\n`;
      summaryMessage += `*Errores*: ${errorCount}\n`;

      const filesWithErrors = results.filter(
        (r) => r.status === 'error' || r.status === 'skipped' || r.status === 'duplicate'
      );
      let errorDetailsCSV: string | undefined = undefined;

      if (filesWithErrors.length > 0) {
        summaryMessage += `\n*Detalles de archivos con problemas*:`;
        const csvRows = [
          [
            'Nombre de archivo',
            'Mensaje de error',
            'Estado',
            'VIN (intentado)',
            'Vehículo (intentado)',
            'Tipo de error',
          ],
        ];
        filesWithErrors.forEach((r) => {
          csvRows.push([
            r.originalFileName,
            r.error?.message || 'N/A',
            r.status,
            r.vin || 'N/A',
            r.vehicleCarNumber || 'N/A',
            r.error?.type || 'N/A',
          ]);
        });
        const errorTypeCounts: Record<string, number> = {};
        filesWithErrors.forEach((r) => {
          const type = r.error?.type || DocumentProcessingErrorType.PROCESSING_ERROR;
          errorTypeCounts[type] = (errorTypeCounts[type] || 0) + 1;
        });
        if (Object.keys(errorTypeCounts).length > 0) {
          summaryMessage += `\n*Por tipo de error*:`;
          for (const [type, count] of Object.entries(errorTypeCounts)) {
            summaryMessage += `\n  - ${type}: ${count}`;
          }
        }
        errorDetailsCSV = csvRows
          .map((row) => row.map((cell) => `"${(cell || '').replace(/"/g, '""')}"`).join(','))
          .join('\n');
      }

      if (summaryMessage.length > 3000) {
        summaryMessage = summaryMessage.substring(0, 3000 - 3) + '...';
      }

      const headerText =
        errorCount > 0
          ? vehicleDocumentsSlackTexts.processCompleteWithFailure
          : vehicleDocumentsSlackTexts.processComplete;

      await slackChannelNotifier({
        message: createSlackSummaryNotification({
          headerText: headerText,
          summaryTitle: vehicleDocumentsSlackTexts.summaryTitle(userNameForSlack),
          summaryMessage: summaryMessage,
          hasErrors: errorCount > 0,
          bottomText:
            errorCount > 0
              ? 'Revise los errores en el CSV adjunto (si lo hay) o los registros del sistema.'
              : 'Todos los documentos se procesaron correctamente.',
        }),
        file: errorDetailsCSV
          ? {
              content: errorDetailsCSV,
              filename: `vehicle_doc_upload_errors_${new Date().toISOString().split('T')[0]}.csv`,
              title: 'Vehicle Document Upload Errors',
              filetype: 'csv',
            }
          : undefined,
        BotToken: SLACK_NOTIFIER_BOT_TOKEN,
        ChannelId: payload.slackChannelId,
      });
    } catch (slackError) {
      logger.error('[VehicleDocService] CRITICAL: Failed to send summary Slack notification:', slackError);
    }
  }

  private buildOcrPrompt(): string {
    return 'Extract the requested information, including specific authenticity marker checks, and return ONLY the data values as a clean JSON object without any extra explanation.';
  }

  // New: Build OCR schema with embedded authenticity markers
  private buildOcrSchemaWithEmbeddedMarkers(documentCategory: DocumentCategory): Schema {
    const commonProperties: Record<string, Schema> = {
      vin: {
        type: SchemaType.STRING,
        description:
          'Vehicle Identification Number: 17-character uppercase alphanumeric code (may be labeled VIN, NIV, SERIE or serial number). Example: LSGEN530XPD009568',
      },
    };
    const commonRequired = ['vin'];

    const platesProperties: Record<string, Schema> = {
      plates: {
        type: SchemaType.STRING,
        description:
          'License plate number: uppercase letters and numbers only, no special characters. Example: ABC1234',
      },
    };
    const platesRequired = ['plates'];

    const generalIsAuthenticProperty: Schema = {
      type: SchemaType.BOOLEAN,
      description:
        "After checking specific textual/structural markers below, does this document, in its entirety (layout, completeness, typical content fields, and overall appearance), coherently represent a [DOCUMENT_TYPE_PLACEHOLDER]? Consider if it's a complete and coherent example, not a fragment or misleading. Return true if you confirm its overall genuineness for this category, assuming specific markers were also found as requested.",
    };

    let specificMarkerProperties: Record<string, Schema> = {};
    let specificMarkerRequired: string[] = [];
    let documentTypeForPrompt = '';

    switch (documentCategory) {
      case DocumentCategory.INSURANCE_POLICY:
        documentTypeForPrompt = 'automobile insurance policy';
        specificMarkerProperties = {
          hasPolicyTitleMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document contain a prominent title like 'PÓLIZA DE SEGURO' or 'PÓLIZA DE SEGURO DE AUTOMÓVILES' or similar?",
          },
        };
        specificMarkerRequired = ['hasPolicyTitleMarker'];
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...commonProperties,
            ...specificMarkerProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
            policyNumber: {
              type: SchemaType.NUMBER,
              description: 'Insurance policy number. Example: 3190766257',
            },
            insurer: {
              type: SchemaType.STRING,
              description: 'Name of the insurance company. Example: Qualitas',
            },
            issueDate: {
              type: SchemaType.STRING,
              description: 'Policy issue date in YYYY-MM-DD format. Example: 2024-01-15',
            },
            validity: {
              type: SchemaType.STRING,
              description: 'Policy expiry date in YYYY-MM-DD format. Example: 2024-12-31',
            },
            broker: {
              type: SchemaType.STRING,
              description: 'Insurance broker name, if present. Example: Lockton Mexico',
            },
          },
          required: [
            ...commonRequired,
            ...specificMarkerRequired,
            'isAuthentic',
            'policyNumber',
            'insurer',
            'issueDate',
            'broker',
            'validity',
          ],
        };
      case DocumentCategory.TENENCIA:
        documentTypeForPrompt =
          'vehicle control/rights payment document (e.g., Tenencia, Derechos Vehiculares)';
        specificMarkerProperties = {
          hasStateFiscalAuthorityMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document clearly identify a state fiscal authority as the issuer or recipient? (e.g., 'ADMINISTRACIÓN FISCAL GENERAL', 'SECRETARIA DE FINANZAS', 'INSTITUTO DE CONTROL VEHICULAR', 'AGENCIA DE ADMINISTRACIÓN FISCAL', 'GOBIERNO DEL ESTADO DE [State Name]'). This indicates an official state government financial document.",
          },
          hasVehicleControlPaymentMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document contain explicit keywords related to vehicle control payments or rights, such as 'CONTROL VEHICULAR', 'DERECHOS DE CONTROL VEHICULAR', 'DERECHOS VEHICULARES', or similar phrases indicating payment for vehicle-related state obligations? This is often found in the document title or main purpose description.",
          },
          hasOfficialPaymentReceiptMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document appear to be an official payment receipt or payment form, indicated by terms like 'RECIBO OFICIAL', 'FORMATO ÚNICO DE PAGO', 'RECIBO DE PAGO', 'COMPROBANTE DE PAGO', or the presence of clear payment details (TOTAL A PAGAR, IMPORTE) and a FOLIO or REFERENCIA?",
          },
        };

        specificMarkerRequired = [
          'hasStateFiscalAuthorityMarker',
          'hasVehicleControlPaymentMarker',
          'hasOfficialPaymentReceiptMarker',
        ];
        return {
          type: SchemaType.OBJECT,
          properties: {
            // Make VIN optional for Tenencia by not including it in required fields
            vin: {
              type: SchemaType.STRING,
              description:
                'Vehicle Identification Number: 17-character uppercase alphanumeric code (may be labeled VIN, NIV, SERIE or serial number). Example: LSGEN530XPD009568. Optional for Tenencia documents.',
            },
            plates: {
              type: SchemaType.STRING,
              description:
                'License plate number: uppercase letters and numbers only, no special characters. May be found in fields labeled REFERENCIA, PLACAS, or similar. Example: ABC1234.',
            },
            ...specificMarkerProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
            payment: { type: SchemaType.NUMBER, description: 'Total payment amount. Example: 1234.56' },
            validity: {
              type: SchemaType.STRING,
              description:
                'Tax validity expiry date (e.g., VIGENCIA HASTA, Válido hasta el) in YYYY-MM-DD format. If only the year is mentioned, assume YYYY-12-31. If the document refers to payment for a specific year (e.g., "Periodo 2025"), the validity is for that entire year. Example: 2025-12-31',
            },
            paymentDate: {
              type: SchemaType.STRING,
              description:
                'Date the payment was made, if available, in YYYY-MM-DD format. Example: 2025-03-25',
            },
          },
          required: [...specificMarkerRequired, 'isAuthentic', 'payment', 'validity'],
        };
      case DocumentCategory.CIRCULATION_CARD_FRONT:
        documentTypeForPrompt = 'front side of a vehicle registration card (Tarjeta de Circulación)';
        specificMarkerProperties = {
          hasCardTitleMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document have the prominent title 'TARJETA DE CIRCULACIÓN' or similar that indicates this is a circulation card document?",
          },
        };
        specificMarkerRequired = ['hasCardTitleMarker'];
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...commonProperties,
            ...specificMarkerProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
            number: {
              type: SchemaType.STRING,
              description: 'Circulation card number (No. Tarjeta). Example: 41657053172',
            },
            issueDate: {
              type: SchemaType.STRING,
              description: 'Circulation card issue date in YYYY-MM-DD format. Example: 2024-06-30',
            },
            validity: {
              type: SchemaType.STRING,
              description:
                "Circulation card validity (derived from 'Fecha de Expedición' and 'Vigencia') in YYYY-MM-DD format. Example: 2026-06-30",
            },
          },
          required: [...commonRequired, ...specificMarkerRequired, 'isAuthentic', 'number', 'issueDate'],
        };
      case DocumentCategory.CIRCULATION_CARD_BACK:
        return {
          type: SchemaType.OBJECT,
          properties: {
            serialNumber: {
              type: SchemaType.STRING,
              description: 'Folio serial number (often labeled CODIFICACIONES). Example: A4716680',
            },
          },
          required: ['serialNumber'],
        };
      case DocumentCategory.PLATES_ALTA_PLACAS:
        documentTypeForPrompt = 'vehicle registration form for new plates (Alta de Placas)';
        specificMarkerProperties = {
          hasMovilidadMarkerPlates: {
            type: SchemaType.BOOLEAN,
            description: "Does it mention 'SECRETARÍA DE MOVILIDAD'?",
          },
          hasVehicleDataSection: {
            type: SchemaType.BOOLEAN,
            description: "Is there a clear section for 'DATOS DEL VEHÍCULO' or similar?",
          },
        };
        specificMarkerRequired = ['hasMovilidadMarkerPlates', 'hasVehicleDataSection'];
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...commonProperties,
            ...platesProperties,
            ...specificMarkerProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
          },
          required: [...commonRequired, ...platesRequired, ...specificMarkerRequired, 'isAuthentic'],
        };
      case DocumentCategory.PLATES_FRONT:
      case DocumentCategory.PLATES_BACK:
        documentTypeForPrompt = 'clear photograph of a vehicle license plate';
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...platesProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
          },
          required: [...platesRequired, 'isAuthentic'],
        };
      case DocumentCategory.FACTURE:
        documentTypeForPrompt = 'vehicle purchase invoice (Factura or CFDI)';
        specificMarkerProperties = {
          hasRFCMarker: {
            type: SchemaType.BOOLEAN,
            description: "Are there clear instances of 'RFC' (for issuer and receiver)?",
          },
          hasFacturaTitleMarker: {
            type: SchemaType.BOOLEAN,
            description:
              "Does the document clearly state 'FACTURA' or indicate it's a 'CFDI' (e.g., via UUID or QR code presence)?",
          },
          hasTotalAmountMarker: {
            type: SchemaType.BOOLEAN,
            description: "Is there a clear 'TOTAL' amount specified?",
          },
        };
        specificMarkerRequired = ['hasRFCMarker', 'hasFacturaTitleMarker', 'hasTotalAmountMarker'];
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...commonProperties,
            ...specificMarkerProperties,
            isAuthentic: {
              ...generalIsAuthenticProperty,
              description: generalIsAuthenticProperty.description!.replace(
                /\[DOCUMENT_TYPE_PLACEHOLDER\]/g,
                documentTypeForPrompt
              ),
            },
            billNumber: {
              type: SchemaType.STRING,
              description:
                'Invoice number often dentoed by "SERIE Y FOLIO FACTURA" or similar identifier. Example: AV2526',
            },
            billDate: {
              type: SchemaType.STRING,
              description: 'Invoice issuance date in YYYY-MM-DD format. Example: 2025-05-10',
            },
            billAmount: { type: SchemaType.NUMBER, description: 'Invoice total amount. Example: 1500.75' },
          },
          required: [
            ...commonRequired,
            ...specificMarkerRequired,
            'isAuthentic',
            'billNumber',
            'billDate',
            'billAmount',
          ],
        };
      default:
        return {
          type: SchemaType.OBJECT,
          properties: {
            ...commonProperties,
            isAuthentic: {
              type: SchemaType.BOOLEAN,
              description: "Is this document what it's supposed to be for its category?",
            },
          },
          required: [...commonRequired, 'isAuthentic'],
        };
    }
  }

  private getFolioPageUrl(serialNumber: string): string {
    return `https://www.semovi.cdmx.gob.mx/controlvehicular/tarjetacirculacion/validar.php?folio=${serialNumber}`;
  }

  private async fetchVinFromService(serialNumber: string): Promise<VinFetchResult> {
    const folioPageUrl = this.getFolioPageUrl(serialNumber);
    let vinFromHtml: string | null = null;
    let response;
    let hasTimeoutError = false;
    let hasNetworkError = false;
    let hasSuccessfulResponse = false;

    const baseHeaders = {
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      Accept: '*/*',
      'Accept-Encoding': 'gzip, deflate, zstd',
      Connection: 'keep-alive',
    };

    const commonAxiosConfigOptions = {
      timeout: 10000,
      responseType: 'text' as 'text',
      validateStatus: (status: number) => status < 600,
      maxRedirects: 5,
    };

    const directAxiosConfig: AxiosRequestConfig = {
      ...commonAxiosConfigOptions,
      headers: baseHeaders,
    };

    try {
      logger.info(`[VehicleDocService] Attempting direct fetch for VIN. URL: ${folioPageUrl}`);
      response = await axios.get(folioPageUrl, directAxiosConfig);

      if (response.status >= 200 && response.status < 300 && response.data) {
        hasSuccessfulResponse = true;
        vinFromHtml = parseVinFromHtml(response.data);
        if (vinFromHtml) {
          logger.info(
            `[VehicleDocService] Successfully fetched and parsed VIN (direct). VIN: ${vinFromHtml}`
          );
          return {
            vin: vinFromHtml,
            success: true,
          };
        } else {
          logger.warn(
            `[VehicleDocService] Could not parse VIN from HTML (direct, 2xx response). URL: ${folioPageUrl}. Retrying with proxy.`
          );
        }
      } else {
        logger.warn(
          `[VehicleDocService] Direct fetch returned non-2xx status: ${response.status}. URL: ${folioPageUrl}. Retrying with proxy.`
        );
      }
    } catch (directError: any) {
      // Track the type of error for better error handling
      if (axios.isAxiosError(directError)) {
        const err = directError as AxiosError;
        if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
          hasTimeoutError = true;
        } else if (err.request && !err.response) {
          hasNetworkError = true;
        }
      }

      logger.warn(
        `[VehicleDocService] Direct fetch failed (e.g., timeout, network error). URL: ${folioPageUrl}. Error: ${
          directError.message
        }. Retrying with proxy.`
      );
      if (axios.isAxiosError(directError)) {
        const err = directError as AxiosError;
        if (err.response) {
          logger.warn(`[VehicleDocService] Direct Axios error details - Status: ${err.response.status}`);
        } else if (err.request) {
          logger.warn(`[VehicleDocService] Direct Axios error details - No response. Code: ${err.code}`);
        }
      }
    }

    if (vinFromHtml === null) {
      logger.info(
        `[VehicleDocService] Proceeding to proxy fetch for URL: ${folioPageUrl} as direct attempt was unsuccessful.`
      );

      const proxyHost = '***************';
      const proxyPort = 443;
      const proxyConnectUrl = `https://${proxyHost}:${proxyPort}`;

      const agentOptions: HttpsProxyAgentOptions<string> = {
        keepAlive: false,
        rejectUnauthorized: false,
      };
      const httpsAgentForTunneling = new HttpsProxyAgent(proxyConnectUrl, agentOptions);

      const proxyAxiosConfig: AxiosRequestConfig = {
        ...commonAxiosConfigOptions,
        headers: {
          ...baseHeaders,
          'Proxy-Connection': 'Keep-Alive',
        },
        httpsAgent: httpsAgentForTunneling,
      };

      try {
        logger.info(
          `[VehicleDocService] Attempting fetch with proxy. URL: ${folioPageUrl}, Proxy: ${proxyConnectUrl}`
        );
        response = await axios.get(folioPageUrl, proxyAxiosConfig);

        if (response.status >= 200 && response.status < 300 && response.data) {
          hasSuccessfulResponse = true;
          vinFromHtml = parseVinFromHtml(response.data);
          if (vinFromHtml) {
            logger.info(
              `[VehicleDocService] Successfully fetched and parsed VIN (proxy). VIN: ${vinFromHtml}`
            );
          } else {
            logger.warn(
              `[VehicleDocService] Could not parse VIN from HTML (proxy, 2xx response). URL: ${folioPageUrl}`
            );
          }
        } else {
          logger.warn(
            `[VehicleDocService] Proxy fetch returned non-2xx status: ${response.status}. URL: ${folioPageUrl}`
          );
        }
      } catch (proxyError: any) {
        // Track the type of error for better error handling
        if (axios.isAxiosError(proxyError)) {
          const err = proxyError as AxiosError;
          if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
            hasTimeoutError = true;
          } else if (err.request && !err.response) {
            hasNetworkError = true;
          }
        }

        logger.error(
          `[VehicleDocService] Proxy fetch failed. URL: ${folioPageUrl}, Proxy: ${proxyConnectUrl}. Error: ${proxyError.message}`
        );
        if (axios.isAxiosError(proxyError)) {
          const err = proxyError as AxiosError;
          if (err.response) {
            logger.error(
              `[VehicleDocService] Proxy Axios error details - Status: ${
                err.response.status
              }, Data (first 100 chars): ${String(err.response.data).substring(0, 100)}`
            );
          } else if (err.request) {
            logger.error(`[VehicleDocService] Proxy Axios error details - No response. Code: ${err.code}`);
          }
        }
      }
    }

    // Determine the appropriate error type based on what happened
    if (vinFromHtml === null) {
      if (hasSuccessfulResponse) {
        // Service responded successfully but VIN could not be extracted
        // This likely means the serial number is invalid or VIN is not available
        logger.warn(`[VehicleDocService] Service responded but VIN not found for serial: ${serialNumber}`);
        return {
          vin: null,
          success: false,
          errorType: 'VIN_NOT_FOUND',
          errorMessage: `VIN could not be extracted from government website response for serial ${serialNumber}`,
        };
      } else if (hasTimeoutError || hasNetworkError) {
        // Specific network/timeout issues indicate service problems
        logger.error(
          `[VehicleDocService] Government service appears to be down (timeout/network error) for serial: ${serialNumber}`
        );
        return {
          vin: null,
          success: false,
          errorType: 'SERVICE_DOWN',
          errorMessage:
            'Government circulation verification website is not responding due to timeout or network issues',
        };
      } else {
        // Other errors (HTTP errors, etc.) also indicate service issues
        logger.error(
          `[VehicleDocService] Government service appears to be down (HTTP errors) for serial: ${serialNumber}`
        );
        return {
          vin: null,
          success: false,
          errorType: 'SERVICE_DOWN',
          errorMessage: 'Government circulation verification website returned error responses',
        };
      }
    }

    return {
      vin: vinFromHtml,
      success: true,
    };
  }

  private async handleCirculationCardBackOcrData(llmOcrData: any): Promise<any> {
    const serialNumber = llmOcrData.serialNumber;
    if (!serialNumber) {
      logger.warn('[VehicleDocService] Serial number not found in OCR result for CIRCULATION_CARD_BACK');
      throw new Error('Serial number not found in OCR result for CIRCULATION_CARD_BACK.');
    }

    const vinFetchResult = await this.fetchVinFromService(serialNumber);

    if (vinFetchResult.success && vinFetchResult.vin) {
      llmOcrData.vin = vinFetchResult.vin;
      logger.info(
        `[VehicleDocService] Successfully fetched VIN for serial ${serialNumber}: ${vinFetchResult.vin}`
      );
    } else {
      // Handle different error scenarios with appropriate client-facing messages
      if (vinFetchResult.errorType === 'SERVICE_DOWN') {
        logger.error(
          `[VehicleDocService] Government service down for serial ${serialNumber}: ${vinFetchResult.errorMessage}`
        );
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.CIRCULATION_SERVICE_DOWN,
          message:
            'El sitio web de verificación de tarjeta de circulación no está disponible. Por favor, inténtelo de nuevo más tarde.',
          technicalMessage: `Government circulation verification website is down: ${vinFetchResult.errorMessage}`,
        });
      } else if (vinFetchResult.errorType === 'VIN_NOT_FOUND') {
        logger.error(
          `[VehicleDocService] VIN not found for serial ${serialNumber}: ${vinFetchResult.errorMessage}`
        );
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.VIN_NOT_FOUND,
          message:
            'No se pudo encontrar el VIN en el sitio web gubernamental. Verifique que el número de serie sea correcto.',
          technicalMessage: `VIN not found on government website for serial ${serialNumber}: ${vinFetchResult.errorMessage}`,
        });
      } else {
        // Unknown error - generic fallback
        logger.error(
          `[VehicleDocService] Unknown error fetching VIN for serial ${serialNumber}: ${vinFetchResult.errorMessage}`
        );
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.CIRCULATION_SERVICE_DOWN,
          message:
            'No es posible verificar la tarjeta de circulación en este momento debido a que el sitio web del gobierno no está respondiendo.',
          technicalMessage: `Unknown error fetching VIN from government service: ${vinFetchResult.errorMessage}`,
        });
      }
    }

    return llmOcrData;
  }

  // New helper method to verify embedded markers
  private verifyEmbeddedAuthenticityMarkers(
    ocrResult: any,
    documentCategory: DocumentCategory,
    schema: Schema
  ): void {
    const objectSchema = schema as ObjectSchema;
    const properties = objectSchema.properties as Record<string, Schema>;
    const missingProgrammaticMarkers: string[] = [];

    for (const key in properties) {
      if (
        key.startsWith('has') &&
        key.endsWith('Marker') &&
        objectSchema.required &&
        (objectSchema.required as string[]).includes(key)
      ) {
        if (ocrResult[key] === undefined) {
          missingProgrammaticMarkers.push(`${key} (not returned by LLM)`);
        } else if (ocrResult[key] === false) {
          missingProgrammaticMarkers.push(`${key} (reported as not found by LLM)`);
        }
      }
    }

    if (missingProgrammaticMarkers.length > 0) {
      const errorMessage = `El documento no pasó la verificación de marcadores de autenticidad incrustados. Marcadores clave no confirmados por LLM: ${missingProgrammaticMarkers.join('; ')}.`;
      logger.error(`[VehicleDocService] ${ocrResult.originalFileName || 'Document'}: ${errorMessage}`);
      throw new DocumentProcessingError({
        type: DocumentProcessingErrorType.INVALID_DOCUMENT,
        message: 'El documento cargado no es válido',
        technicalMessage: errorMessage,
      });
    }
    logger.info(
      `[VehicleDocService] ${ocrResult.originalFileName || 'Document'} passed embedded authenticity marker verification for ${documentCategory}.`
    );
  }

  // Non-throwing version of verifyEmbeddedAuthenticityMarkers
  private verifyEmbeddedAuthenticityMarkersNonThrowing(
    ocrResult: any,
    documentCategory: DocumentCategory,
    schema: Schema
  ): string[] {
    const objectSchema = schema as ObjectSchema;
    const properties = objectSchema.properties as Record<string, Schema>;
    const missingProgrammaticMarkers: string[] = [];

    for (const key in properties) {
      if (
        key.startsWith('has') &&
        key.endsWith('Marker') &&
        objectSchema.required &&
        (objectSchema.required as string[]).includes(key)
      ) {
        if (ocrResult[key] === undefined) {
          missingProgrammaticMarkers.push(`${key} (not returned by LLM)`);
        } else if (ocrResult[key] === false) {
          missingProgrammaticMarkers.push(`${key} (reported as not found by LLM)`);
        }
      }
    }

    if (missingProgrammaticMarkers.length > 0) {
      const errorMessage = `El documento no pasó la verificación de marcadores de autenticidad incrustados. Marcadores clave no confirmados por LLM: ${missingProgrammaticMarkers.join(
        '; '
      )}.`;
      logger.warn(
        `[VehicleDocService] ${
          ocrResult.originalFileName || 'Document'
        }: ${errorMessage} (validation bypassed)`
      );
      return [`Marcadores de autenticidad: ${errorMessage}`];
    }

    logger.info(
      `[VehicleDocService] ${ocrResult.originalFileName || 'Document'} passed embedded authenticity marker verification for ${documentCategory}.`
    );
    return [];
  }

  // Helper: validate that OCR result includes all required fields and no placeholder types
  private validateOcrData(ocrResult: any, schema: Schema, documentCategory: DocumentCategory): void {
    const missingFields: string[] = [];
    const placeholderFields: string[] = [];

    const objectSchema = schema as ObjectSchema;
    if (Array.isArray(objectSchema.required)) {
      for (const key of objectSchema.required) {
        if (key.startsWith('has') && key.endsWith('Marker')) {
          continue;
        }

        const val = ocrResult[key];
        const prop = (objectSchema.properties as Record<string, Schema>)[key];
        const expectedType = prop?.type;

        if (!val) {
          missingFields.push(key);
        } else if (prop && typeof expectedType === 'string' && val === expectedType) {
          placeholderFields.push(key);
        }
      }
    }

    // Special validation for TENENCIA: at least one of VIN or plates must be present
    if (documentCategory === DocumentCategory.TENENCIA) {
      const hasVin = ocrResult.vin?.trim();
      const hasPlates = ocrResult.plates?.trim();

      if (!hasVin && !hasPlates) {
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.INVALID_DOCUMENT,
          message: 'El documento debe contener al menos VIN o número de placas para identificar el vehículo',
          technicalMessage: 'Neither VIN nor plates were extracted from TENENCIA document',
        });
      }

      logger.info(
        `[VehicleDocService] TENENCIA validation passed. VIN: ${hasVin ? 'present' : 'absent'}, Plates: ${hasPlates ? 'present' : 'absent'}`
      );
    }

    if (
      objectSchema.required &&
      (objectSchema.required as string[]).includes('isAuthentic') &&
      ocrResult.isAuthentic === false
    ) {
      const detailedMessage =
        'El LLM determinó que el documento no es auténtico en su evaluación de coherencia general.';
      logger.error(`[VehicleDocService] Document authenticity failed: ${detailedMessage}`);
      throw new DocumentProcessingError({
        type: DocumentProcessingErrorType.INVALID_DOCUMENT,
        message: 'El documento cargado no es válido',
        technicalMessage: detailedMessage,
      });
    }

    if (missingFields.length || placeholderFields.length) {
      const allProblemFields = Array.from(new Set([...missingFields, ...placeholderFields]));
      if (allProblemFields.length) {
        const detailedMessage = `Validación de OCR fallida. Problemas con campos de datos: [${allProblemFields.join(', ')}] (faltantes o con valor de tipo placeholder).`;
        logger.error(`[VehicleDocService] Field validation failed: ${detailedMessage}`);
        throw new DocumentProcessingError({
          type: DocumentProcessingErrorType.INVALID_DOCUMENT,
          message: 'El documento cargado no es válido',
          technicalMessage: detailedMessage,
        });
      }
    }

    // Handle validity date validation with special logic for circulation card front
    if (
      objectSchema.required &&
      (objectSchema.required as string[]).includes('validity') &&
      ocrResult.validity
    ) {
      DocumentValidators.processValidityDate(ocrResult, documentCategory, true);
    } else if (ocrResult.validity) {
      // Handle optional validity field (like for circulation card front)
      DocumentValidators.processValidityDate(ocrResult, documentCategory, false);
    }
  }

  // Non-throwing version of validateOcrData
  private validateOcrDataNonThrowing(
    ocrResult: any,
    schema: Schema,
    documentCategory: DocumentCategory
  ): string[] {
    const validationErrors: string[] = [];
    const missingFields: string[] = [];
    const placeholderFields: string[] = [];

    const objectSchema = schema as ObjectSchema;
    if (Array.isArray(objectSchema.required)) {
      for (const key of objectSchema.required) {
        if (key.startsWith('has') && key.endsWith('Marker')) {
          continue;
        }

        const val = ocrResult[key];
        const prop = (objectSchema.properties as Record<string, Schema>)[key];
        const expectedType = prop?.type;

        if (!val) {
          missingFields.push(key);
        } else if (prop && typeof expectedType === 'string' && val === expectedType) {
          placeholderFields.push(key);
        }
      }
    }

    // Special validation for TENENCIA: at least one of VIN or plates must be present
    if (documentCategory === DocumentCategory.TENENCIA) {
      const hasVin = ocrResult.vin?.trim();
      const hasPlates = ocrResult.plates?.trim();

      if (!hasVin && !hasPlates) {
        validationErrors.push(
          'El documento debe contener al menos VIN o número de placas para identificar el vehículo'
        );
      } else {
        logger.info(
          `[VehicleDocService] TENENCIA validation passed. VIN: ${hasVin ? 'present' : 'absent'}, Plates: ${hasPlates ? 'present' : 'absent'}`
        );
      }
    }

    if (
      objectSchema.required &&
      (objectSchema.required as string[]).includes('isAuthentic') &&
      ocrResult.isAuthentic === false
    ) {
      validationErrors.push(
        'El LLM determinó que el documento no es auténtico en su evaluación de coherencia general'
      );
    }

    if (missingFields.length || placeholderFields.length) {
      const allProblemFields = Array.from(new Set([...missingFields, ...placeholderFields]));
      if (allProblemFields.length) {
        validationErrors.push(
          `Problemas con campos de datos: [${allProblemFields.join(', ')}] (faltantes o con valor de tipo placeholder)`
        );
      }
    }

    // Handle validity date validation with special logic for circulation card front
    if (
      objectSchema.required &&
      (objectSchema.required as string[]).includes('validity') &&
      ocrResult.validity
    ) {
      const validityErrors = DocumentValidators.processValidityDateNonThrowing(
        ocrResult,
        documentCategory,
        true
      );
      validationErrors.push(...validityErrors);
    } else if (ocrResult.validity) {
      // Handle optional validity field (like for circulation card front)
      const validityErrors = DocumentValidators.processValidityDateNonThrowing(
        ocrResult,
        documentCategory,
        false
      );
      validationErrors.push(...validityErrors);
    }

    return validationErrors;
  }

  // Modified version of extractOCRData that supports bypassing validations
  private async extractOCRDataWithValidationControl(params: {
    docInfo: VehicleDocumentBatchPayload['documents'][0];
    fileBase64: string;
    country?: string;
    bypassValidations?: boolean;
  }): Promise<{ ocrData: any; validationErrors: string[] }> {
    const { docInfo, fileBase64, country, bypassValidations = false } = params;
    const prompt = this.buildOcrPrompt();
    const schema = this.buildOcrSchemaWithEmbeddedMarkers(docInfo.documentCategory);
    const validationErrors: string[] = [];

    const structuredSource: StructuredSource = {
      data: fileBase64,
      media_type: docInfo.contentType as AllowedMimeType,
      prompt,
      filename: docInfo.originalFileName,
      responseSchema: schema,
      modelChain: ['gemini-2.0-flash', GEMINI_MODEL, GEMINI_BACKOFF_MODEL],
    };
    let llmOcrData = (await parseStructuredTextFromSource(structuredSource)) || {};

    // Normalize all date fields immediately after OCR extraction
    llmOcrData = DocumentValidators.normalizeAllDateFields(llmOcrData, docInfo.documentCategory);

    // Date normalization handles format conversion, so no need for LLM retry on dates

    if (
      country !== CountriesEnum['United States'] &&
      docInfo.documentCategory !== DocumentCategory.CIRCULATION_CARD_BACK
    ) {
      if (bypassValidations) {
        const authErrors = this.verifyEmbeddedAuthenticityMarkersNonThrowing(
          llmOcrData,
          docInfo.documentCategory,
          schema
        );
        validationErrors.push(...authErrors);
      } else {
        this.verifyEmbeddedAuthenticityMarkers(llmOcrData, docInfo.documentCategory, schema);
      }
    }

    if (docInfo.documentCategory === DocumentCategory.CIRCULATION_CARD_BACK) {
      try {
        llmOcrData = await this.handleCirculationCardBackOcrData(llmOcrData);
      } catch (error: any) {
        if (bypassValidations) {
          validationErrors.push(`Error procesando tarjeta de circulación (reverso): ${error.message}`);
          logger.warn(
            `[VehicleDocService] Error in handleCirculationCardBackOcrData (validation bypassed): ${error.message}`
          );
        } else {
          throw error;
        }
      }
    }

    const ocrResult = { ...llmOcrData, originalFileName: docInfo.originalFileName };

    if (country !== CountriesEnum['United States']) {
      if (bypassValidations) {
        const dataErrors = this.validateOcrDataNonThrowing(ocrResult, schema, docInfo.documentCategory);
        validationErrors.push(...dataErrors);
      } else {
        this.validateOcrData(ocrResult, schema, docInfo.documentCategory);
      }
    }

    return { ocrData: ocrResult, validationErrors };
  }

  // Original helper method for extracting OCR data (for backward compatibility with batch processing)
  private async extractOCRData(
    docInfo: VehicleDocumentBatchPayload['documents'][0],
    fileBase64: string,
    country?: string
  ): Promise<any> {
    const { ocrData } = await this.extractOCRDataWithValidationControl({
      docInfo,
      fileBase64,
      country,
      bypassValidations: false, // Always use strict validation for the original method
    });
    return ocrData;
  }

  private assignOcrDataToVehicle(options: {
    vehicle: any;
    documentCategory: DocumentCategory;
    newDocumentId: Types.ObjectId;
    ocrData: any;
    oldDocIdsToMove: Types.ObjectId[];
  }): boolean {
    const { vehicle, documentCategory, newDocumentId, ocrData, oldDocIdsToMove } = options;
    switch (documentCategory) {
      case DocumentCategory.INSURANCE_POLICY:
        vehicle.policiesArray.push({
          _id: new Types.ObjectId(),
          policyDocument: newDocumentId,
          policyNumber: parseInt(ocrData.policyNumber) || 0,
          insurer: ocrData.insurer,
          validity: ocrData.validity,
          broker: ocrData.broker,
        });
        return true;
      case DocumentCategory.TENENCIA:
        vehicle.tenancy.push({
          _id: new Types.ObjectId(),
          payment: parseFloat(ocrData.payment) || 0,
          validity: ocrData.validity,
          tenancyDocument: newDocumentId,
        });
        return true;
      case DocumentCategory.CIRCULATION_CARD_FRONT:
        if (!vehicle.circulationCard) vehicle.circulationCard = { number: '', validity: '', older: [] };
        if (vehicle.circulationCard.frontImg) oldDocIdsToMove.push(vehicle.circulationCard.frontImg!);
        vehicle.circulationCard.frontImg = newDocumentId;
        vehicle.circulationCard.number = ocrData.number || vehicle.circulationCard.number;
        vehicle.circulationCard.validity = ocrData.validity || vehicle.circulationCard.validity;
        // region-specific validity: MER and QRO cards valid for 3 years from issueDate
        if (['mer', 'qro'].includes(vehicle.state) && ocrData.issueDate) {
          const issue = new Date(ocrData.issueDate);
          issue.setFullYear(issue.getFullYear() + 3);
          vehicle.circulationCard.validity = issue.toISOString().split('T')[0];
        }
        return true;
      case DocumentCategory.CIRCULATION_CARD_BACK:
        if (!vehicle.circulationCard) vehicle.circulationCard = { number: '', validity: '', older: [] };
        if (vehicle.circulationCard.backImg) oldDocIdsToMove.push(vehicle.circulationCard.backImg!);
        vehicle.circulationCard.backImg = newDocumentId;

        if (ocrData.vinFromQrPage) {
          vehicle.vin = ocrData.vinFromQrPage;
          logger.info(`[VehicleDocService] Assigning VIN from QR Page to vehicle: ${ocrData.vinFromQrPage}`);
        } else if (ocrData.vin && !vehicle.vin) {
          vehicle.vin = ocrData.vin;
          logger.warn(
            `[VehicleDocService] Assigning VIN from LLM to vehicle (CIRCULATION_CARD_BACK fallback - unexpected): ${ocrData.vin}`
          );
        }
        return true;
      case DocumentCategory.PLATES_ALTA_PLACAS:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.platesDocument) {
          oldDocIdsToMove.push(vehicle.carPlates.platesDocument!);
        }
        vehicle.carPlates.platesDocument = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.PLATES_FRONT:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.frontImg) oldDocIdsToMove.push(vehicle.carPlates.frontImg!);
        vehicle.carPlates.frontImg = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.PLATES_BACK:
        if (!vehicle.carPlates) {
          vehicle.carPlates = {
            plates: '',
            frontImg: undefined,
            backImg: undefined,
            platesDocument: undefined,
          };
        }
        if (vehicle.carPlates.backImg) oldDocIdsToMove.push(vehicle.carPlates.backImg!);
        vehicle.carPlates.backImg = newDocumentId;
        vehicle.carPlates.plates = ocrData.plates || vehicle.carPlates.plates;
        return true;
      case DocumentCategory.FACTURE:
        if (vehicle.bill) oldDocIdsToMove.push(vehicle.bill!);
        vehicle.bill = newDocumentId;
        // if the fields are already present, keep them as is instead of replacing
        vehicle.billNumber = vehicle.billNumber || ocrData.billNumber;
        vehicle.billDate = vehicle.billDate || ocrData.billDate;
        vehicle.billAmount = vehicle.billAmount || parseFloat(ocrData.billAmount);
        return true;
      default:
        throw new Error(`Invalid document category: ${documentCategory}`);
    }
  }

  /**
   * New method: Extract OCR data without saving document
   * Always returns extracted data, even when validation fails
   */
  public async extractOCRDataOnly(params: {
    file: Express.Multer.File;
    documentType: DocumentCategory;
    vehicleId: string;
    country?: string;
    plates?: string;
  }): Promise<any> {
    const { file, documentType, vehicleId, plates, country = CountriesEnum.Mexico } = params;

    let ocrData: any = {};
    let allValidationErrors: string[] = [];

    try {
      // 1. Validate file size
      if (file.size > 5 * 1024 * 1024) {
        allValidationErrors.push('El archivo excede el límite de 5MB.');
      }

      // 2. Get vehicle for validation
      const vehicle = await StockVehicle.findById(vehicleId);
      if (!vehicle) {
        allValidationErrors.push('Vehículo no encontrado.');
      }

      // 3. Convert file to base64
      const fileBase64 = file.buffer.toString('base64');

      // 4. Create document info for OCR processing
      const docInfo = {
        originalFileName: file.originalname,
        s3Key: '', // Not used for extraction-only
        contentType: file.mimetype,
        documentCategory: documentType,
      };

      // 5. Extract OCR data - ALWAYS try to extract data, capture validation errors
      try {
        const { ocrData: extractedData, validationErrors: ocrValidationErrors } =
          await this.extractOCRDataWithValidationControl({
            docInfo,
            fileBase64,
            country,
            bypassValidations: true, // Always bypass to get data
          });

        ocrData = extractedData;
        allValidationErrors.push(...ocrValidationErrors);
      } catch (extractionError: any) {
        // Even if extraction fails, we want to return what we can
        logger.error(
          `[VehicleDocService] OCR extraction failed: ${extractionError.message}`,
          extractionError
        );
        allValidationErrors.push('Error durante la extracción OCR');
      }

      // 6. Validate VIN/plates match (if we have a vehicle and extracted data)
      if (vehicle && ocrData) {
        const validationResult = this.validateExtractedDataMatch(ocrData, vehicle, documentType, plates);

        // Check validation results and add to errors
        if (documentType === DocumentCategory.TENENCIA) {
          // Check for specific vehicle data missing errors first
          if (validationResult.error && validationResult.missingVehicleData) {
            allValidationErrors.push(validationResult.error);
            allValidationErrors.push(`Datos faltantes en vehículo: ${validationResult.missingVehicleData}`);
            allValidationErrors.push(`Valor extraído: ${validationResult.extractedValue}`);
          } else {
            // For TENENCIA, validation passes if either VIN or plates match (or both)
            const hasValidMatch = validationResult.vinMatch === true || validationResult.platesMatch === true;

            if (!hasValidMatch) {
              const errorDetails = [];
              if (validationResult.vinMatch === false) {
                errorDetails.push(
                  `VIN extraído: ${validationResult.extractedValue}, VIN esperado: ${vehicle.vin || 'N/A'}`
                );
              }
              if (validationResult.platesMatch === false) {
                errorDetails.push(
                  `Placas extraídas: ${validationResult.extractedValue}, Placas esperadas: ${
                    vehicle.carPlates?.plates || 'N/A'
                  }`
                );
              }

              allValidationErrors.push(
                'Ni el VIN ni las placas extraídos coinciden con el vehículo especificado'
              );
              allValidationErrors.push(...errorDetails);
            }
          }
        } else if (validationResult.vinMatch === false || validationResult.platesMatch === false) {
          const matchError = `El ${
            validationResult.vinMatch === false ? 'VIN' : 'número de placas'
          } extraído no coincide con el vehículo especificado`;
          const matchDetails = `Extracted: ${validationResult.extractedValue}, Expected: ${validationResult.expectedValue}`;

          allValidationErrors.push(matchError);
          allValidationErrors.push(matchDetails);
        }
      }

      // 7. Return response - always include data, use 400 status for validation errors
      if (allValidationErrors.length > 0) {
        return {
          success: false,
          error: 'El documento no es válido o no coincide con el vehículo',
          validationErrors: this.sanitizeValidationErrorsForClient(allValidationErrors),
          statusCode: 400,
          data: ocrData, // Always include extracted data
        };
      }

      return {
        success: true,
        data: ocrData,
      };
    } catch (error) {
      // Handle unexpected errors
      const procError =
        error instanceof DocumentProcessingError
          ? error
          : new DocumentProcessingError({
              type: DocumentProcessingErrorType.PROCESSING_ERROR,
              message: (error as Error).message || 'Unknown error',
            });

      return {
        success: false,
        error: procError.message,
        validationErrors: this.sanitizeValidationErrorsForClient([
          ...allValidationErrors,
          ...(procError.technicalMessage ? [procError.technicalMessage] : []),
        ]),
        statusCode: 500,
        data: ocrData, // Include whatever data we managed to extract
      };
    }
  }

  /**
   * Validate that extracted VIN/plates match the vehicle
   */
  // eslint-disable-next-line max-params
  private validateExtractedDataMatch(
    ocrData: any,
    vehicle: any,
    documentType: DocumentCategory,
    plates?: string
  ): any {
    // For plates-based documents
    if (documentType === DocumentCategory.PLATES_FRONT || documentType === DocumentCategory.PLATES_BACK) {
      const extractedPlates = ocrData.plates?.trim();

      return {
        platesMatch: extractedPlates === plates,
        expectedValue: plates,
        extractedValue: extractedPlates,
      };
    }

    // For TENENCIA documents - use fallback strategy
    if (documentType === DocumentCategory.TENENCIA) {
      const extractedVin = ocrData.vin?.trim();
      const extractedPlates = ocrData.plates?.trim();
      const vehicleVin = vehicle.vin;
      const vehiclePlates = vehicle.carPlates?.plates;

      // Check VIN match first (preferred)
      if (extractedVin && vehicleVin) {
        const vinMatches = extractedVin === vehicleVin;
        if (vinMatches) {
          return {
            vinMatch: true,
            expectedValue: vehicleVin,
            extractedValue: extractedVin,
          };
        }
      }

      // Fallback to plates match - but ensure vehicle has plates data
      if (extractedPlates) {
        if (!vehiclePlates) {
          // Vehicle doesn't have plates data to match against
          return {
            vinMatch: extractedVin ? false : undefined,
            platesMatch: false,
            expectedValue: vehicleVin || 'N/A',
            extractedValue: extractedPlates,
            missingVehicleData: 'plates',
            error: 'El vehículo no tiene número de placas registrado para comparar con el documento',
          };
        }

        const platesMatch = extractedPlates === vehiclePlates;
        return {
          platesMatch: platesMatch,
          expectedValue: vehiclePlates,
          extractedValue: extractedPlates,
        };
      }

      // If we have extracted VIN but no vehicle VIN to match
      if (extractedVin && !vehicleVin) {
        return {
          vinMatch: false,
          platesMatch: undefined,
          expectedValue: vehiclePlates || 'N/A',
          extractedValue: extractedVin,
          missingVehicleData: 'vin',
          error: 'El vehículo no tiene VIN registrado para comparar con el documento',
        };
      }

      // If we have extracted data but no match
      if (extractedVin || extractedPlates) {
        return {
          vinMatch: extractedVin ? false : undefined,
          platesMatch: extractedPlates ? false : undefined,
          expectedValue: vehicleVin || vehiclePlates,
          extractedValue: extractedVin || extractedPlates,
        };
      }

      // No identifying data extracted
      return {
        vinMatch: false,
        platesMatch: false,
        expectedValue: vehicleVin || vehiclePlates || 'N/A',
        extractedValue: 'N/A',
      };
    }

    // For VIN-based documents
    const extractedVin = ocrData.vin?.trim();
    const vehicleVin = vehicle.vin;

    return {
      vinMatch: extractedVin === vehicleVin,
      expectedValue: vehicleVin,
      extractedValue: extractedVin,
    };
  }
}

export const vehicleDocumentProcessingService = new VehicleDocumentProcessingService();
