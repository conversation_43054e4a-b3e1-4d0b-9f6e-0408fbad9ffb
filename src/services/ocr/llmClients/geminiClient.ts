import { logger } from '@/clean/lib/logger';
import { GEMINI_MAX_OUTPUT_TOKENS, GEMINI_MODEL, GEMINI_BACKOFF_MODEL } from '@/constants';
import { GoogleGenerativeAI, GenerationConfig, Schema } from '@google/generative-ai';
import { anthropicParseMultipleSources, parseImageText } from '@/services/ocr/llmClients/anthropicClient';
import { getMimeTypeFromFilename } from './utils';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// Shared types for OCR LLM clients
export const allowedMimeTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
] as const;
export type AllowedMimeType = (typeof allowedMimeTypes)[number];

// Parts used by Gemini client
export type TextPart = { text: string };
export type InlineDataPart = { inlineData: { mimeType: AllowedMimeType; data: string } };
export type Part = TextPart | InlineDataPart;

// Source for LLM OCR
export type Source = {
  data: string;
  media_type: AllowedMimeType;
  prompt?: string;
  filename?: string;
};

export type StructuredSource = Source & {
  responseSchema: Schema;
  modelChain?: string[];
};

/**
 * Process a single image/source with LLM
 */
export const parseTextFromSource = async (param: Source) => {
  logger.info('[parseTextFromSource][Gemini] Processing document');

  // Determine and validate media type
  let mediaType = param.media_type;
  if (!mediaType && param.filename) {
    mediaType = getMimeTypeFromFilename(param.filename);
    logger.info(`[parseTextFromSource] Detected MIME type ${mediaType}`);
  }
  if (!mediaType || !allowedMimeTypes.includes(mediaType)) {
    throw new Error(`Unsupported MIME type: ${mediaType}`);
  }

  // Sequentially try Gemini models
  const models = [GEMINI_MODEL, GEMINI_BACKOFF_MODEL];
  let lastError: any;
  for (const modelName of models) {
    try {
      logger.info(`[parseTextFromSource][Gemini] trying ${modelName}`);
      const client = genAI.getGenerativeModel({ model: modelName });
      const parts = [{ text: param.prompt || '' }, { inlineData: { mimeType: mediaType, data: param.data } }];
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
      };
      const response = await client.generateContent({
        contents: [{ role: 'user', parts }],
        generationConfig,
      });
      const text = response.response.text();
      if (!text) throw new Error('Empty response from Gemini');
      const start = text.indexOf('{');
      const end = text.lastIndexOf('}');
      if (start < 0 || end < 0) throw new Error('Invalid JSON response from Gemini');
      return JSON.parse(text.slice(start, end + 1));
    } catch (error: any) {
      lastError = error;
      logger.warn(`[parseTextFromSource][Gemini] ${modelName} failed: ${error.message}`);
    }
  }

  // Third-level fallback to Anthropic for images
  try {
    logger.info('[parseTextFromSource] Falling back to Anthropic');
    return await parseImageText({
      data: param.data,
      media_type: mediaType as any,
      prompt: param.prompt || '',
    });
  } catch (error: any) {
    lastError = error;
    logger.warn(`[parseTextFromSource][Anthropic] fallback failed: ${error.message}`);
  }

  logger.error(`[parseTextFromSource][Gemini] All fallbacks failed: ${lastError.message}`);
  throw lastError;
};

export const parseStructuredTextFromSource = async (param: StructuredSource) => {
  logger.info('[parseStructuredTextFromSource][Gemini] - Starting structured parsing');
  // Determine and validate media type for structured parsing
  let mediaType = param.media_type;
  if (!mediaType && param.filename) mediaType = getMimeTypeFromFilename(param.filename);
  if (!mediaType || !allowedMimeTypes.includes(mediaType))
    throw new Error(`Unsupported MIME type: ${mediaType}`);
  // build model sequence: custom or default
  const defaultChain = [GEMINI_MODEL, GEMINI_BACKOFF_MODEL];
  const models = Array.isArray(param.modelChain) && param.modelChain.length ? param.modelChain : defaultChain;
  let lastError: any;
  for (const modelName of models) {
    try {
      logger.info(`[parseStructuredTextFromSource][Gemini] trying ${modelName}`);
      const client = genAI.getGenerativeModel({ model: modelName });
      // build parts
      const parts = [
        { text: param.prompt as string },
        { inlineData: { mimeType: mediaType, data: param.data } },
      ];
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
        responseMimeType: 'application/json',
        responseSchema: param.responseSchema,
      };
      // call Gemini
      const res = await client.generateContent({
        contents: [{ role: 'user', parts }],
        generationConfig,
      });
      const txt = res.response.text();
      if (!txt) throw new Error('Empty response');
      return JSON.parse(txt);
    } catch (err: any) {
      lastError = err;
      logger.warn(`[parseStructuredTextFromSource][Gemini] ${modelName} failed: ${err.message}`);
      // try next
    }
  }
  // Fallback to Anthropic with JSON schema instruction
  try {
    logger.info('[parseStructuredTextFromSource] Falling back to Anthropic');
    const anthropicPrompt = `${param.prompt || ''}\nPlease respond with JSON matching this schema, no extra descriptions with fields, just the vale of the field in the way and format described in description:\n${JSON.stringify(
      param.responseSchema
    )}`;
    return await parseImageText({
      data: param.data,
      media_type: mediaType as any,
      prompt: anthropicPrompt,
    });
  } catch (err: any) {
    lastError = err;
    logger.warn(`[parseStructuredTextFromSource][Anthropic] fallback failed: ${err.message}`);
  }
  logger.error(`[parseStructuredTextFromSource][Gemini] All fallbacks failed: ${lastError.message}`);
  throw lastError;
};

export const parseTextFromMultipleSources = async (sources: Source[], prompt: string) => {
  logger.info('[parseMultipleImages][Gemini] Processing multiple images');
  const models = [GEMINI_MODEL, GEMINI_BACKOFF_MODEL];
  let lastError: any = new Error('No models attempted');
  for (const modelName of models) {
    try {
      logger.info(`[parseMultipleImages][Gemini] trying ${modelName}`);
      const client = genAI.getGenerativeModel({ model: modelName });
      const parts: Part[] = [{ text: prompt }];
      for (const source of sources) {
        let mediaType = source.media_type;
        if (!mediaType && source.filename) mediaType = getMimeTypeFromFilename(source.filename);
        if (!mediaType || !allowedMimeTypes.includes(mediaType))
          throw new Error(`Unsupported MIME type: ${mediaType}`);
        parts.push({ inlineData: { mimeType: mediaType, data: source.data } });
      }
      const generationConfig: GenerationConfig = {
        temperature: 0,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
      };
      const resp = await client.generateContent({ contents: [{ role: 'user', parts }], generationConfig });
      const txt = resp.response.text();
      if (!txt) throw new Error('Empty response from Gemini');
      const start = txt.indexOf('{');
      const end = txt.lastIndexOf('}');
      if (start < 0 || end < 0) throw new Error('Invalid JSON response');
      return JSON.parse(txt.slice(start, end + 1));
    } catch (err: any) {
      lastError = err;
      logger.warn(`[parseMultipleImages][Gemini] ${modelName} failed: ${err.message}`);
    }
  }
  // Fallback to Anthropic for multiple sources
  try {
    logger.info('[parseMultipleImages] Falling back to Anthropic');
    return await anthropicParseMultipleSources(sources, prompt);
  } catch (error: any) {
    lastError = error;
    logger.warn(`[parseMultipleImages][Anthropic] fallback failed: ${error.message}`);
  }

  logger.error(`[parseMultipleImages][Gemini] All fallbacks failed: ${lastError.message}`);
  throw lastError;
};

/**
 * Generates text content based on a prompt using the Gemini API with retry logic.
 * Assumes the response should contain a parsable JSON object.
 * @param prompt The text prompt to send to the model.
 * @param generationConfig Optional generation configuration.
 * @returns The parsed JSON object from the model's response.
 */
export const generateTextContent = async (prompt: string, generationConfig?: Partial<GenerationConfig>) => {
  logger.info('[generateTextContent][Gemini] - Generating text content');

  // Default generation config
  const config: GenerationConfig = {
    temperature: 0,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
    ...generationConfig,
  };

  // Sequentially try Gemini models
  const models = [GEMINI_MODEL, GEMINI_BACKOFF_MODEL];
  let lastError: any;

  for (const modelName of models) {
    try {
      logger.info(`[generateTextContent][Gemini] trying ${modelName}`);
      const client = genAI.getGenerativeModel({ model: modelName });

      const response = await client.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: config,
      });

      const text = response.response.text();
      if (!text) throw new Error('Empty response from Gemini');

      const start = text.indexOf('{');
      const end = text.lastIndexOf('}');
      if (start < 0 || end < 0) throw new Error('Invalid JSON response from Gemini');

      return JSON.parse(text.slice(start, end + 1));
    } catch (error: any) {
      lastError = error;
      logger.warn(`[generateTextContent][Gemini] ${modelName} failed: ${error.message}`);
    }
  }

  logger.error(`[generateTextContent][Gemini] All models failed: ${lastError.message}`);
  throw lastError;
};
