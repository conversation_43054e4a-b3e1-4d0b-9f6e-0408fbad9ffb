import qrcode from 'qrcode';
import { Types } from 'mongoose';
import Document from '../models/documentSchema';
import { uploadFile } from '../aws/s3';
import { logger } from '@/clean/lib/logger';
import QRActionToken, { QRActionTokenType } from '../models/qrActionTokenSchema';
import { PhysicalVehicleStatus } from '../models/StockVehicleSchema';
import crypto from 'crypto';

const FRONTEND_ADMIN_URL = process.env.FRONTEND_ADMIN_URL;

export const generateAndUploadQrCode = async (
  vehicleId: Types.ObjectId,
  carNumber: string | undefined
): Promise<Types.ObjectId | null> => {
  if (!vehicleId || !carNumber) {
    logger.error('[generateAndUploadQrCode] Missing vehicleId or carNumber for QR code generation');
    return null;
  }

  try {
    // Generate a QR code that points to the redirect-info endpoint
    // The frontend can then handle the dynamic redirection based on the response
    const qrCodeUrl = `${FRONTEND_ADMIN_URL}/dashboard/flotilla/vehicle-redirect/${vehicleId.toString()}`;

    logger.info(
      `[generateAndUploadQrCode] Generating QR code for vehicleId ${vehicleId} with URL: ${qrCodeUrl}`
    );

    const qrCodeBuffer = await qrcode.toBuffer(qrCodeUrl, {
      errorCorrectionLevel: 'H', // High - provides best error correction
      type: 'png',
      width: 300,
      margin: 1,
    });

    const qrCodeDirectory = 'vehicle-qrcodes/ocn-admin/';
    const qrCodeFileName = `${carNumber}_qrcode_${vehicleId}.png`;
    const qrCodePath = `${qrCodeDirectory}${qrCodeFileName}`;

    const qrCodeDoc = new Document({
      originalName: qrCodeFileName,
      path: qrCodePath,
      vehicleId: vehicleId,
    });

    const qrCodeFileData: Express.Multer.File = {
      fieldname: 'qrCode',
      originalname: qrCodeFileName,
      encoding: '7bit',
      mimetype: 'image/png',
      buffer: qrCodeBuffer,
      size: qrCodeBuffer.length,
      destination: qrCodeDirectory,
      filename: qrCodeFileName,
      path: qrCodePath,
      stream: null as any, // Stream not needed for buffer upload
    };

    logger.info(`[generateAndUploadQrCode] Uploading QR code to: ${qrCodePath}`);
    await uploadFile(qrCodeFileData, qrCodeFileName, qrCodeDirectory);

    await qrCodeDoc.save();

    logger.info(
      `[generateAndUploadQrCode] QR code successfully generated and saved with document ID: ${qrCodeDoc._id}`
    );
    return qrCodeDoc._id;
  } catch (error) {
    logger.error(`[generateAndUploadQrCode] Error generating QR code for vehicle ${vehicleId}:`, error);
    return null;
  }
};

export async function createQrActionToken({
  vehicleId,
  type,
  intendedNextStatus,
  expiresInMinutes = 5,
}: {
  vehicleId: Types.ObjectId;
  type: QRActionTokenType;
  intendedNextStatus?: PhysicalVehicleStatus;
  expiresInMinutes?: number;
}): Promise<string> {
  const tokenValue = crypto.randomBytes(16).toString('hex');
  const expiresAt = new Date(Date.now() + expiresInMinutes * 60 * 1000);
  const tokenDoc = new QRActionToken({
    token: tokenValue,
    vehicleId,
    type,
    intendedNextStatus,
    expiresAt,
    used: false,
  });
  await tokenDoc.save();
  return tokenValue;
}
