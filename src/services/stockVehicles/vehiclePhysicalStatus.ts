import { PhysicalVehicleStatus } from '@/models/StockVehicleSchema';

export type NextStepOption = { value: PhysicalVehicleStatus; label: string };

export interface QrScanStepResult {
  nextPhysicalStatusToDisplay: PhysicalVehicleStatus | null;
  nextStepOptions: NextStepOption[];
  message: string;
  intendedNextStatusForToken?: PhysicalVehicleStatus;
  dealershipName?: string;
}

export function getQrScanStepResult(
  physicalStatus: PhysicalVehicleStatus,
  brand: string | undefined,
  source: string | undefined
): QrScanStepResult {
  let nextPhysicalStatusToDisplay: PhysicalVehicleStatus | null = null;
  let nextStepOptions: NextStepOption[] = [];
  let message = `Current status: ${physicalStatus}.`;
  let intendedNextStatusForToken: PhysicalVehicleStatus | undefined = undefined;

  const dealershipName = brand || 'Dealership';

  switch (physicalStatus) {
    case PhysicalVehicleStatus.AWAITING_RECEIPT:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Received at OCN Warehouse from ${dealershipName}.`;
      break;
    case PhysicalVehicleStatus.RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP:
      nextStepOptions = [
        {
          value: PhysicalVehicleStatus.AVAILABLE_IN_STOCK,
          label: 'Inspection Completed - Vehicle Available in Stock',
        },
        {
          value: PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED,
          label: 'Inspection Failed - Vehicle to be Repaired',
        },
      ];
      message = `Vehicle is ${physicalStatus}. Please select inspection outcome:`;
      break;
    case PhysicalVehicleStatus.AVAILABLE_IN_STOCK:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.COLLECTED_FROM_STOCK;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Collected from Stock For Delivery to Customer.`;
      break;
    case PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle In Transit to Vendor Workshop.`;
      break;
    case PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Received by Vendor Workshop.`;
      break;
    case PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Under Repair at Vendor Workshop.`;
      break;
    case PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Repair Complete by Vendor.`;
      break;
    case PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR: {
      if (source === 'vendor-panel') {
        nextStepOptions = [
          {
            value: PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER,
            label: PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER,
          },
        ];
        message = `Vehicle is ${physicalStatus}. Please select the next action: collected by customer.`;
      } else {
        nextStepOptions = [
          {
            value: PhysicalVehicleStatus.RETURNED_TO_OCN_AND_REAVAILABLE,
            label: PhysicalVehicleStatus.RETURNED_TO_OCN_AND_REAVAILABLE,
          },
          {
            value: PhysicalVehicleStatus.RETURNED_AND_AVAILABLE_FOR_REDELIVERY,
            label: PhysicalVehicleStatus.RETURNED_AND_AVAILABLE_FOR_REDELIVERY,
          },
        ];
        message = `Vehicle is ${physicalStatus}. Please select the next action after repair return:`;
      }
      break;
    }
    case PhysicalVehicleStatus.COLLECTED_FROM_STOCK:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Delivered to Customer.`;
      break;
    case PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER:
      nextStepOptions = [
        { value: PhysicalVehicleStatus.REPOSSESSION_COMPLETE, label: 'Complete Vehicle Repossession' },
        { value: PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED, label: 'Needs repairing' },
      ];
      message = `Vehicle status is ${physicalStatus}. Please select the outcome for the vehicle.`;
      break;
    case PhysicalVehicleStatus.REPOSSESSION_COMPLETE:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.RETURNED_TO_OCN_WAREHOUSE;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Returned to OCN Warehouse.`;
      break;
    case PhysicalVehicleStatus.RETURNED_TO_OCN_WAREHOUSE:
      nextStepOptions = [
        { value: PhysicalVehicleStatus.AVAILABLE_IN_STOCK, label: 'Vehicle Inspection - Passed' },
        {
          value: PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED,
          label: 'Vehicle Inspection - Failed (Needs Repair)',
        },
      ];
      message = `Vehicle status is ${physicalStatus}. Please select inspection outcome:`;
      break;
    case PhysicalVehicleStatus.RETURNED_TO_OCN_AND_REAVAILABLE:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.COLLECTED_FROM_STOCK;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Collected from Stock For Delivery to Customer.`;
      break;
    case PhysicalVehicleStatus.RETURNED_AND_AVAILABLE_FOR_REDELIVERY:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.COLLECTED_FROM_STOCK_TO_BE_REDELIVERED;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Collected from Stock to be Redelivered to Customer.`;
      break;
    case PhysicalVehicleStatus.COLLECTED_FROM_STOCK_TO_BE_REDELIVERED:
      nextPhysicalStatusToDisplay = PhysicalVehicleStatus.REDELIVERED_TO_CUSTOMER;
      intendedNextStatusForToken = nextPhysicalStatusToDisplay;
      message = `Proposing to change status to: Vehicle Redelivered to Customer.`;
      break;
    case PhysicalVehicleStatus.REDELIVERED_TO_CUSTOMER:
      nextStepOptions = [
        { value: PhysicalVehicleStatus.REPOSSESSION_COMPLETE, label: 'Complete Vehicle Repossession' },
        { value: PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED, label: 'Needs repairing' },
      ];
      message = `Vehicle status is ${physicalStatus}. Please select the outcome for the vehicle.`;
      break;
    case PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER:
      nextStepOptions = [
        { value: PhysicalVehicleStatus.REPOSSESSION_COMPLETE, label: 'Complete Vehicle Repossession' },
        { value: PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED, label: 'Needs repairing' },
      ];
      message = `Vehicle status is ${physicalStatus}. Please select the outcome for the vehicle.`;
      break;
    default:
      message = `Current status: ${physicalStatus}. No specific QR scan action defined for this status.`;
      break;
  }

  return {
    nextPhysicalStatusToDisplay,
    nextStepOptions,
    message,
    intendedNextStatusForToken,
    dealershipName,
  };
}
