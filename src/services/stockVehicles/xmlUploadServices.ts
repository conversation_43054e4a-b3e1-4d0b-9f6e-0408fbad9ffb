import { logger } from '@/clean/lib/logger';
import { Factory } from '@nodecfdi/cfdi-to-json';
import { readFileSync } from 'node:fs';
import { DOMParser } from '@xmldom/xmldom';
import * as fs from 'fs';
import BulkUploadHistory from '@/models/bulkUploadHistorySchema';
import { Types } from 'mongoose';
import StockVehicle, {
  UpdatedVehicleStatus,
  VehicleCategory,
  VehicleSubCategory,
} from '@/models/StockVehicleSchema';
import { getLastContractNumber } from '../getTheLastContractNumber';
import { sendSlackFallbackEmail } from '@/modules/platform_connections/emailFunc';
import {
  mongoErroCodes,
  regionNumbertoCity,
  SLACK_BULK_UPLOAD_CHANNEL_ID,
  SLACK_NOTIFIER_BOT_TOKEN,
  slackTexts,
} from '@/constants';
import { getCurrentDateTime } from '../timestamps';
import Contract from '@/models/contractSchema';
import {
  createSlackErrorNotification,
  createSlackSummaryNotification,
  slackChannelNotifier,
  getSlackUserIdByEmail,
} from '../slackBotNotifier/slackBot';
import { generateAndUploadQrCode } from '../qrCodeService';

export function extractVehicleInfo(jsonData: any) {
  try {
    logger.info('[extractVehicleInfo] Starting vehicle info extraction from XML data');

    const description = jsonData.Conceptos?.Concepto?.[0]?.Descripcion;
    if (!description) {
      logger.error('[extractVehicleInfo] Missing description in XML');
      return {
        error: {
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_DESCRIPTION',
          userMessage: 'No se encontró la descripción del vehículo en el XML',
          affectedField: 'Descripción',
        },
      };
    }

    logger.info(`[extractVehicleInfo] Processing description: ${description}`);

    let brand = null;
    let model = null;
    let version = null;
    let year = null;
    let color = null;
    let vin = null;
    let errors = [];

    try {
      const brandMatch = description.match(/MARCA\s*(\w+)/i);
      if (brandMatch) {
        brand = brandMatch[1];
        logger.info(`[extractVehicleInfo] Extracted brand: ${brand}`);
      } else {
        logger.error('[extractVehicleInfo] Missing brand in XML');
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_BRAND',
          userMessage: 'No se encontró la marca del vehículo en el XML',
          affectedField: 'Marca',
        });
      }

      const modelPatterns = [
        /MODELO\s+(\d{4})\s+(\w+)/i,
        /MODELO\s+(\d{4})\s+([^,]+)/i,
        /MODELO\s+(\d{4})/i,
        /(\d{4})\s+(\w+)/i,
      ];

      let modelFound = false;
      for (const pattern of modelPatterns) {
        const match = description.match(pattern);
        if (match) {
          year = match[1];
          if (match[2]) {
            model = match[2].trim();
          }
          modelFound = true;
          logger.info(`[extractVehicleInfo] Extracted year: ${year}, model: ${model}`);
          break;
        }
      }

      if (!modelFound) {
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_MODEL_YEAR',
          userMessage: 'No se encontró el modelo o año del vehículo en el XML',
          affectedField: 'Modelo/Año',
        });
      }

      const versionPattern = new RegExp(
        `MODELO\\s+${year}\\s+([A-Z0-9\\.\\s\\-]+?)(?=\\s*(?:COLOR|INT\\.|MOTOR|TRANSMISION|NO\\.|\\,|$))`,
        'i'
      );
      const versionMatch = description.match(versionPattern);

      if (versionMatch && versionMatch[1]) {
        version = versionMatch[1].trim();
        logger.info(`[extractVehicleInfo] Extracted version: ${version}`);
      } else {
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_VERSION',
          userMessage: 'No se encontró el versión del vehículo en el XML',
          affectedField: 'Version',
        });
      }

      const colorPatterns = [/COLOR EXTERIOR:\s*([A-Z]+)/i, /5VELCOLOR\s+([A-Z]+)/i, /COLOR\s+([A-Z]+)/i];

      let colorFound = false;
      for (const pattern of colorPatterns) {
        const match = description.match(pattern);
        if (match) {
          color = match[1].trim().toUpperCase();
          colorFound = true;
          logger.info(`[extractVehicleInfo] Extracted color: ${color}`);
          break;
        }
      }

      if (!colorFound) {
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_COLOR',
          userMessage: 'No se encontró el color del vehículo en el XML',
          affectedField: 'Color',
        });
      }

      const vinPatterns = [
        /NO\.\s*SERIE:\s*([A-Z0-9]+)/i,
        /NO\.\s*MOTOR:\s*([A-Z0-9]+)/i,
        /Niv\s*([A-Z0-9]+)/i,
      ];

      let vinFound = false;
      for (const pattern of vinPatterns) {
        const match = description.match(pattern);
        if (match) {
          vin = match[1];
          vinFound = true;
          logger.info(`[extractVehicleInfo] Extracted VIN: ${vin}`);
          break;
        }
      }

      if (!vinFound && jsonData.Conceptos.Concepto[0].ComplementoConcepto?.VentaVehiculos?.Niv) {
        vin = jsonData.Conceptos.Concepto[0].ComplementoConcepto.VentaVehiculos.Niv;
        vinFound = true;
        logger.info(`[extractVehicleInfo] Extracted VIN from complemento: ${vin}`);
      }

      if (!vinFound) {
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_VIN',
          userMessage: 'No se encontró el número VIN del vehículo en el XML',
          affectedField: 'VIN',
        });
      }

      if (!jsonData.Total) {
        logger.error('[extractVehicleInfo] Missing bill amount in XML');
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_BILL_AMOUNT',
          userMessage: 'No se encontró el monto total en el XML',
          affectedField: 'Monto total',
        });
      }

      if (!jsonData.Serie || !jsonData.Folio) {
        logger.error('[extractVehicleInfo] Missing invoice number in XML');
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_INVOICE_NUMBER',
          userMessage: 'No se encontró el número de factura en el XML',
          affectedField: 'Número de factura',
        });
      }

      if (!jsonData.Fecha) {
        logger.error('[extractVehicleInfo] Missing bill date in XML');
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_BILL_DATE',
          userMessage: 'No se encontró la fecha de factura en el XML',
          affectedField: 'Fecha',
        });
      }

      if (!jsonData.Emisor?.Nombre) {
        logger.error('[extractVehicleInfo] Missing supplier name in XML');
        errors.push({
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'MISSING_SUPPLIER',
          userMessage: 'No se encontró el nombre del proveedor en el XML',
          affectedField: 'Proveedor',
        });
      }

      const billAmount = jsonData.Total;
      const invoiceNumber = `${jsonData.Serie} ${jsonData.Folio}`;

      let billDate = null;
      if (jsonData.Fecha.includes('T')) {
        billDate = jsonData.Fecha.split('T')[0];
      } else {
        const date = new Date(jsonData.Fecha);
        if (!isNaN(date.getTime())) {
          billDate = date.toISOString().split('T')[0];
        }
      }

      const supplierName = jsonData.Emisor.Nombre;

      const isElectric = /ELECTRICO/i.test(description);

      const extractedInfo = {
        brand,
        model: model,
        version: version,
        year,
        color,
        vin,
        owner: 'OCN',
        billAmount,
        invoiceNumber,
        billDate,
        supplierName,
        isElectric,
      };

      if (errors.length > 0) {
        return {
          error: errors,
          data: extractedInfo,
        };
      }

      logger.info('[extractVehicleInfo] Successfully extracted vehicle info:', extractedInfo);
      return { data: extractedInfo };
    } catch (extractionError) {
      logger.error('[extractVehicleInfo] Error during specific field extraction:', extractionError);
      return {
        error: {
          fileName: jsonData.fileName || 'Unknown file',
          errorType: 'EXTRACTION_ERROR',
          userMessage: 'Error al extraer información del vehículo del XML',
          affectedField: 'Extracción de datos',
        },
      };
    }
  } catch (error) {
    logger.error('[extractVehicleInfo] Critical error in extractVehicleInfo:', error);
    return {
      error: {
        fileName: jsonData.fileName || 'Unknown file',
        errorType: 'CRITICAL_ERROR',
        userMessage: 'Error crítico al procesar el XML',
        affectedField: 'Procesamiento',
      },
    };
  }
}

export function createReadableErrors(error: any, context: string = '') {
  try {
    logger.info('[createReadableErrors] Creating user-friendly error message', { error, context });

    let errorType = 'UNKNOWN_ERROR';
    let userMessage = 'Ocurrió un error inesperado';
    let technicalMessage = error.message || 'Unknown error';
    let affectedField = '';

    if (error.errorType) {
      // Handle errors from extractVehicleInfo
      errorType = error.errorType;
      userMessage = error.userMessage;
      affectedField = error.affectedField;
      logger.warn('[createReadableErrors] Using error from extractVehicleInfo', { error });
    } else if (error.code === mongoErroCodes.DUPLICATE_KEY) {
      // Duplicate key error
      errorType = 'DUPLICATE_FILE';
      const field = error.keyValue ? Object.keys(error.keyValue)[0] : 'unknown field';
      affectedField = field;

      if (field === 'carNumber') {
        userMessage = `Archivo idéntico cargado previamente - Un vehículo con este número de matrícula ya existe en el sistema`;
      } else if (field === 'vin') {
        userMessage = `Archivo idéntico cargado previamente - Un vehículo con este número VIN ya existe en el sistema`;
      } else {
        userMessage = `Archivo idéntico cargado previamente - Este ${field} ya existe en el sistema`;
      }
      logger.warn('[createReadableErrors] Duplicate key error detected', { field, error });
    } else if (error.name === 'ValidationError') {
      // Mongoose validation error
      errorType = 'VALIDATION_ERROR';
      const field = Object.keys(error.errors)[0];
      affectedField = field;
      userMessage = `Datos inválidos proporcionados para ${field}`;
      logger.warn('[createReadableErrors] Validation error detected', { field, error });
    } else if (error instanceof SyntaxError) {
      errorType = 'PARSE_ERROR';
      userMessage = 'No se pudo leer el archivo XML';
      logger.error('[createReadableErrors] XML parsing error detected', { error });
    }

    if (context) {
      userMessage = `${context}: ${userMessage}`;
    }

    const errorInfo = {
      userMessage,
      technicalMessage,
      errorType,
      affectedField,
    };

    logger.info('[createReadableErrors] Created user-friendly error message', errorInfo);
    return errorInfo;
  } catch (errorHandlingError: any) {
    logger.error('[createReadableErrors] Error in createReadableErrors:', errorHandlingError);
    return {
      userMessage: 'Ocurrió un error mientras procesábamos su solicitud',
      technicalMessage: errorHandlingError.message,
      errorType: 'ERROR_HANDLING_FAILURE',
      affectedField: '',
    };
  }
}

function createErrorCSV(errors: any[]) {
  // Group errors by filename
  const errorsByFile: Record<string, any[]> = {};

  errors.forEach((error) => {
    const fileName = error?.fileName || 'Unknown file';
    if (!errorsByFile[fileName]) {
      errorsByFile[fileName] = [];
    }
    errorsByFile[fileName].push(error);
  });

  const headers = ['No.', 'Nombre del Archivo', 'Mensaje de Error', 'Campo Afectado', 'Tipo de Error'];

  const rows = Object.entries(errorsByFile)?.map(([fileName, fileErrors], index) => {
    // If we only have one error for this file, show it directly
    if (fileErrors.length > 1) {
      const formattedUserMessages = fileErrors
        .map((err, i) => (err?.userMessage ? `${i + 1}. ${err?.userMessage}` : ''))
        .filter(Boolean)
        .join('\n');

      const formattedAffectedFields = fileErrors
        .map((err, i) => (err?.affectedField ? `${i + 1}. ${err?.affectedField}` : ''))
        .filter(Boolean)
        .join('\n');

      const formattedErrorTypes = fileErrors
        .map((err, i) => (err?.errorType ? `${i + 1}. ${err?.errorType}` : ''))
        .filter(Boolean)
        .join('\n');

      return [index + 1, fileName, formattedUserMessages, formattedAffectedFields, formattedErrorTypes];
    } else {
      return [
        index + 1,
        fileName,
        fileErrors[0]?.userMessage || '',
        fileErrors[0]?.affectedField || '',
        fileErrors[0]?.errorType || '',
      ];
    }
  });

  return [headers, ...rows]
    ?.map((row) =>
      row
        ?.map((field) => {
          // Convert numbers to strings
          const stringField = String(field);
          // Escape fields that contain commas, quotes, or newlines
          if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
            // Escape quotes by doubling them and wrap in quotes
            return `"${stringField.replace(/"/g, '""')}"`;
          }
          return stringField;
        })
        ?.join(',')
    )
    ?.join('\n');
}

export async function cleanupFiles(files: Express.Multer.File[]) {
  try {
    for (const file of files) {
      try {
        await fs.promises.unlink(file.path);
        logger.info(`[cleanupFiles] Successfully deleted file: ${file.originalname}`);
      } catch (unlinkError) {
        logger.error(`[cleanupFiles] Error deleting file ${file.originalname}:`, unlinkError);
      }
    }
  } catch (error) {
    logger.error('[cleanupFiles] Error during cleanup:', error);
  }
}

export async function processBulkXMLFiles({
  files,
  country,
  region,
  userId,
  userName,
  initialErrorDetails = [],
  userEmail,
}: {
  files: Express.Multer.File[];
  country: string;
  region: number;
  userId: string;
  userName: string;
  initialErrorDetails?: any[];
  userEmail: string;
}) {
  const factory = new Factory();
  const dataConverter = factory.createConverter();

  const regionNumber = region;
  let successCount = 0;
  let errorCount = 0;
  const errorDetails = [...initialErrorDetails];

  logger.info('[processBulkXMLFiles] Starting bulk XML file processing', {
    fileCount: files.length,
    country,
    region: regionNumber,
    userId,
  });

  const slackUser = await getSlackUserIdByEmail({ email: userEmail, BotToken: SLACK_NOTIFIER_BOT_TOKEN });
  try {
    for (const file of files) {
      try {
        logger.info(`[processBulkXMLFiles] Processing file: ${file.originalname}`);

        try {
          const xml = readFileSync(file.path).toString('utf8');
          logger.info(`[processBulkXMLFiles] Successfully read file: ${file.originalname}`);

          const parser = new DOMParser();
          const xmlDoc = parser.parseFromString(xml, 'text/xml');

          const rootNode = dataConverter.convertXmlDocument(xmlDoc);
          const jsonData = rootNode.toRecord();
          jsonData.fileName = file.originalname;

          const vehicleInfo = extractVehicleInfo(jsonData);

          if (vehicleInfo.error) {
            if (Array.isArray(vehicleInfo.error)) {
              errorDetails.push(...vehicleInfo.error);
            } else {
              errorDetails.push(vehicleInfo.error);
            }
            errorCount++;
            continue;
          }

          if (vehicleInfo.data) {
            const newStockVehicleId = new Types.ObjectId();

            const nextNumber = (await getLastContractNumber(regionNumber)) || 1;
            let carNumber: string;

            if (typeof nextNumber === 'number' && nextNumber.toString().length > 3) {
              carNumber = regionNumber + '0' + nextNumber;
            } else {
              const ceros = '0'.repeat(3 - nextNumber.toString().length);
              carNumber = nextNumber === 0 ? regionNumber + '000' : regionNumber + ceros + nextNumber;
            }

            logger.info(`[processBulkXMLFiles] Generated car number: ${carNumber}`);

            const contract = new Contract({
              region: regionNumber,
              contractNumber: nextNumber,
              alias: Number(carNumber),
              stockVehicleId: newStockVehicleId,
            });

            try {
              const newVehicleValidator = await StockVehicle.findOne({ carNumber });
              if (newVehicleValidator) {
                logger.warn(`[processBulkXMLFiles] Vehicle with car number ${carNumber} already exists`);
                errorCount++;

                const errorInfo = createReadableErrors(
                  { code: mongoErroCodes.DUPLICATE_KEY, keyValue: { carNumber } },
                  `Error in file ${file.originalname}`
                );
                errorDetails.push({
                  fileName: file.originalname,
                  ...errorInfo,
                });

                await BulkUploadHistory.create({
                  fileName: file.originalname,
                  fileData: xml,
                  success: false,
                  failureReason: errorInfo,
                  uploadedBy: new Types.ObjectId(userId),
                  vehicleInfo: {
                    brand: vehicleInfo.data.brand,
                    model: vehicleInfo.data.model,
                    version: vehicleInfo.data.version,
                    year: vehicleInfo.data.year,
                    color: vehicleInfo.data.color,
                    vin: vehicleInfo.data.vin,
                    carNumber,
                  },
                });

                continue;
              }

              const newStockVehicle = new StockVehicle({
                _id: newStockVehicleId,
                carNumber,
                vehicleState: regionNumbertoCity[regionNumber],
                status: 'invoiced',
                vehicleStatus: UpdatedVehicleStatus.inactive,
                category: VehicleCategory['in-preparation'],
                subCategory: VehicleSubCategory.default,
                brand: vehicleInfo.data.brand,
                model: vehicleInfo.data.model,
                version: vehicleInfo.data.version,
                year: vehicleInfo.data.year,
                color: vehicleInfo.data.color,
                vin: vehicleInfo.data.vin,
                owner: vehicleInfo.data.owner,
                billAmount: vehicleInfo.data.billAmount,
                billNumber: vehicleInfo.data.invoiceNumber,
                billDate: vehicleInfo.data.billDate,
                country: country,
                gpsNumber: carNumber,
                gpsSerie: carNumber,
                isElectric: vehicleInfo.data.isElectric,
              });

              const historyUpdate = {
                userId: new Types.ObjectId(userId),
                step: 'VEHICULO CREADO',
                description: 'Vehículo creado a partir de una carga masiva de XML',
                time: getCurrentDateTime(),
              };
              newStockVehicle.updateHistory.push(historyUpdate);

              try {
                await newStockVehicle.save();
                await contract.save();

                // Generate QR code for the vehicle
                try {
                  const qrCodeId = await generateAndUploadQrCode(
                    newStockVehicle._id,
                    newStockVehicle.carNumber
                  );
                  if (qrCodeId) {
                    newStockVehicle.qrCode = qrCodeId;
                    await newStockVehicle.save(); // Save again with QR code ID
                    logger.info(`[processBulkXMLFiles] Generated QR code for vehicle ${carNumber}`);
                  } else {
                    logger.warn(
                      `[processBulkXMLFiles] QR code generation failed for vehicle ${carNumber}, continuing without QR code`
                    );
                  }
                } catch (qrError: any) {
                  logger.error(
                    `[processBulkXMLFiles] Error generating QR code for vehicle ${carNumber}: ${qrError.message}`,
                    qrError
                  );
                  // Continue with the process even if QR generation fails
                }

                await BulkUploadHistory.create({
                  fileName: file.originalname,
                  fileData: xml,
                  success: true,
                  uploadedBy: new Types.ObjectId(userId),
                  vehicleInfo: {
                    brand: vehicleInfo.data.brand,
                    model: vehicleInfo.data.model,
                    version: vehicleInfo.data.version,
                    year: vehicleInfo.data.year,
                    color: vehicleInfo.data.color,
                    vin: vehicleInfo.data.vin,
                    carNumber,
                  },
                });

                logger.info(
                  `[processBulkXMLFiles] Successfully created new vehicle with car number ${carNumber}`
                );
                successCount++;
              } catch (saveError) {
                logger.error(
                  `[processBulkXMLFiles] Error saving vehicle or contract for ${file.originalname}:`,
                  saveError
                );
                errorCount++;

                const errorInfo = createReadableErrors(saveError);
                errorDetails.push({
                  fileName: file.originalname,
                  ...errorInfo,
                });

                await BulkUploadHistory.create({
                  fileName: file.originalname,
                  fileData: xml,
                  success: false,
                  failureReason: errorInfo,
                  uploadedBy: new Types.ObjectId(userId),
                  vehicleInfo: {
                    brand: vehicleInfo.data.brand,
                    model: vehicleInfo.data.model,
                    version: vehicleInfo.data.version,
                    year: vehicleInfo.data.year,
                    color: vehicleInfo.data.color,
                    vin: vehicleInfo.data.vin,
                    carNumber,
                  },
                });
              }
            } catch (dbError) {
              logger.error(`[processBulkXMLFiles] Database error processing ${file.originalname}:`, dbError);
              errorCount++;

              const errorInfo = createReadableErrors(dbError, `Database error in ${file.originalname}`);
              errorDetails.push({
                fileName: file.originalname,
                ...errorInfo,
              });

              await BulkUploadHistory.create({
                fileName: file.originalname,
                fileData: xml,
                success: false,
                failureReason: errorInfo,
                uploadedBy: new Types.ObjectId(userId),
              });
            }
          }
        } catch (fileError) {
          logger.error(
            `[processBulkXMLFiles] Error reading or parsing file ${file.originalname}:`,
            fileError
          );
          errorCount++;

          const errorInfo = createReadableErrors(fileError, `Error processing ${file.originalname}`);
          errorDetails.push({
            fileName: file.originalname,
            ...errorInfo,
            errorType: 'INVALID_XML',
            userMessage: 'Formato de datos no válido',
          });

          await BulkUploadHistory.create({
            fileName: file.originalname,
            fileData: 'File could not be read',
            success: false,
            failureReason: errorInfo,
            uploadedBy: new Types.ObjectId(userId),
          });
        }
      } catch (error) {
        logger.error(`[processBulkXMLFiles] Unexpected error processing file ${file.originalname}:`, error);
        errorCount++;

        const errorInfo = createReadableErrors(error, `Unexpected error in ${file.originalname}`);
        errorDetails.push({
          fileName: file.originalname,
          ...errorInfo,
        });

        await BulkUploadHistory.create({
          fileName: file.originalname,
          fileData: 'Error occurred before file could be processed',
          success: false,
          failureReason: errorInfo,
          uploadedBy: new Types.ObjectId(userId),
        });
      }
    }

    const summaryMessage = slackTexts.summaryMessage(files.length, successCount, errorCount);

    const csvContent = errorDetails.length > 0 ? createErrorCSV(errorDetails) : undefined;
    const currentDate = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const fileName = `errores_xml_${currentDate}.csv`;

    const notificationSuccess = await slackChannelNotifier({
      message: createSlackSummaryNotification({
        headerText: errorCount > 0 ? slackTexts.processCompleteWithFailure : slackTexts.processComplete,
        summaryTitle: slackTexts.summaryTitle(slackUser ? `<@${slackUser}>` : userName),
        summaryMessage,
        hasErrors: errorCount > 0,
        bottomText:
          errorCount > 0
            ? slackTexts.fileErrorBottomText(errorCount)
            : slackTexts.fileSuccessBottomText(successCount, files.length),
      }),
      file: csvContent
        ? {
            content: csvContent,
            filename: fileName,
            title: `Errores de carga XML - ${currentDate}`,
            filetype: 'csv',
          }
        : undefined,
      BotToken: SLACK_NOTIFIER_BOT_TOKEN,
      ChannelId: SLACK_BULK_UPLOAD_CHANNEL_ID,
    });

    if (notificationSuccess) {
      logger.info('[processBulkXMLFiles] Successfully sent summary notification');
    } else {
      await sendSlackFallbackEmail({
        userName: userName,
        message: summaryMessage,
        isError: Boolean(errorCount > 0),
        fileContent: csvContent ? csvContent : undefined,
        fileName: csvContent ? fileName : undefined,
      });
    }
  } catch (error: any) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    logger.error('[processBulkXMLFiles] Critical error in processing:', error);

    const notificationMessage = slackTexts.criticalError(errorMessage);
    const notificationSuccess = await slackChannelNotifier({
      message: createSlackErrorNotification({
        headerText: slackTexts.criticalErrorTitle,
        userName: slackUser ? `<@${slackUser}>` : userName,
        errorMessage: notificationMessage,
        bottomText: slackTexts.criticalErrorBottomText,
      }),
      BotToken: SLACK_NOTIFIER_BOT_TOKEN,
      ChannelId: SLACK_BULK_UPLOAD_CHANNEL_ID,
    });
    if (notificationSuccess) {
      logger.info('[processBulkXMLFiles] Successfully sent summary notification');
    } else {
      await sendSlackFallbackEmail({
        userName: userName,
        message: notificationMessage,
        isError: true,
        fileContent: undefined,
        fileName: undefined,
      });
    }
    throw error;
  } finally {
    await cleanupFiles(files);
  }
}
