export const HILOS_URL = 'https://api.hilos.io/api/channels/whatsapp/template';

export const HILOS_TEMPLATES = {
  newLead: {
    templateId: 'd42aa00d-66a4-466f-a455-de8662cacfc1',
  },
  approved: {
    templateId: '462e3620-35a0-41d7-a0b7-b17b9f1bab7d',
  },
  appointmentScheduler: {
    // templateId: '067a0f1c-3609-7218-8000-779153305210', // previous template
    templateId: '0679d0b5-cd55-7806-8000-abacc06aa34b',
  },

  homeVisitAppointmentScheduled: {
    templateId: '067a2387-b9c5-786f-8000-64a8d0db2cab',
  },

  homeVisitAppointmentReminderOneNightAgo: {
    templateId: '067a22e7-ea9f-7292-8000-782308653f76',
  },

  homeVisitAppointmentReminderAboutFiveMinutes: {
    templateId: '067a22ef-6162-7ec7-8000-c05e0edc7d05',
  },

  homeVisitAppointmentFinish: {
    templateId: '067a0f6d-2d73-760f-8000-30a06101718d',
  },

  otpVerification: {
    templateId: '067a9d5f-897c-7845-8000-3624161c0cdf',
  },

  onboardingSupport: {
    templateId: '067cee3f-298d-7528-8000-405b59225831',
  },

  onboardingSupportEnd: {
    templateId: '067c07b8-d135-7e4c-8000-cbdfc294f206',
  },

  homeVisitAppointmentNoShow: {
    templateId: '067b5563-f473-7f47-8000-c9e2f9287be1',
  },

  homeVisitAppointmentApology: {
    templateId: '067e10a4-58a5-7ef4-8000-25ac200f636a',
  },

  homeVisitAppointmentCancel: {
    templateId: '067d9359-dc0c-747c-8000-8e6b9f8f0930',
  },

  homeVisitApproval: {
    templateId: '067e186e-dddb-74ed-8000-4c941c5f36a5',
  },

  homeVisitRejection: {
    templateId: '',
  },

  locationGatheringMessage: {
    templateId: '067cac94-20b2-720a-8000-1f6dd1f4bab9',
  },

  homeImageUploadMessage: {
    templateId: '06830129-aea2-7f61-8000-256024b7410d',
  },

  earningsAbove5500: {
    templateId: '06870058-5b52-7a63-8000-59e4cae42ea1',
  },

  earningsBelow5500: {
    templateId: '0687023c-d50b-70c8-8000-fb9cd2475e8e',
  },
};

export const HUBSPOT_URL = 'https://api.hubapi.com/crm/v3/objects/contacts';
export const HUBSPOT_DEAL_URL = 'https://api.hubapi.com/crm/v3/objects/deals';
export const HUBSPOT_BATCH_URL = 'https://api.hubapi.com/crm/v3/associations/deal/contact/batch/create';
export const HUBSPOT_DEAL_TOKEN = process.env.HUBSPOT_DEAL_TOKEN;

export const SOCIAL_SCORING_URL = process.env.SOCIAL_SCORING_URL;

export const OCN_USER_EMAIL = process.env.OCN_USER_EMAIL;
