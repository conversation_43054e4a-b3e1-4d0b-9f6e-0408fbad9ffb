import { MAX_PAGINATION_LIMIT, DEFAULT_PAGINATION_LIMIT } from '../constants';

// WHY IS ALL OF THIS EVEN HERE????
export enum AdmissionRequestStatus {
  created = 'created', // The request is been created from the admin panel and we are waiting for the user to connect their gig platforms
  earnings_analysis = 'earnings_analysis', // The user has connected his gig platforms and we can start the earnings analysis
  documents_analysis = 'documents_analysis', // The user has uploaded all the required documents and we perform  the analysis
  // judicial_analysis = 'judicial_analysis',
  risk_analysis = 'risk_analysis', // We have all the risk variables and we can perform the risk analysis
  home_visit = 'home_visit', // We can perform the home visit
  social_analysis = 'social_analysis', // We can perform the social analysis and set the result
  approved = 'approved', // The request has been approved
  rejected = 'rejected', // The request has been rejected
}

export enum AdmissionRequestRejectionReason {
  earnings_analysis = 'Rejected after earnings analysis',
  documents_analysis = 'Rejected after documents analysis',
  judicial_analysis = 'Rejected after judicial analysis',
  risk_analysis = 'Rejected after risk analysis',
  home_visit = 'Rejected after home visit',
  final_evaluation = 'Rejected after final evaluation',
}

export enum PalencaWebhookAction {
  'user.created' = 'user.created',
  'profile.updated' = 'profile.updated',
  'earnings.updated' = 'earnings.updated',
  'login.created' = 'login.created',
  'login.success' = 'login.success',
  'login.error' = 'login.error',
  'login.incomplete' = 'login.incomplete',
}

export enum GigPlatform {
  uber = 'uber',
  didi = 'didi',
  didi_food = 'didi_food',
  cornershop = 'cornershop',
  indriver = 'indriver',
  uber_eats = 'uber_eats',
  rappi = 'rappi',
}

export enum Country {
  mx = 'mx',
}

export enum CurrencyCode {
  mxn = 'mxn',
  usd = 'usd',
}

export enum MediaType {
  proof_of_tax_situation = 'proof_of_tax_situation',
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement = 'bank_statement',
  home_visit_evidence = 'home_visit_evidence',
  curp = 'curp',
}

export enum MediaStatus {
  pending = 'pending', // A document that has been uploaded but not yet processed
  active = 'active', // A document that is currently in use and valid
  deleted = 'deleted', // A document that has been marked for deletion
  archived = 'archived', // A document that is no longer in active use but retained for historical purposes
}

export enum RequestDocumentStatus {
  pending = 'pending',
  approved = 'approved',
  pending_review = 'pending_review',
}

export enum RequestDocumentsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum RequestPersonalDataStepStatus {
  pending = 'pending',
  approved = 'approved',
}

export enum HomeVisitStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum ResidentOwnershipStatus {
  owned = 'owned',
  rented = 'rented',
}

export enum PalencaRetrievalStatus {
  pending = 'pending',
  queued = 'queued',
  success = 'success',
  error = 'error',
}

export enum PalencaAccountStatus {
  success = 'success',
  incomplete = 'incomplete',
  pending = 'pending',
  retry = 'retry',
  error = 'error',
}

export enum EarningsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
  approved_with_conditions = 'approved_with_conditions',
}

export enum AdmissionRequestDocumentType {
  proof_of_tax_situation = 'proof_of_tax_situation',
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement_month_1 = 'bank_statement_month_1',
  bank_statement_month_2 = 'bank_statement_month_2',
  bank_statement_month_3 = 'bank_statement_month_3',
  // bank_statement_month_4 = 'bank_statement_month_4',
  // bank_statement_month_5 = 'bank_statement_month_5',
  // bank_statement_month_6 = 'bank_statement_month_6',
}

// Identifies what kind of entity the action is performed on (e.g., Document, User Profile)
export enum EntityType {
  admission_request = 'admission_request',
}

//  Describes the specific action taken in the log entry.
export enum ActionType {
  'admission_request.created' = 'admission_request.created',
  'home_visit.created' = 'home_visit.created',
  'home_visit.approved' = 'home_visit.approved',
  'home_visit.rejected' = 'home_visit.rejected',
  'admission_request.approved' = 'admission_request.approved',
  'admission_request.rejected' = 'admission_request.rejected',
  'documents_analysis.approved' = 'documents_analysis.approved',
  'documents_analysis.rejected' = 'documents_analysis.rejected',
  'risk_analysis.approved' = 'risk_analysis.approved',
  'risk_analysis.rejected' = 'risk_analysis.rejected',
}

export enum RiskAnalysisStatus {
  pending = 'pending',
  completed = 'completed',
}

export enum RiskCategory {
  low = 'low',
  medium = 'medium',
  high = 'high',
}

export enum ScorecardVariableName {
  age = 'age',
  gig_platforms = 'gig_platforms',
  life_time_completed_trips = 'life_time_completed_trips',
  days_since_first_trip = 'days_since_first_trip',
  percentage_acceptance_rate = 'percentage_acceptance_rate',
  percentage_cancellation_rate = 'percentage_cancellation_rate',
  average_rating = 'average_rating',
  earnings_last_12_weeks = 'earnings_last_12_weeks',
  vehicle_condition = 'vehicle_condition',
  vehicle_type = 'vehicle_type',
  // risk_city = 'risk_city', We needs more definition on how to handle them
}

export enum ScorecardVersion {
  v1 = 'v1',
}

export enum RiskCityList {
  'high_risk_list' = 'high_risk_list',
  'medium_risk_list' = 'medium_risk_list',
  'low_risk_list' = 'low_risk_list',
}

export enum VehicleCondition {
  new = 'new',
  used = 'used',
}

export enum VehicleType {
  car = 'car',
  motorcycle = 'motorcycle',
}

export enum ScorecardVariableCategoryType {
  numeric = 'numeric',
  categorical = 'categorical',
}

export interface TimestampProps {
  createdAt?: Date;
  updatedAt?: Date;
}

export class Document {
  id?: string | undefined;

  fileName: string;

  path: string;

  type: MediaType;

  status: MediaStatus;

  mimeType: string;

  url?: string | null;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Document) {
    this.id = props.id;
    this.fileName = props.fileName;
    this.path = props.path;
    this.type = props.type;
    this.status = props.status;
    this.mimeType = props.mimeType;
    this.url = props.url;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
export class RequestPersonalData {
  status?: RequestPersonalDataStepStatus | null;

  firstName?: string | null;

  lastName?: string | null;

  phone?: string | null;

  email?: string | null;

  birthdate?: string | null;

  taxId?: string | null;

  nationalId?: string | null;

  postalCode?: string | null;

  city?: string | null;

  state?: string | null;

  neighborhood?: string | null;

  street?: string | null;

  streetNumber?: string | null;

  department?: string | null;

  constructor(props: RequestPersonalData) {
    this.status = props.status;
    this.firstName = props.firstName || null;
    this.lastName = props.lastName || null;
    this.phone = props.phone || null;
    this.email = props.email || null;
    this.birthdate = props.birthdate || null;
    this.taxId = props.taxId || null;
    this.nationalId = props.nationalId || null;
    this.postalCode = props.postalCode || null;
    this.city = props.city || null;
    this.state = props.state || null;
    this.neighborhood = props.neighborhood || null;
    this.street = props.street || null;
    this.streetNumber = props.streetNumber || null;
    this.department = props.department || null;
  }
}

export class RequestDocument {
  mediaId?: string | null;

  status: RequestDocumentStatus;

  media?: Document | null;

  type: AdmissionRequestDocumentType;

  constructor(props: RequestDocument) {
    this.mediaId = props.mediaId || null;
    this.status = props.status || RequestDocumentStatus.pending;
    this.media = props.media || null;
    this.type = props.type;
  }
}

export class RequestDocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus;

  documents: RequestDocument[];

  constructor(props: RequestDocumentsAnalysis) {
    this.status = props.status;
    this.documents = props.documents;
  }
}

export class PalencaAccountRetrieval {
  status: PalencaRetrievalStatus;

  constructor(props: PalencaAccountRetrieval) {
    this.status = props.status;
  }
}

export class PalencaAccount {
  accountId: string;

  platform: GigPlatform;

  status: PalencaAccountStatus;

  earnings: PalencaAccountRetrieval;

  metrics: PalencaAccountRetrieval;

  createdAt: Date;

  constructor(props: PalencaAccount) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.status = props.status;
    this.earnings = props.earnings;
    this.metrics = props.metrics;
    this.createdAt = props.createdAt;
  }
}
export class RequestPalenca {
  widgetId: string; // Its needed to know which Palenca widget was used and listen to the correct webhook

  externalId: string; // This is the same as our customerId, the only way we can map a Palenca customer with our customer is through this field

  accounts: PalencaAccount[]; // We keep track of the accounts that the customer has connected through Palenca

  constructor(props: RequestPalenca) {
    this.widgetId = props.widgetId;
    this.externalId = props.externalId;
    this.accounts = props.accounts;
  }
}

export class HomeVisit {
  residentOwnershipStatus: ResidentOwnershipStatus;

  comments: string;

  images: string[];

  media?: Document[] | null;

  status: HomeVisitStatus;

  visitDate: Date;

  constructor(props: HomeVisit) {
    this.residentOwnershipStatus = props.residentOwnershipStatus;
    this.comments = props.comments;
    this.images = props.images;
    this.status = props.status;
    this.visitDate = props.visitDate;
    this.media = props.media || null;
  }
}

export class EarningsAnalysis {
  totalEarnings?: number | null;

  earnings?: WeeklyEarning[] | null;

  platforms?: number | null;

  status: EarningsAnalysisStatus;

  // We need to know when the user started the analysis
  // to know if we need to retrieve the earnings if we did not receive the webhook
  startedAt?: Date;

  earningsNotificationSent?: boolean;

  constructor(props: EarningsAnalysis) {
    this.status = props.status;
    this.totalEarnings = props.totalEarnings;
    this.earnings = props.earnings;
    this.platforms = props.platforms;
    this.startedAt = props.startedAt;
    this.earningsNotificationSent = props.earningsNotificationSent;
  }
}

// WHY IS THIS WHOLE THING EVEN REPLICATED HERE???????
export class AdmissionRequest implements TimestampProps {
  id?: string | undefined;

  status: AdmissionRequestStatus;

  rejectionReason?: AdmissionRequestRejectionReason | null;

  personalData: RequestPersonalData;

  documentsAnalysis: RequestDocumentsAnalysis;

  palenca: RequestPalenca;

  homeVisit?: HomeVisit | null;

  earningsAnalysis: EarningsAnalysis;

  riskAnalysis: RiskAnalysis;

  hubspot?: {
    id?: string | null;
  };

  hubspotDeal?: {
    id?: string | null;
  };

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: AdmissionRequest) {
    this.id = props.id;
    this.status = props.status;
    this.rejectionReason = props.rejectionReason || null;
    this.personalData = props.personalData;
    this.documentsAnalysis = props.documentsAnalysis;
    this.hubspot = props.hubspot || undefined;
    this.hubspotDeal = props.hubspotDeal || undefined;
    this.palenca = props.palenca;
    this.homeVisit = props.homeVisit || null;
    this.earningsAnalysis = props.earningsAnalysis;
    this.riskAnalysis = props.riskAnalysis;
    this.createdAt = props.createdAt || new Date();
    this.updatedAt = props.updatedAt || new Date();
  }
}

export class PalencaWebhook {
  webhookAction: PalencaWebhookAction;

  userId: string;

  accountId: string;

  // This is the same as our customerId, the only way we can map
  // a Palenca customer with our customer is through this field
  externalId: string;

  country: Country;

  platform: GigPlatform;

  statusDetails?: string | null;

  constructor(props: PalencaWebhook) {
    this.webhookAction = props.webhookAction;
    this.userId = props.userId;
    this.accountId = props.accountId;
    this.externalId = props.externalId;
    this.country = props.country;
    this.platform = props.platform;
    this.statusDetails = props.statusDetails;
  }
}

export class DailyEarning {
  amount: number;

  countTrips: number;

  earningDate: Date;

  currency: CurrencyCode;

  constructor(props: DailyEarning) {
    this.amount = props.amount;
    this.countTrips = props.countTrips;
    this.earningDate = props.earningDate;
    this.currency = props.currency;
  }
}
export class WeeklyEarning {
  totalAmount: number;

  totalTrips: number;

  fromDate: Date;

  toDate: Date;

  week: number;

  year: number;

  currency: CurrencyCode;

  dailyEarnings: DailyEarning[];

  constructor(props: WeeklyEarning) {
    this.totalAmount = props.totalAmount;
    this.totalTrips = props.totalTrips;
    this.fromDate = props.fromDate;
    this.toDate = props.toDate;
    this.week = props.week;
    this.year = props.year;
    this.currency = props.currency;
    this.dailyEarnings = props.dailyEarnings;
  }
}

export class Earning {
  id?: string | undefined;

  amount: number;

  currency: CurrencyCode;

  earningDate: Date;

  countTrips: number;

  requestId: string;

  cashAmount?: number;

  platform: GigPlatform;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Earning) {
    this.id = props.id;
    this.amount = props.amount;
    this.currency = props.currency;
    this.earningDate = props.earningDate;
    this.countTrips = props.countTrips;
    this.requestId = props.requestId;
    this.platform = props.platform;
    this.cashAmount = props.cashAmount;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
export class Metric {
  id?: string | undefined;

  requestId: string;

  acceptanceRate: number;

  cancellationRate: number;

  rating: number;

  lifetimeTrips: number;

  timeSinceFirstTrip: number;

  activationStatus: string;

  platform: GigPlatform;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Metric) {
    this.id = props.id;
    this.requestId = props.requestId;
    this.acceptanceRate = props.acceptanceRate;
    this.cancellationRate = props.cancellationRate;
    this.rating = props.rating;
    this.lifetimeTrips = props.lifetimeTrips;
    this.timeSinceFirstTrip = props.timeSinceFirstTrip;
    this.activationStatus = props.activationStatus;
    this.platform = props.platform;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}

export class PaginationSearchOptions {
  page: number;

  itemsPerPage: number;

  constructor(props: PaginationSearchOptions) {
    this.page = props.page || 1;
    this.itemsPerPage = props.itemsPerPage || DEFAULT_PAGINATION_LIMIT;

    if (this.itemsPerPage > MAX_PAGINATION_LIMIT) {
      this.itemsPerPage = MAX_PAGINATION_LIMIT;
    }
  }
}

export class Pagination {
  hasPrevious: boolean;

  hasMore: boolean;

  page: number;

  totalItems: number;

  totalPages: number;

  constructor(props: Pagination) {
    this.hasPrevious = props.hasPrevious;
    this.hasMore = props.hasMore;
    this.page = props.page;
    this.totalItems = props.totalItems;
    this.totalPages = props.totalPages;
  }
}

export class Event {
  id?: string | undefined;

  userId?: string;

  entityId: string;

  entityType: EntityType;

  actionType: ActionType;

  message: string;

  createdAt?: Date;

  user?: User | null;

  constructor(props: Event) {
    this.id = props.id;
    this.userId = props.userId;
    this.entityId = props.entityId;
    this.entityType = props.entityType;
    this.actionType = props.actionType;
    this.message = props.message;
    this.createdAt = props.createdAt || new Date();
    this.user = props.user || null;
  }
}

export class User {
  id?: string | undefined;

  name: string;

  image?: string;

  constructor(props: User) {
    this.id = props.id;
    this.name = props.name;
    this.image = props.image;
  }
}

/**
 * Represents a category within a scorecard variable.
 * Includes details such as threshold for evaluation, risk category, risk score, and weight.
 * Used for defining the scorecard's structure and scoring parameters.
 */
export class ScorecardVariableCategory {
  threshold: { min: number; max: number } | string;

  riskCategory: RiskCategory;

  riskScore: number;

  weight: number;

  constructor(props: ScorecardVariableCategory) {
    this.threshold = props.threshold;
    this.riskCategory = props.riskCategory;
    this.riskScore = props.riskScore;
    this.weight = props.weight;
  }
}

/**
 * Extends the ScorecardVariableCategory with a 'result' property.
 * Used in the context of a ScorecardDetail to store the calculated result
 * for a specific category, reflecting its contribution to the total score.
 */
export class ScorecardDetailCategory extends ScorecardVariableCategory {
  result: number;

  constructor(props: ScorecardDetailCategory) {
    super(props);
    this.result = props.result;
  }
}

/**
 * Represents a variable in the scorecard.
 * Contains a name identifier for the variable and an array of categories,
 * each of which represents a possible classification within the variable.
 */
export class ScorecardVariable {
  name: ScorecardVariableName;

  categories: ScorecardVariableCategory[];

  type: ScorecardVariableCategoryType;

  constructor(props: ScorecardVariable) {
    this.name = props.name;
    this.categories = props.categories;
    this.type = props.type;
  }
}

/**
 * Configuration class for a scorecard.
 * Holds the version of the scorecard and an array of ScorecardVariables,
 * defining the structure and criteria of the scorecard.
 */
export class ScorecardConfig {
  version: ScorecardVersion;

  variables: ScorecardVariable[];

  minScore: number;

  maxScore: number;

  minScaledScore: number;

  maxScaledScore: number;

  constructor(props: ScorecardConfig) {
    this.version = props.version;
    this.variables = props.variables;
    this.minScore = props.minScore;
    this.maxScore = props.maxScore;
    this.minScaledScore = props.minScaledScore;
    this.maxScaledScore = props.maxScaledScore;
  }
}

/**
 * Represents the detailed selection of a category for a specific variable
 * in the scorecard during a risk analysis.
 * Stores the chosen variable and the corresponding category details.
 */
export class ScorecardDetail {
  variable: ScorecardVariableName;

  category: ScorecardDetailCategory;

  constructor(props: ScorecardDetail) {
    this.variable = props.variable;
    this.category = props.category;
  }
}

/**
 * Represents the overall scorecard used in a risk analysis.
 * Includes the total score achieved, the version of the scorecard used,
 * and a detailed breakdown of the score in terms of individual variables and categories.
 */
export class Scorecard {
  totalScore: number;

  scaledScore: number;

  minScore: number;

  maxScore: number;

  minScaledScore: number;

  maxScaledScore: number;

  details: ScorecardDetail[];

  constructor(props: Scorecard) {
    this.totalScore = props.totalScore;
    this.details = props.details;
    this.scaledScore = props.scaledScore;
    this.minScore = props.minScore;
    this.maxScore = props.maxScore;
    this.minScaledScore = props.minScaledScore;
    this.maxScaledScore = props.maxScaledScore;
  }
}

/**
 * Represents the complete risk analysis for an individual or entity.
 * Includes the status of the analysis and the detailed Scorecard used,
 * capturing the total score and the specific assessments made for each variable.
 */
export class RiskAnalysis {
  status: RiskAnalysisStatus;

  scorecardVersion: ScorecardVersion;

  scorecard?: Scorecard | null;

  constructor(props: RiskAnalysis) {
    this.status = props.status;
    this.scorecardVersion = props.scorecardVersion;
    this.scorecard = props.scorecard || null;
  }
}

export type RiskAnalysisDataVariables = Partial<Map<ScorecardVariableName, number | string>>;

/**
 * Represents the data collected for a risk analysis.
 * We store all the variables as they become available and are calculated.
 * Then we use this data to generate the scorecard and perform the risk analysis.
 *
 */
export class RiskAnalysisData {
  id?: string | undefined;

  requestId: string;

  variables: RiskAnalysisDataVariables;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: RiskAnalysisData) {
    this.id = props.id;
    this.requestId = props.requestId;
    this.variables = props.variables;
    this.createdAt = props.createdAt || new Date();
    this.updatedAt = props.updatedAt || new Date();
  }
}

export class PalencaJobRetrieveEarnings {
  accountId: string;

  platform: GigPlatform;

  requestId: string;

  constructor(props: PalencaJobRetrieveEarnings) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.requestId = props.requestId;
  }
}

export class PalencaJobRetrieveMetrics {
  accountId: string;

  platform: GigPlatform;

  requestId: string;

  constructor(props: PalencaJobRetrieveMetrics) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.requestId = props.requestId;
  }
}
