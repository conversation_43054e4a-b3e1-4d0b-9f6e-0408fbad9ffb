import { Schema, Types, model } from 'mongoose';
import { ReassignLeadsReason } from '@/constants';

export interface ReassignAuditLogDocument extends Document {
  fromAgentName: string;
  fromAgentId: Types.ObjectId;
  toAgentName: string;
  toAgentId: Types.ObjectId;
  solicitudeId: Types.ObjectId;
  reason: ReassignLeadsReason;
  reassignedBy: Types.ObjectId;
  reassignedByName: string;
  description: string;
  createdAt: Date;
}

const ReassignAuditLogSchema = new Schema<ReassignAuditLogDocument>(
  {
    fromAgentName: {
      type: String,
    },
    fromAgentId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    toAgentName: {
      type: String,
      required: [true, 'To Agent Name is required'],
    },
    toAgentId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'To Agent ID is required'],
    },
    solicitudeId: {
      type: Schema.Types.ObjectId,
      ref: 'admissionRequests',
      required: [true, 'Solicitude ID is required'],
    },
    reason: {
      type: String,
      enum: Object.values(ReassignLeadsReason),
      required: [true, 'Reason is required'],
    },
    reassignedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Reassigned By is required'],
    },
    reassignedByName: {
      type: String,
      required: [true, 'Reassigned By Name is required'],
    },
    description: {
      type: String,
      required: false,
      default: null,
    },
    createdAt: {
      type: Date,
      default: () => new Date(),
    },
  },
  {
    timestamps: {
      currentTime: () => Date.now(), // Use epoch timestamps for timestamps
      createdAt: true,
      updatedAt: true,
    },
  }
);

export const ReassignAuditLogModel = model<ReassignAuditLogDocument>(
  'ReassignAuditLog',
  ReassignAuditLogSchema
);

export default ReassignAuditLogModel;
