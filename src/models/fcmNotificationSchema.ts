import { FCMNotificationStatus } from '@/modules/FirebaseCloudMessaging/common/enums';
import { model, Schema, Types } from 'mongoose';

export interface FCMNotificationFailedDetails {
  code: string;
  failedAt: Date | null;
  failedReason: string | null;
  token: string;
}

export interface FCMNotificationMongoI {
  _id: Types.ObjectId;
  userId: Types.ObjectId; // This can be associate ID or admission request ID
  payload: {
    title: string;
    body: string;
    data: Record<string, any>;
  };
  status: FCMNotificationStatus;
  messageId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  failedDetails?: FCMNotificationFailedDetails;
}

const payloadSchema = new Schema(
  {
    title: { type: String, required: true },
    body: { type: String, required: true },
    data: { type: Object, default: {} },
  },
  { _id: false }
);

const failedDetailsSchema = new Schema(
  {
    code: { type: String, required: true },
    failedAt: { type: Date, default: null },
    failedReason: { type: String, default: null },
    token: { type: String, required: true },
  },
  { _id: false }
);

const FCMNotificationModel = model(
  'FCMNotification',
  new Schema<FCMNotificationMongoI>(
    {
      userId: { type: Schema.Types.ObjectId, required: true },
      payload: { type: payloadSchema, required: true },
      status: {
        type: String,
        enum: Object.values(FCMNotificationStatus),
        default: FCMNotificationStatus.PENDING,
      },
      messageId: { type: String, default: null },
      failedDetails: { type: failedDetailsSchema, default: null },
    },
    { timestamps: true }
  )
);

const instance = new FCMNotificationModel();
export type FCMNotificationType = typeof instance;
export default FCMNotificationModel as FCMNotificationType & typeof FCMNotificationModel;
