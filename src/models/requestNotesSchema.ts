import { Document, model, Schema, Types } from 'mongoose';

export interface RequestNoteMongoI extends Document {
  content: string;
  author: Types.ObjectId; // Reference to User
  admissionRequest?: Types.ObjectId; // Optional link to AdmissionRequest
  createdAt: Date;
  updatedAt: Date;
}

const noteSchema = new Schema<RequestNoteMongoI>(
  {
    content: {
      type: String,
      required: [true, 'Content is required'],
    },
    author: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Author is required'],
    },
    admissionRequest: {
      type: Schema.Types.ObjectId,
      ref: 'AdmissionRequest',
      required: false,
    },
  },
  { timestamps: true }
);

export const RequestNoteMongo = model<RequestNoteMongoI>('Note', noteSchema, 'notes');
