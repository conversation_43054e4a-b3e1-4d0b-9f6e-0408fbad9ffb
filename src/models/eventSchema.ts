import { Types } from 'mongoose';

import { Document, Schema, model } from 'mongoose';
import { UserMongoI } from './userSchema';

export interface EventMongoI extends Document {
  id?: string | undefined;
  user: UserMongoI;
  entityId: string;
  entityType: string;
  actionType: string;
  message: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

const eventsSchema = new Schema(
  {
    user: {
      type: Types.ObjectId,
      required: true,
      ref: 'User',
    },
    entityId: {
      type: Types.ObjectId,
      required: true,
    },
    entityType: {
      type: String,
      required: true,
    },
    actionType: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
  },
  {
    timestamps: true,
    toObject: {
      virtuals: true,
    },
  }
);

export const EventMongo = model<EventMongoI>('Event', eventsSchema, 'events');
