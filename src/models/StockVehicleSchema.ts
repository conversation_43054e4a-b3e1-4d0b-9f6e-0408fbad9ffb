import { Schema, Types, model } from 'mongoose';
import { getCurrentDateTime } from '../services/timestamps';
import { CountriesEnum, enumStepName, enumStepNumber } from '../constants';
import Associate from './associateSchema';
import Document from './documentSchema';

const associate = new Associate();

export interface Violation {
  folio: string;
  violationDate: Date;
  status: string;
}
export interface IStockVehicle extends Document {
  _id: Types.ObjectId;
  carNumber: string;
  extensionCarNumber?: number;
  step: {
    stepName: (typeof enumStepName)[number];
    stepNumber: (typeof enumStepNumber)[number];
  };
  vehicleDocsComplete: boolean;
  model: string;
  brand: string;
  status: VehicleStatusType;
  vehicleStatus: UpdatedVehicleStatusType;
  category: VehicleCategoryType;
  subCategory: VehicleSubCategoryType;
  version: string;
  newCar: boolean;
  platform: string;
  isElectric: boolean;
  year: string;
  color: string;
  vin: string;
  vehicleState: VehicleStateType;
  owner: string;
  billAmount: number;
  bill?: Types.ObjectId;
  billDate?: string;
  receptionDate?: string;
  billNumber?: string;
  qrCode?: Types.ObjectId;
  contract?: Types.ObjectId;
  carPlates?: {
    plates: string;
    frontImg?: Types.ObjectId;
    backImg?: Types.ObjectId;
    platesDocument?: Types.ObjectId;
  };
  circulationCard?: {
    number: string;
    frontImg?: Types.ObjectId;
    backImg?: Types.ObjectId;
    validity?: string;
    older?: {
      number: string;
      frontImg: Types.ObjectId;
      backImg: Types.ObjectId;
      validity: string;
    }[];
  };
  gpsNumber?: string;
  paymentCount: number;
  gpsSerie?: string;
  gpsInstalled: boolean;
  km: number;
  mi: number;
  policiesArray: {
    _id: Types.ObjectId;
    policyNumber: number;
    insurer: string;
    validity: string;
    broker: string;
    policyDocument: Types.ObjectId;
  }[];
  tenancy: {
    _id: Types.ObjectId;
    payment: number;
    validity: string;
    tenancyDocument: Types.ObjectId;
  }[];
  updateHistory: {
    userId: Types.ObjectId;
    step: string;
    description?: string;
    time?: string;
    filter?: UpdateHistoryFilterType;
    group?: UpdateHistoryGroupType;
  }[];
  drivers: {
    _id: Types.ObjectId;
  }[];
  associates: (typeof associate)[];
  oldDocuments: {
    path: string;
    originalName: string;
  }[];
  readmissionReason?: string;
  adendumServiceCount: number;
  readmissionDate?: string;
  deliveredDate: string[] | [];
  canFinishProcess: boolean;
  dischargedData?: {
    reason: string;
    date: string;
    comments?: string;
    platesDischargedDoc?: Types.ObjectId;
    dictamenDoc?: Types.ObjectId;
    reportDoc?: Types.ObjectId;
  };
  transferredTo?: string | null;
  isBlocked: boolean;
  country: string;
  state: string;
  physicalStatus: PhysicalVehicleStatusType;

  purchaseAgreement: Types.ObjectId;

  soldInvoicePdf: Types.ObjectId;

  soldInvoiceXml: Types.ObjectId;
  platesCancelation: Types.ObjectId;

  createdAt: string;
  updatedAt: string;

  lastViolationCheck: Date | null;
  violations: Violation[];

  // Corrective Maintenance History
  correctiveMaintenanceHistory: {
    _id: Types.ObjectId;
    orderId: Types.ObjectId;
    type: 'customer-initiated' | 'preventive-detected' | 'workshop-initiated';
    status: 'pending' | 'diagnosed' | 'quoted' | 'approved' | 'in-progress' | 'completed' | 'cancelled';
    totalCost: number;
    totalDuration: number;
    createdAt: Date;
    completedAt?: Date;
  }[];
}

export enum VehicleStatus {
  invoiced = 'invoiced',
  stock = 'stock',
  active = 'active',
  activo = 'activo',
  readmissions = 'readmissions',
  discharged = 'discharged',
  desbloqueo = 'desbloqueo',
  bloqueo = 'bloqueo',
  'in-service' = 'in-service',
  'awaiting-insurance' = 'awaiting-insurance',
  'legal-process' = 'legal-process',
  overhauling = 'overhauling',
  sold = 'sold',
}
export type VehicleStatusType = keyof typeof VehicleStatus;

//new fields added here

export enum UpdatedVehicleStatus {
  active = 'active',
  inactive = 'inactive',
  'default' = '',
}
export type UpdatedVehicleStatusType = keyof typeof UpdatedVehicleStatus | UpdatedVehicleStatus.default;

export enum VehicleCategory {
  withdrawn = 'withdrawn',
  sold = 'sold',
  insurance = 'insurance',
  collection = 'collection',
  legal = 'legal',
  workshop = 'workshop',
  revision = 'revision',
  adendum = 'adendum',
  'in-preparation' = 'in-preparation',
  stock = 'stock',
  assigned = 'assigned',
  delivered = 'delivered',
  utilitary = 'utilitary',
  'default' = '',
}
export type VehicleCategoryType = keyof typeof VehicleCategory | VehicleCategory.default;

export enum VehicleSubCategory {
  'damage-payment' = 'damage-payment',
  valuation = 'valuation',
  repair = 'repair',
  'payment-commitment' = 'payment-commitment',
  'payment-extension' = 'payment-extension',
  'non-payment' = 'non-payment',
  'incomplete-payment' = 'incomplete-payment',
  'in-recovery' = 'in-recovery',
  demand = 'demand',
  'public-ministry' = 'public-ministry',
  complaint = 'complaint',
  impounded = 'impounded',
  'aesthetic-repair' = 'aesthetic-repair',
  'duplicate-key-missing' = 'duplicate-key-missing',
  'mechanical-repair' = 'mechanical-repair',
  'electrical-repair' = 'electrical-repair',
  'engine-repair' = 'engine-repair',
  'waiting-for-parts' = 'waiting-for-parts',
  'corrective-maintenance' = 'corrective-maintenance',
  management = 'management',
  gps = 'gps',
  'total-loss' = 'total-loss',
  'operational-loss' = 'operational-loss',
  'default' = '',
}
export type VehicleSubCategoryType = keyof typeof VehicleSubCategory | VehicleSubCategory.default;

//new fields above

export enum VehicleState {
  cdmx = 'cdmx',
  gdl = 'gdl',
  mty = 'mty',
  tij = 'tij',
  qro = 'qro',
  moka = 'moka',
  pbe = 'pbe',
  tol = 'tol',
  ptv = 'ptv',
  tep = 'tep',
  col = 'col',
  sal = 'sal',
  torr = 'torr',
  dur = 'dur',
  mxli = 'mxli',
  her = 'her',
  chi = 'chi',
  leo = 'leo',
  ags = 'ags',
  slp = 'slp',
  mer = 'mer',
  // mia = 'mia', // US -> Florida state -> Miami city
  dal = 'dal', // US -> Texas state -> Dallas city
}

export const vehicleStateEnum = Object.keys(VehicleState) as VehicleStateType[];

export type VehicleStateType = keyof typeof VehicleState;

export enum UpdateHistoryFilter {
  all = 'all',
  status = 'status',
  step = 'step',
  user = 'user',
  date = 'date',
}

export type UpdateHistoryFilterType = keyof typeof UpdateHistoryFilter;

export enum UpdateHistoryGroup {
  'vehicle-info' = 'vehicle-info',
  date = 'date',
  'contract-info' = 'contract-info',
}

export type UpdateHistoryGroupType = keyof typeof UpdateHistoryGroup;

// Enum for the new physicalStatus field
export enum PhysicalVehicleStatus {
  AWAITING_RECEIPT = 'AWAITING_RECEIPT',
  RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP = 'RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP',
  AVAILABLE_IN_STOCK = 'AVAILABLE_IN_STOCK',
  VEHICLE_TO_BE_REPAIRED = 'VEHICLE_TO_BE_REPAIRED',
  COLLECTED_FROM_STOCK = 'COLLECTED_FROM_STOCK',
  DELIVERED_TO_CUSTOMER = 'DELIVERED_TO_CUSTOMER',

  // New statuses for vendor workshop flow
  IN_TRANSIT_TO_VENDOR_WORKSHOP = 'IN_TRANSIT_TO_VENDOR_WORKSHOP',
  RECEIVED_BY_VENDOR_WORKSHOP = 'RECEIVED_BY_VENDOR_WORKSHOP',
  UNDER_REPAIR_AT_VENDOR_WORKSHOP = 'UNDER_REPAIR_AT_VENDOR_WORKSHOP',
  REPAIR_COMPLETE_BY_VENDOR = 'REPAIR_COMPLETE_BY_VENDOR',
  COLLECTED_BY_CUSTOMER = 'COLLECTED_BY_CUSTOMER',
  RETURNED_TO_OCN_AND_REAVAILABLE = 'RETURNED_TO_OCN_AND_REAVAILABLE',
  RETURNED_AND_AVAILABLE_FOR_REDELIVERY = 'RETURNED_AND_AVAILABLE_FOR_REDELIVERY',
  COLLECTED_FROM_STOCK_TO_BE_REDELIVERED = 'COLLECTED_FROM_STOCK_TO_BE_REDELIVERED',
  REDELIVERED_TO_CUSTOMER = 'REDELIVERED_TO_CUSTOMER',
  COLLECTED_FROM_VENDOR_BY_OCN_AGENT = 'COLLECTED_FROM_VENDOR_BY_OCN_AGENT',

  // Repossession Flow Statuses
  REPOSSESSION_COMPLETE = 'REPOSSESSION_COMPLETE',
  RETURNED_TO_OCN_WAREHOUSE = 'RETURNED_TO_OCN_WAREHOUSE',
}

export type PhysicalVehicleStatusType = keyof typeof PhysicalVehicleStatus;

const StockVehicleSchema = new Schema<IStockVehicle>({
  carNumber: {
    type: String,
    required: true,
    unique: true,
  },

  extensionCarNumber: {
    type: Number,
  },

  step: {
    type: {
      stepName: {
        type: String,
        required: true,
        enum: enumStepName,
      },
      stepNumber: { type: Number, required: true, enum: enumStepNumber },
    },
    default: {
      stepName: 'Stock',
      stepNumber: 1,
    },
    required: true,
    _id: false,
  },

  vehicleDocsComplete: {
    type: Boolean,
    default: false,
  },

  // vehiclePhoto: {
  //   type: Schema.Types.ObjectId,
  //   ref: 'Document',
  // },

  model: {
    type: String,
    required: true,
  },

  brand: {
    type: String,
    required: true,
  },

  status: {
    type: String, // Esto de active y activo es para cambiar a ingles los registros sin romper nada en la transición
    enum: [
      'invoiced', // pre-stock
      'stock',
      'active',
      'activo',
      'readmissions',
      'discharged',
      'desbloqueo',
      'bloqueo',
      'in-service',
      'awaiting-insurance',
      'legal-process',
      'overhauling',
      'sold',
    ],
    default: VehicleStatus.invoiced,
  },

  //new fields below

  vehicleStatus: {
    type: String,
    enum: Object.values(UpdatedVehicleStatus),
    default: UpdatedVehicleStatus.inactive,
  },

  category: {
    type: String,
    enum: Object.values(VehicleCategory),
    default: VehicleCategory.default,
  },

  subCategory: {
    type: String,
    enum: Object.values(VehicleSubCategory),
    default: VehicleSubCategory.default,
  },

  //new fields above

  version: {
    type: String,
    required: true,
  },

  newCar: {
    type: Boolean,
    default: true,
  },

  platform: {
    type: String,
    // required: true
  },

  isElectric: {
    type: Boolean,
    default: false,
  },

  year: {
    type: String,
    required: true,
  },

  color: {
    type: String,
    required: true,
  },

  vin: {
    type: String,
    required: true,
    unique: true,
  },

  vehicleState: {
    type: String,
    enum: vehicleStateEnum,
    required: [true, 'State not found'],
  },

  owner: {
    type: String,
    required: true,
  },

  billAmount: {
    type: Number,
    required: true,
  },

  billNumber: {
    type: String,
  },

  billDate: {
    type: String,
  },

  receptionDate: {
    type: String,
  },

  bill: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  qrCode: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  contract: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  carPlates: {
    plates: String,
    frontImg: {
      type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
      ref: 'Document',
    },
    backImg: {
      type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
      ref: 'Document',
    },
    platesDocument: {
      type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
      ref: 'Document',
    },
  },

  circulationCard: {
    type: {
      number: String,
      frontImg: {
        type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
        ref: 'Document',
      },
      backImg: {
        type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
        ref: 'Document',
      },
      validity: { type: String, required: false },
      older: {
        type: [
          // This field is only for TIJUANA vehicles
          {
            number: {
              type: String,
              required: true,
            },
            frontImg: {
              type: Schema.Types.ObjectId,
              ref: 'Document',
              required: true,
            },
            backImg: {
              type: Schema.Types.ObjectId,
              ref: 'Document',
              required: true,
            },
            validity: {
              type: String,
              required: true,
            },
          },
        ],
        required: false,
      },
    },
    default: null,
  },

  gpsNumber: {
    type: String,
  },

  paymentCount: {
    type: Number,
    default: 0,
  },

  gpsSerie: {
    type: String,
  },

  gpsInstalled: {
    type: Boolean,
    default: false,
  },

  km: { type: Number, required: false, default: 0 },

  policiesArray: {
    type: [
      {
        _id: { type: Schema.Types.ObjectId, required: true },
        policyNumber: { type: Number, required: true },
        insurer: { type: String, required: true },
        validity: { type: String, required: true },
        broker: { type: String, required: true },
        policyDocument: {
          type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
          ref: 'Document',
          required: true,
        },
      },
    ],
    default: [],
  },

  tenancy: {
    type: [
      {
        _id: { type: Schema.Types.ObjectId, required: true },
        payment: { type: Number, required: true },
        validity: { type: String, required: true },
        tenancyDocument: {
          type: Schema.Types.ObjectId, // Tipo de datos ObjectId para la referencia al modelo User
          ref: 'Document',
          required: true,
        },
      },
    ],
    default: [],
  },

  updateHistory: {
    type: [
      {
        userId: {
          type: Schema.Types.ObjectId,
          ref: 'User',
          required: true,
        },
        step: {
          type: String,
          required: true,
        },
        description: {
          type: String,
        },
        time: {
          type: String,
          required: false,
          default: getCurrentDateTime,
        },
        filter: {
          type: String,
          required: false,
          enum: ['all', 'status', 'step', 'user', 'date'],
        },
        group: {
          type: String,
          required: false,
          enum: ['vehicle-info', 'date', 'contract-info'],
        },
      },
    ],
    default: [],
  },

  drivers: {
    type: [
      {
        _id: {
          type: Schema.Types.ObjectId,
          ref: 'Associate',
        },
      },
    ],
    default: [],
  },

  oldDocuments: {
    type: [
      {
        path: String,
        originalName: String,
      },
    ],
    default: [],
  },

  readmissionReason: {
    type: String,
    default: null,
  },

  adendumServiceCount: {
    type: Number,
    default: null,
  },

  readmissionDate: {
    type: String,
    default: null,
  },

  deliveredDate: {
    type: [String],
    default: [],
  },

  canFinishProcess: {
    type: Boolean,
    default: false,
  },

  dischargedData: {
    reason: String,
    date: String,
    comments: String,
    platesDischargedDoc: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
    },
    dictamenDoc: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
    },
    reportDoc: {
      type: Schema.Types.ObjectId,
      ref: 'Document',
    },
  },

  transferredTo: {
    type: String,
  },
  isBlocked: {
    type: Boolean,
    default: false,
  },

  country: {
    type: String,
    enum: Object.values(CountriesEnum),
    default: CountriesEnum.Mexico,
  },

  state: {
    type: String,
    default: null,
  },

  mi: { type: Number, required: false, default: 0 },

  physicalStatus: {
    type: String,
    enum: Object.values(PhysicalVehicleStatus),
    default: PhysicalVehicleStatus.AWAITING_RECEIPT,
    required: true,
  },

  purchaseAgreement: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  soldInvoicePdf: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  soldInvoiceXml: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },
  platesCancelation: {
    type: Schema.Types.ObjectId,
    ref: 'Document',
  },

  createdAt: { type: String, default: getCurrentDateTime },
  updatedAt: { type: String, default: getCurrentDateTime },

  lastViolationCheck: { type: Date, default: null },
  violations: {
    type: [
      {
        folio: String,
        violationDate: Date,
        status: String,
        amount: {
          type: Number,
          required: false,
        },
      },
    ],
    default: [],
  },

  correctiveMaintenanceHistory: {
    type: [
      {
        _id: { type: Schema.Types.ObjectId, required: true },
        orderId: { type: Schema.Types.ObjectId, ref: 'CorrectiveMaintenanceOrder', required: true },
        type: {
          type: String,
          enum: ['customer-initiated', 'preventive-detected', 'workshop-initiated'],
          required: true,
        },
        status: {
          type: String,
          enum: ['pending', 'diagnosed', 'quoted', 'approved', 'in-progress', 'completed', 'cancelled'],
          required: true,
        },
        totalCost: { type: Number, required: true },
        totalDuration: { type: Number, required: true },
        createdAt: { type: Date, default: Date.now },
        completedAt: { type: Date },
      },
    ],
    default: [],
  },
});

StockVehicleSchema.virtual('associates', {
  ref: 'Associate', // El modelo a poblar
  localField: 'drivers._id', // El campo en tu esquema que apunta al modelo User
  foreignField: '_id', // El campo en User que corresponde a localField
  // justOne: true // Retorna un objeto en lugar de un array
});

StockVehicleSchema.set('toJSON', { virtuals: true });
StockVehicleSchema.set('toObject', { virtuals: true });

const StockVehicle = model('StockVehicle', StockVehicleSchema);

export default StockVehicle;
