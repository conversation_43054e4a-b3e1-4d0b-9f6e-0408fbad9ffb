// models/AuditLog.ts
import { Schema, model, Types } from 'mongoose';

const auditLogSchema = new Schema({
  action: { type: String, required: true },
  performedBy: { type: Types.ObjectId, ref: 'User', required: true },
  timestamp: { type: Date, default: Date.now },
  metadata: { type: Object },
});

const AuditLog = model('AuditLog', auditLogSchema);

export const UserMongo = AuditLog;

export default AuditLog;
