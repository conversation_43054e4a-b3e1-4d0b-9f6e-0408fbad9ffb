import { Schema, Document, model, models } from 'mongoose';
import { Roles, Areas } from '@/constants';

export interface Permission {
  section: string;
  subSection: string;
  capability: string;
}

export interface PermissionSetDocument extends Document {
  name: string;
  role: string;
  area: string;
  permissions: Permission[];
}

const PermissionSchema = new Schema<Permission>(
  {
    section: { type: String, required: true },
    subSection: { type: String, required: false },
    capability: { type: String, required: true },
  },
  { _id: false }
);

const PermissionSetSchema = new Schema<PermissionSetDocument>({
  name: { type: String, required: true },
  role: {
    type: String,
    enum: Object.values(Roles),
    required: [true, 'Role is required'],
  },
  area: {
    type: String,
    enum: Object.values(Areas),
    required: [true, 'Area is required'],
  },
  permissions: { type: [PermissionSchema], required: true },
});

export default models.PermissionSet || model<PermissionSetDocument>('PermissionSet', PermissionSetSchema);
