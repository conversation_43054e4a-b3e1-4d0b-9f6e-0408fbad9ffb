import { Document, Schema, Types, model } from 'mongoose';

// Interface for Block Lead Assignation Audit log
interface IBlockLeadAssignationAudit extends Document {
  timestamp: Date;
  action: 'create' | 'update' | 'delete';
  user: Types.ObjectId;
  userName: string;
  agent: Types.ObjectId;
  agentName: string;
  serialNumber: number;
  description: string;
  previousData?: {
    blockedFrom?: Date;
    blockedUntil?: Date;
    reason?: string;
  };
  newData?: {
    blockedFrom?: Date;
    blockedUntil?: Date;
    reason?: string;
  };
}

const blockLeadAssignationAuditSchema = new Schema<IBlockLeadAssignationAudit>(
  {
    timestamp: {
      type: Date,
      default: Date.now,
      required: true,
    },
    action: {
      type: String,
      enum: ['create', 'update', 'delete'],
      required: true,
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    userName: {
      type: String,
      required: true,
    },
    agent: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    agentName: {
      type: String,
      required: true,
    },
    serialNumber: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    previousData: {
      blockedFrom: Date,
      blockedUntil: Date,
      reason: String,
    },
    newData: {
      blockedFrom: Date,
      blockedUntil: Date,
      reason: String,
    },
  },
  {
    timestamps: false,
  }
);

export const BlockLeadAssignationAudit = model<IBlockLeadAssignationAudit>(
  'BlockLeadAssignationAudit',
  blockLeadAssignationAuditSchema
);
