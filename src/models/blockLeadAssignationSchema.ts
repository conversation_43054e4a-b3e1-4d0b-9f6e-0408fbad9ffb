import { Document, Schema, Types, model } from 'mongoose';
import { BlockLeadAssignationReason } from '@/constants';

export interface BlockLeadAssignationI extends Document {
  serialNumber: number; // Add this line
  agent: Types.ObjectId;
  blockedFrom: Date;
  blockedUntil: Date;
  reason: BlockLeadAssignationReason;
  blockedBy: Types.ObjectId;
  createdAt: Date;
}

const blockLeadAssignationSchema = new Schema({
  serialNumber: {
    type: Number,
    unique: true,
  },
  agent: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Agent is required'],
  },
  blockedFrom: {
    type: Date,
    required: [true, 'Blocked From date is required'],
  },
  blockedUntil: {
    type: Date,
    required: [true, 'Blocked Until date is required'],
  },
  reason: {
    type: String,
    enum: Object.values(BlockLeadAssignationReason),
    required: [true, 'Reason is required'],
  },
  blockedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Blocked By user is required'],
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

export const BlockLeadAssignation = model<BlockLeadAssignationI>(
  'BlockLeadAssignation',
  blockLeadAssignationSchema
);
