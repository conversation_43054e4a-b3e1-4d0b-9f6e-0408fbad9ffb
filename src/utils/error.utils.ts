export const errorCodeUtils = {
  INTERNAL_SERVER_ERROR: {
    code: 'INTERNAL_SERVER_ERROR',
    status: 500,
    message: 'An internal server error occurred.',
  },
  ASSOCIATE_NOT_ACTIVE: {
    code: 'ASSOCIATE_NOT_ACTIVE',
    status: 1001,
    message: 'The associate is not active.',
  },
  USER_NOT_ALLOWED: {
    code: 'USER_NOT_ALLOWED',
    status: 1002,
    message: 'User is not allowed to perform this action.',
  },
  UNABLE_TO_GENERATE_OR_SEND_OTP: {
    code: 'UNABLE_GENERATE_OR_SEND_OTP',
    status: 1003,
    message: 'Unable to generate or send OTP.',
  },
  INVALID_OR_EXPIRED_OTP: {
    code: 'INVALID_OR_EXPIRED_OTP',
    status: 1004,
    message: 'The OTP is invalid or has expired.',
  },
  ORGANIZATION_NOT_FOUND: {
    code: 'ORGANIZATION_NOT_FOUND',
    status: 1005,
    message: 'Organization not found.',
  },
  ASSOCIATE_NOT_FOUND: {
    code: 'ASSOCIATE_NOT_FOUND',
    status: 1006,
    message: 'Associate not found.',
  },
  VEHICLE_DETAILS_NOT_FOUND: {
    code: 'VEHICLE_DETAILS_NOT_FOUND',
    status: 1007,
    message: 'Vehicle details not found.',
  },
  NOTIFICATION_TOKEN_ALREADY_EXISTS: {
    code: 'NOTIFICATION_TOKEN_ALREADY_EXISTS',
    status: 1008,
    message: 'Notification token already exists for the associateId.',
  },
  INVALID_REQUEST_DATA: {
    code: 'INVALID_REQUEST_DATA',
    status: 1009,
    message: 'Invalid request data provided.',
  },
  NO_ACTIVE_TOKENS: {
    code: 'NO_ACTIVE_TOKENS',
    status: 1010,
    message: 'No active FCM tokens found for the associateId.',
  },
  NOTIFICATION_SEND_FAILED: {
    code: 'NOTIFICATION_SEND_FAILED',
    status: 1011,
    message: 'Failed to send notification to the associate.',
  },
  SERVICE_NOT_FOUND: {
    code: 'SERVICE_NOT_FOUND',
    status: 1012,
    message: 'Service not found for the provided serviceId.',
  },
  VEHICLE_DELIVERY_DATE_NOT_FOUND: {
    code: 'VEHICLE_DELIVERY_DATE_NOT_FOUND',
    status: 1013,
    message: 'Vehicle delivery date not found.',
  },
} as const;
