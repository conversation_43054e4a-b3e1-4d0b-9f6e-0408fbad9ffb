/* eslint-disable array-bracket-newline */
import { getTextToImages } from '../services/getTextToImages';
import Document from '../models/documentSchema';
import admissionRequestsMedia from '../models/admissionRequestsMedia';
import { AdmissionRequestMongo } from '../models/admissionRequestSchema';
import { EarningMongo } from '../models/earningSchema';
import { MetricMongo } from '../models/metricsSchema';
import { uploadFile, uploadToS3 } from '../aws/s3';
import type { AsyncController } from '../types&interfaces/types';
import { replaceDocWithUrl } from '../services/getPropertyWithUrls';
import { Types } from 'mongoose';
import { calculateEarningsAnalysis } from '../services/socialScoring/calculateEarningsAnalysis';
import { logger } from '../clean/lib/logger';

import { getUberProfileData } from '@/services/ocr/uberProfileExtractor';
import { getUberEarningsData } from '@/services/ocr/uberEarningsExtractor';
import { getDidiProfileData } from '@/services/ocr/didiProfileExtractor';
import { getDidiEarningsData } from '@/services/ocr/didiEarningsExtractor';
import { getLyftProfileData } from '@/services/ocr/lyftProfileExtractor';
import {
  AdmissionRequestStatus,
  AnalysisStatus,
  MLModels,
  Country,
  EntityType,
  ActionType,
} from '@/clean/domain/enums';
import { modelResultMongoAdapter } from '@/clean/domain/adapters';
import { repoSaveAllModelResults } from '@/clean/data/mongoRepositories';
import { CurrencyCode } from '@/constants';
import { getLyftEarningsData } from '@/services/ocr/lyftEarningsExtractor';
import { auditTrail } from '@/modules/AuditTrail/services/auditTrail.service';

// import AdmissionRequestBankOcr from '../models/admissionRequestBankOcr';

export const getIneData: AsyncController = async (req, res) => {
  const { image } = req.body;
  try {
    const text = await getTextToImages(image);
    return res.status(200).send(text);
  } catch (error: any) {
    console.error(error);
    return res.status(400).send({ message: error.message });
  }
};

export const saveVideo: AsyncController = async (req, res) => {
  const { requestId } = req.params;
  const video: Express.Multer.File | undefined = req.file;
  const platform = req.headers['x-platform'] as string;

  if (!video || !requestId || !platform) {
    return res.status(400).send({ message: 'Missing datas' });
  }
  try {
    console.log('iniciando proceso');
    const request = await AdmissionRequestMongo.findById(requestId);
    if (!request) {
      return res.status(404).send({ message: 'Request not found' });
    }
    await uploadFile(video, video.filename, `videos/request-${requestId}/`);
    const document = new Document({
      originalName: video.originalname,
      path: `videos/request-${requestId}/` + video.filename,
    });
    await document.save();
    const admissionRequestMedia = new admissionRequestsMedia({
      requestId,
      documentId: document._id,
    });
    await admissionRequestMedia.save();
    const videoUrl = await replaceDocWithUrl(document.id.toString());
    if (!videoUrl) {
      return res.status(404).send({ message: 'Document not found' });
    }
    const newAccount = {
      _id: new Types.ObjectId(),
      accountId: new Date().getTime().toString(),
      platform,
      earnings: {
        status: 'pending',
        _id: new Types.ObjectId(),
      },
      metrics: {
        status: 'pending',
        _id: new Types.ObjectId(),
      },
      status: 'pending',
    };

    await AdmissionRequestMongo.updateOne(
      { _id: requestId },
      {
        $push: {
          'palenca.accounts': newAccount,
          video: { platform, document },
        },
      }
    );

    return res.status(200).send({ message: 'Video saved', newAccount });
  } catch (error: any) {
    console.error(error);
    return res.status(400).send({ message: error.message });
  }
};

export const getEarningsAnalysis: AsyncController = async (req, res) => {
  const { requestId } = req.params;
  logger.info(`[getEarningsAnalysis] processing earning Analysis for userId ${requestId}`);
  const preca = await calculateEarningsAnalysis(requestId);
  logger.info(
    `[getEarningsAnalysis] successfully processed earning Analysis for userId ${requestId} and status is ${preca.earningsAnalysis.status}`
  );
  if (preca.earningsAnalysis.status) {
    await AdmissionRequestMongo.updateOne({ _id: requestId }, { status: 'documents_analysis' });
    return res.status(200).send({ message: 'success' });
  }
  return res.status(400).send({ message: 'Earning Analysis Status is not available' });
};

export const saveDriverProfileData: AsyncController = async (req, res) => {
  const { requestId } = req.params;
  const { earnings, acceptanceRate, cancellationRate, platform, rating, timeSinceFirstTrip, totalTrips } =
    req.body;
  const userId = req.authUser?.userId ?? '';
  if (
    !earnings ||
    !requestId ||
    !platform ||
    isNaN(acceptanceRate) ||
    isNaN(cancellationRate) ||
    isNaN(rating) ||
    isNaN(timeSinceFirstTrip) ||
    isNaN(totalTrips)
  ) {
    return res.status(400).send({ message: 'Missing data' });
  }
  try {
    const request = await AdmissionRequestMongo.findById(requestId);
    if (!request) {
      return res.status(404).send({ message: 'Request not found' });
    }
    if (request.palenca.accounts.some((account: any) => account.platform === platform)) {
      await AdmissionRequestMongo.updateOne(
        { _id: requestId },
        { $pull: { 'palenca.accounts': { platform } } }
      );
    }

    const existingEarnings = await EarningMongo.find({ requestId, platform });
    const savedEarnings = new Map();
    existingEarnings.forEach((earning) => {
      const dateKey = new Date(earning.earningDate).toISOString().split('T')[0];
      savedEarnings.set(dateKey, earning);
    });

    const isCountryUS = request.personalData.country === Country.us;

    for (const earning of earnings) {
      const earningDate = new Date(Date.parse(earning.week));
      const dateKey = earningDate.toISOString().split('T')[0];
      const existingEarning = savedEarnings.get(dateKey);

      if (existingEarning) {
        if (existingEarning.amount !== earning.earning) {
          await EarningMongo.updateOne({ _id: existingEarning._id }, { $set: { amount: earning.earning } });
          await auditTrail.logFieldChange({
            userId,
            entityId: requestId,
            entityType: EntityType.admission_request,
            actionType: ActionType['driver_earning.updated'],
            metadata: {
              field: 'amount',
              oldValue: existingEarning.amount,
              newValue: earning.earning,
              displayName: 'ganancia',
            },
            message: `${platform} ${earning.week} ganancia actualizado de ${existingEarning.amount} a ${earning.earning}`,
          });
        }
      } else {
        const earningData = new EarningMongo({
          amount: earning.earning,
          currency: isCountryUS ? CurrencyCode.usd : CurrencyCode.mxn,
          earningDate: earningDate,
          countTrips: 0,
          requestId,
          cashAmount: 0,
          platform: platform.toLocaleLowerCase(),
        });
        await earningData.save();

        if (earning.earning > 0) {
          await auditTrail.logFieldChange({
            userId,
            entityId: requestId,
            entityType: EntityType.admission_request,
            actionType: ActionType['driver_earning.added'],
            metadata: {
              field: 'amount',
              oldValue: null,
              newValue: earning.earning,
              displayName: 'ganancia',
            },
            message: `${platform} ${earning.week} ganancia agregada ${earning.earning}`,
          });
        }
      }
    }

    const savedMetric = await MetricMongo.findOne({ requestId, platform });

    if (savedMetric) {
      const updates: Partial<typeof savedMetric> = {};

      if (savedMetric.acceptanceRate !== acceptanceRate) {
        updates.acceptanceRate = acceptanceRate;
        await auditTrail.logFieldChange({
          userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          metadata: {
            field: 'acceptanceRate',
            oldValue: savedMetric.acceptanceRate,
            newValue: acceptanceRate,
            displayName: 'Tasa de aceptación',
          },
          message: `${platform} Tasa de aceptación actualizado de ${savedMetric.acceptanceRate} a ${acceptanceRate}`,
        });
      }
      if (savedMetric.cancellationRate !== cancellationRate) {
        updates.cancellationRate = cancellationRate;

        await auditTrail.logFieldChange({
          userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          metadata: {
            field: 'cancellationRate',
            oldValue: savedMetric.cancellationRate,
            newValue: cancellationRate,
            displayName: 'Tasa de cancelación',
          },
          message: `${platform} Tasa de cancelación actualizado de ${savedMetric.cancellationRate} a ${cancellationRate}`,
        });
      }
      if (savedMetric.rating !== rating) {
        updates.rating = rating;

        await auditTrail.logFieldChange({
          userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          metadata: {
            field: 'rating',
            oldValue: savedMetric.rating,
            newValue: rating,
            displayName: 'Clasificación',
          },
          message: `${platform} Clasificación actualizado de ${savedMetric.rating} a ${rating}`,
        });
      }
      if (savedMetric.lifetimeTrips !== totalTrips) {
        updates.lifetimeTrips = totalTrips;

        await auditTrail.logFieldChange({
          userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          metadata: {
            field: 'lifetimeTrips',
            oldValue: savedMetric.lifetimeTrips,
            newValue: totalTrips,
            displayName: 'Viajes totales',
          },
          message: `${platform} Viajes totales actualizado de ${savedMetric.lifetimeTrips} a ${totalTrips}`,
        });
      }
      if (savedMetric.timeSinceFirstTrip !== timeSinceFirstTrip) {
        updates.timeSinceFirstTrip = timeSinceFirstTrip;

        await auditTrail.logFieldChange({
          userId,
          entityId: requestId,
          entityType: EntityType.admission_request,
          actionType: ActionType['driver_metric.updated'],
          metadata: {
            field: 'timeSinceFirstTrip',
            oldValue: savedMetric.timeSinceFirstTrip,
            newValue: timeSinceFirstTrip,
            displayName: 'Tiempo desde el primer viaje',
          },
          message: `${platform} Tiempo desde el primer viaje actualizado de ${savedMetric.timeSinceFirstTrip} a ${timeSinceFirstTrip}`,
        });
      }

      if (Object.keys(updates).length > 0) {
        await savedMetric.updateOne({ $set: updates });
      }
    } else {
      await MetricMongo.create({
        platform: platform.toLowerCase(),
        requestId,
        acceptanceRate,
        cancellationRate,
        rating,
        lifetimeTrips: totalTrips,
        timeSinceFirstTrip,
        activationStatus: 'documents_analysis',
      });

      await auditTrail.logCustomEvent({
        userId,
        entityId: requestId,
        entityType: EntityType.admission_request,
        actionType: ActionType['driver_metric.added'],
        message: `${platform} métricas agregadas (aceptación: ${acceptanceRate}, cancelación: ${cancellationRate}, rating: ${rating}, viajes: ${totalTrips})`,
        metadata: { platform, acceptanceRate, cancellationRate, rating, totalTrips, timeSinceFirstTrip },
      });
    }

    const newAccount = {
      _id: new Types.ObjectId(),
      accountId: new Date().getTime().toString(),
      platform: platform.toLocaleLowerCase(),
      earnings: {
        status: 'success',
        _id: new Types.ObjectId(),
      },
      metrics: {
        status: 'success',
        _id: new Types.ObjectId(),
      },
      status: 'success',
    };
    await AdmissionRequestMongo.updateOne(
      { _id: requestId },
      { $push: { 'palenca.accounts': newAccount }, status: AdmissionRequestStatus.earnings_analysis }
    );
    // Prepare model results for batch saving
    const modelResults = [
      {
        modelName: MLModels.RIDESHARE_PERFORMANCE,
        result: {
          ...modelResultMongoAdapter(request.modelScores?.[MLModels.RIDESHARE_PERFORMANCE]),
          status: AnalysisStatus.pending,
        },
      },
      {
        modelName: MLModels.FINANCIAL_ASSESSMENT,
        result: {
          ...modelResultMongoAdapter(request.modelScores?.[MLModels.FINANCIAL_ASSESSMENT]),
          status: AnalysisStatus.pending,
        },
      },
    ];
    // Save all model results in a single database operation
    await repoSaveAllModelResults(requestId, modelResults);
    return res.status(200).send({ message: 'Earnings saved' });
  } catch (error: any) {
    console.error(error);
    return res.status(400).send({ message: error.message });
  }
};
// export const saveBankStatement: AsyncController = async (req, res) => {
//   const document: Express.Multer.File | undefined = req.file;

//   if (!document) {
//     return res.status(400).send({ message: 'Missing data: No file provided' });
//   }
//   const { requestId } = req.params;
//   const FilePath = document.path;

//   try {
//     const imageData = fs.readFileSync(FilePath);
//     if (!imageData) return res.status(400).send({ message: 'Missing data: No file provided' });

//     const response = await axios.post(OCR_SERVICE_URL, imageData, {
//       headers: {
//         'Content-Type': 'application/octet-stream',
//       },
//       auth: {
//         username: OCR_SERVICE_USER,
//         password: OCR_SERVICE_PASSWORD,
//       },
//     });
//     // const response = {
//     //   data: {
//     //     ErrorMessage: '',
//     //     OutputInformation: null,
//     //     AvailablePages: 917,
//     //     ProcessedPages: 11,
//     //     OCRText: [
//     //       [
//     //         'BBVA ARTURO RAMON FERREIRA MARTINEZ C ORIENTE 172 224 MOCTEZUMA 2A SECCION VENUSTIANO CARRANZA CIUDAD DE MEXICO MEXICO Información Financiera CP 15530 Rendimiento Saldo Promedio 491.57 Días del Periodo 31 Tasa Bruta Anual % 0.000 Saldo Promedio Gravable 0.00 Intereses a Favor (+) 0.00 ISR Retenido (-) 0.00 Comisiones Cheques pagados 0 0.00 Manejo de Cuenta 0.00 Total Comisiones 0.00 Cargos Objetados 0 0.00 Abonos Objetados 0 0.00 Detalle de Movimientos Realizados Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 1 / 11 Periodo DEL 13/01/2024 AL 12/02/2024 Fecha de Corte 12/02/2024 No. de Cuenta 1513873997 No. de Cliente 15838131 R.F.0 FEMA710131G25 No. Cuenta CLABE 012 180 01513873997 1 SUCURSAL: 0064 DIRECCION: PLAZA: TELEFONO: CDMX MOCTEZUMA AV. AVIACION CIVIL 1 COL. INDUSTRIAL PUERTO AEREO M CIUDAD DE MEXICO (5)2262425 MONEDA NACIONAL Comportamiento Saldo Anterior Depósitos / Abonos (+) 34 3.03 20,760.76 Retiros / Cargos (-) 26 20,429.28 Saldo Final 334.51 Saldo Promedio Mínimo Mensual: 0.00 Otros productos incluidos en el estado de cuenta (inversiones) Tasa de Contrato Producto Interes anual GAT GAT Total de Nominal Real comisiones ANTES DE IMPUESTOS N/A N/A N/A N/A N/A N/A FECHA OPER LIQ DESCRIPCION 13/ENE 15/ENE SPEI RECIBIDOBANAMEX 2024011 DIDI MOBILITY MEXICO SA DE CV 00002180002363330197 18D01A2D2BB97F2B DIDI MOBILITY MEXICO SA DE CV 13/ENE 15/ENE SPEI ENVIADO AZTECA 1301240gas y mona SALDO REFERENCIA CARGOS ABONOS OPERACION LIQUIDACION 800.00 Referencia 0107717646 002 800.00 Referencia 0052507061 127 La GAT Real es el rendimiento que obtendría después de descontar la inflación estimada BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México R.F.C. BBA830831U2 3.03 3.03 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 2 / 11 No. de Cuenta No. de Cliente 00004027665303938212 MBAN01002401150052507061 Nomina Azt 14/ENE 15/ENE SPEI RECIBIDOBANAMEX 300.00 2024011 DIDI MOBILITY MEXICO SA DE CV Referencia 0110929060 002 00002180002363330197 18D072B55CF1F1B6 DIDI MOBILITY MEXICO SA DE CV 14/ENE 15/ENE SPEI RECIBIDOBANAMEX 650.00 2024011 DIDI MOBILITY MEXICO SA DE CV Referencia 0111326164 002 00002180002363330197 18D08D65CFD6D94D DIDI MOBILITY MEXICO SA DE CV 14/ENE 15/ENE SPEI ENVIADO AZTECA 952.00 140124010mo tanda Referencia 0056057886 127 00004027665303938212 MBAN01002401150056057886 Nomina Azt 14/ENE 15/ENE SPEI RECIBIDOBANAMEX 300.00 2024011 DIDI MOBILITY MEXICO SA DE CV Referencia 0111596574 002 00002180002363330197 18D0938A1A6BOCOA DIDI MOBILITY MEXICO SA DE CV 14/ENE 15/ENE SPEI ENVIADO AZTECA 300.00 1401240comida Referencia 0056542636 127 00004027665303938212 MBAN01002401150056542636 Nomina Azt 15/ENE 15/ENE SPEI RECIBIDOJP MORGAN 701.87 0562484D idi payment Referencia 0113730064 110 00110180000776463752 IACH31 N09225SY DIDI MOBILITY MEXICO, S.A. DE C.V. 18/ENE 18/ENE SPEI ENVIADO AZTECA 700.00 1801240moto Referencia 0069499138 127 00004027665303938212 MBAN01002401180069499138 Nomina Azt 18/ENE 18/ENE SPEI RECIBIDOBANAMEX 300.00 2024011 DIDI MOBILITY MEXICO SA DE CV Referencia 0129201782 002 00002180002363330197 18D1F94ED52DA277 DIDI MOBILITY MEXICO SA DE CV 18/ENE 18/ENE SPEI RECIBIDOAZTECA 10.00 0159335Nomina Referencia 0129299308 127 00127180001067578416 2401190720764091121 FERREIRA MARTINEZ ARTURO RAMON 18/ENE 18/ENE SPEI ENVIADO SANTANDER 310.00 1801240cena Referencia 0071947233 014 00005579100415382231 MBAN01002401190071947233 Francisco Alejandro Aguirre BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 1513873997 15838131 1.03 3.03 702.90 702.90 2.90 2.90 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 3 / 11 No. de Cuenta No. de Cliente 1513873997 15838131 19/ENE 19/ENE SPEI RECIBIDOBANAMEX 250.00 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0130832980 002 00002180002363330197 18D22CCD3453CAE9 DIDI MOBILITY MEXICO SA DE CV 19/ENE 19/ENE SPEI ENVIADO AZTECA 250.00 1901240gas Referencia 0073718878 127 00004027665303938212 MBAN01002401190073718878 Nomina Azt 19/ENE 19/ENE SPEI RECIBIDOBANAMEX 1,770.47 1,773.37 1,773.37 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0133662042 002 00002180002363330197 18D24DC420F9C446 DIDI MOBILITY MEXICO SA DE CV 20/ENE 22/ENE SPEI ENVIADO AZTECA 1,772.00 1.37 1,773.37 2001240Gas Referencia 0075959517 127 00004027665303938212 MBAN01002401220075959517 Nomina Azt 21/ENE 22/ENE SPEI RECIBIDOAZTECA 1,005.00 0159335Nomina Referencia 0138531577 127 00127180001067578416 2401220721052104021 FERREIRA MARTINEZ ARTURO RAMON 21/ENE 22/ENE SPEI RECIBIDOBANAMEX 270.35 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0138551945 002 00002180002363330197 18D2E634683E18AB DIDI MOBILITY MEXICO SA DE CV 21/ENE 22/ENE SPEI ENVIADO BANORTE 1,275.00 1.72 1,773.37 2101240Retiro Referencia 0080365250 072 00004189143093326729 MBAN01002401220080365250 Arturo Ramon Ferreira Martnez 22/ENE 22/ENE SPEI RECIBIDOJP MORGAN 204.12 0788205Didi payment Referencia 0139595080 110 00110180000776463752 IACH31 Z093H9N8 DIDI MOBILITY MEXICO, S.A. DE C.V. 22/ENE 22/ENE SPEI ENVIADO BANORTE 205.00 0.84 0.84 2201240gas Referencia 0082228215 072 00004189143093326729 MBAN01002401220082228215 Arturo Ramon Ferreira Martnez 23/ENE 23/ENE SPEI RECIBIDOSTP 5,000.00 6728469MERCADO*PAGO Referencia 0144670228 646 00646017206867678981 CP071123249710 ALBERTO JACINTO MARTINEZ TORRES 23/ENE 23/ENE SPEI RECIBIDOBANAMEX 638.22 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0144810605 002 00002180002363330197 BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 4 / 11 18D38630415EE7BE DIDI MOBILITY MEXICO SA DE CV No. de Cuenta No. de Cliente 1513873997 15838131 23/ENE 23/ENE SPEI ENVIADO BANORTE 5,638.00 1.06 1.06 2301240Monte Referencia 0085728530 072 00004189143093326729 MBAN01002401230085728530 Arturo Ramon Ferreira Martnez 26/ENE 26/ENE PAGO CUENTA DE TERCERO 300.00 301.06 301.06 BNET 1171406655 Viernes 26 Referencia 5284311400 28/ENE 29/ENE SPEI RECIBIDOBANAMEX 185.79 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0160367311 002 00002180002363330197 18D4F635641179C6 DIDI MOBILITY MEXICO SA DE CV 28/ENE 29/ENE SPEI ENVIADO AZTECA 486.00 2801240Tanda Referencia 0099671395 127 00004027665303938212 MBAN01002401290099671395 Nomina Azt 28/ENE 29/ENE SPEI RECIBIDOBANAMEX 549.03 2024012DIDI MOBILITY MEXICO SA DE CV Referencia 0161186720 002 00002180002363330197 18D5194112A558DF DIDI MOBILITY MEXICO SA DE CV 28/ENE 29/ENE PAGO CUENTA DE TERCERO 400.00 BNET 0106666353 comida Referencia 5446741135 28/ENE 29/ENE SPEI ENVIADO BANORTE 149.80 0.08 301.06 2801240gas Referencia 0050281375 072 00004189143093326729 MBAN01002401290050281375 Arturo Ramon Ferreira Martnez 29/ENE 29/ENE SPEI RECIBIDOJP MORGAN 168.37 1021122Didi payment Referencia 0162925375 110 00110180000776463752 IACH32609512MP DIDI MOBILITY MEXICO, S.A. DE C.V. 29/ENE 29/ENE SPEI RECIBIDOJP MORGAN 27.00 1021032Didi payment Referencia 0162925344 110 00110180000776463752 IACH3260950T10 DIDI MOBILITY MEXICO, S.A. DE C.V. 29/ENE 30/ENE SPEI ENVIADO BANORTE 139.00 56.45 195.45 2901240cena Referencia 0054348727 072 00004189143093326729 MBAN01002401300054348727 Arturo Ramon Ferreira Martnez 31/ENE 31/ENE SPEI ENVIADO BANORTE 50.00 3101240gas Referencia 0058936670 072 00004189143093326729 MBAN01002401310058936670 Arturo Ramon Ferreira Martnez 31/ENE 31/ENE SPEI RECIBIDOBANAMEX 500.00 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0173118363 002 BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 5 / 11 00002180002363330197 18D6ODB15DBCAB3E DIDI MOBILITY MEXICO SA DE CV No. de Cuenta No. de Cliente 1513873997 15838131 31/ENE 31/ENE SPEI ENVIADO BANORTE 500.00 6.45 6.45 3101240comida birthday Referencia 0060602232 072 00004189143093326729 MBAN01002401310060602232 Arturo Ramon Ferreira Martnez 01/FEB 01/FEB SPEI RECIBIDOBANORTE 100.00 0240201 Transferencia Referencia 0177886187 072 00072078012470862208 3843CP05202402012824820909 ARTURO RAMON FERREIRA MARTINEZ 01/FEB 01/FEB PAGO CUENTA DE TERCERO 100.00 BNET 2693969916 SINDO Referencia 5777945116 01/FEB 01/FEB PAGO CUENTA DE TERCERO 1,000.00 1,006.45 1,006.45 BNET 1565470247 Renta de Jess Referencia 5785038909 03/FEB 06/FEB SPEI RECIBIDOBANAMEX 2,140.00 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0188168096 002 00002180002363330197 18D7178FD1D1EA26 DIDI MOBILITY MEXICO SA DE CV 03/FEB 06/FEB SPEI RECIBIDOBANORTE 93.50 0240203Transferencia Referencia 0188288310 072 00072078012470862208 3843CP01202402032831217775 ARTURO RAMON FERREIRA MARTINEZ 03/FEB 06/FEB SPEI ENVIADO AZTECA 579.00 2,660.95 1,006.45 0302240Totalplay Referencia 0073301684 127 00004027665303938212 MBAN01002402060073301684 Nomina Azt 05/FEB 06/FEB SPEI RECIBIDOBANAMEX 481.70 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0191449946 002 00002180002363330197 18D7969E5E4D19C3 DIDI MOBILITY MEXICO SA DE CV 05/FEB 06/FEB PAGO CUENTA DE TERCERO 3,000.00 BNET 1598900668 Renta atraso Referencia 6112393261 05/FEB 06/FEB SPEI ENVIADO BANORTE 142.50 0.15 1,006.45 0502240gas Referencia 0076517073 072 00004189143093326729 MBAN01002402060076517073 Arturo Ramon Ferreira Martnez 07/FEB 07/FEB SPEI RECIBIDOBANAMEX 400.00 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0199175789 002 00002180002363330197 18D8508335A8CA96 DIDI MOBILITY MEXICO SA DE CV 07/FEB 07/FEB MOVISTAR 322.98 77.17 77.17 REF:00051000000981776316 CIE:0657409 Referencia GUIA:9442230 08/FEB 08/FEB SPEI RECIBIDOBANAMEX 423.00 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0101206093 002 BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 6 / 11 00002180002363330197 18D87879B093EEDB DIDI MOBILITY MEXICO SA DE CV No. de Cuenta No. de Cliente 1513873997 15838131 08/FEB 08/FEB PAGO CUENTA DE TERCERO 27.00 BNET 2752460122 didi Referencia 6373256170 08/FEB 08/FEB RETIRO CAJERO AUTOMATICO 300.00 FEB08 15:21 BBVA B850 FOL10:9948 Referencia ******7420 08/FEB 08/FEB SPEI ENVIADO STP 225.00 0802240Pago prestamo Yoya Referencia 0087281856 646 00646180272155014419 MBAN01002402090087281856 DiDi Prestamo 08/FEB 09/FEB DEPOSITO EFECTIVO PRACTIC 170.00 FEB08 23:58 PRAC D940 FOL10:4629 Referencia ******3997 08/FEB 09/FEB SPEI RECIBIDOBANORTE 86.00 258.17 2.17 0240208Transferencia Referencia 0104807436 072 00072078012470862208 3843CP06202402082840353313 ARTURO RAMON FERREIRA MARTINEZ 09/FEB 09/FEB SPEI ENVIADO STP 255.00 0902240pago didi novia Referencia 0087956196 646 00646180272155014419 MBAN01002402090087956196 DiDi Prestamo 09/FEB 09/FEB SPEI RECIBIDOBANAMEX 471.27 2024020DIDI MOBILITY MEXICO SA DE CV Referencia 0104851088 002 00002180002363330197 18D8CAD67034F400 DIDI MOBILITY MEXICO SA DE CV 09/FEB 09/FEB SPEI ENVIADO STP 78.00 396.44 396.44 0902240pago novia Referencia 0088469282 646 00646180272155014419 MBAN01002402090088469282 DiDi Prestamo 11/FEB 12/FEB SPEI RECIBIDOBANAMEX 775.00 2024021DIDI MOBILITY MEXICO SA DE CV Referencia 0112866872 002 00002180002363330197 18D98DC8E3735418 DIDI MOBILITY MEXICO SA DE CV 11/FEB 12/FEB SPEI RECIBIDOAZTECA 335.00 0159335Nomina Referencia 0113405991 127 00127180001067578416 2402120723369136661 FERREIRA MARTINEZ ARTURO RAMON 11/FEB 12/FEB PAGO CUENTA DE TERCERO 1,500.00 6.44 396.44 BNET 2978129363 adelanto Referencia 6646772506 12/FEB 12/FEB SPEI RECIBIDOJP MORGAN 328.07 334.51 334.51 1411927Didi payment Referencia 0115134958 110 00110180000776463752 IACH32K097STSG DIDI MOBILITY MEXICO, S.A. DE C.V. BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Total de Movimientos Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 7 / 11 No. de Cuenta No. de Cliente 1513873997 15838131 TOTAL IMPORTE CARGOS TOTAL IMPORTE ABONOS 20,429.28 TOTAL MOVIMIENTOS CARGOS 26 20,760.76 TOTAL MOVIMIENTOS ABONOS 34 Le informamos que puede consultar, en cualquier momento, el contrato correspondiente a esta cuenta en nuetra página de internet https://www.bbva.mx, en el apartado del producto contratado. BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         '1 1 BBVA Cuadro resumen y gráfico de movimientos del período Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 8 / 11 No. de Cuenta No. de Cliente 1513873997 15838131 Concepto Cantidad Porcentaje Columna Saldo Inicial 3.03 0.01% A Depósitos / Abonos (+) 20,760.76 100.00% B Comisiones (-) 0.00 0.00% C Intereses a favor (+) 0.00 0.00% D Retiros efectivo (-) -300.00 -1.44% E Otros cargos (-) -20,129.28 -96.95% F Saldo Final 334.51 1.61% G 100 % B ( + ) A C D E F G Nota : En la columna "porcentaje" se señala con el 100% a la cantidad más alta, permitiéndole relacionarse porcentualmente con las demás. Otros cargos: Ver detallede movimientos Los montos mínimos requeridos para los productos de inversión a plazo fijo son: Pagaré Liquidable al vencimiento MN. $2,000.00, Certificado de Depósitos MN: $5,000.00(sujetas a cambio dependiendo de las variaciones del mercado). Para mayor información consulta la página de internet: https://www.bbva.mx BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 9 / 11 No. de Cuenta No. de Cliente 1513873997 15838131 Tiene 90 días naturales contados a partir de la fecha de corte o de la realización de la operación para presentar su aclaración en la sucursal donde radica su cuenta, o bien, llamando al Centro de Atención Telefónica al teléfono 55 5226 2663. Con gusto atenderemos sus reclamaciones que ha presentado ante nuestra institución a través de Línea BBVA al teléfono 55 5226 2663 Ciudad de México, en caso de no recibir una respuesta satisfactoria dirigirse a: Unidad Especializada de Atención a Clientes (UNE) BBVA recibe las consultas, reclamaciones o aclaraciones, en su Unidad Especializada de Atención a Usuarios, ubicada en Lago Alberto 320 (entrada por Mariano Escobedo 303), Colonia Granada, Código Postal 11320, Alcaldía Miguel Hidalgo, Ciudad de México, México y por correo electrónico <EMAIL> o teléfono 55 1998 8039, así como en cualquiera de sus sucursales u oficinas. En el caso de no obtener una respuesta satisfactoria, podrá acudir a la Comisión Nacional para la Protección y Defensa de los Usuarios de Servicios Financieros www.condusef.gob.mx y 55 5340 0999. 01~% ...Z.1%  ~ ,1 pm El, . .■  wrifi]udo took.Ñ LA mame mob .L ~4 MICW5 "Si desea recibir pagos a través de transferencias electrónicas de fondos interbancarias, deberá hacer del conocimiento de la persona que le enviará el o los pagos respectivos, el número de Cuenta que a continuación se índica: 012 180 01513873997 1 Clave Bancaria Estándar (CLABE), así como el nombre de este Banco." Todas las tasas de interés están expresadas en terminos anuales. "Únicamente están garantizados por el Instituto de Protección al Ahorro Bancarios (IPAB), los depósitos bancarios de dinero a la vista, retirables en días preestablecidos, de ahorro, y a plazo con previo aviso, así como los préstamos y créditos que acepte la Institución, hasta por el equivalente a cuatrocientas mil UDIS por persona, cualquiera que sea el número, tipo y clase de dichas obligaciones a su favor y a cargo de la Institución de banca múltiple." www.ipab.org.mx BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA Glosario de Abreviaturas Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 10 / 11 No. de Cuenta No. de Cliente 1513873997 15838131 ADMON ADMINISTRACION DEP DEPOSITO MN MONEDA NACIONAL ANT ANTERIOR DESC/ DESCTO DESCUENTO MOV MOVIMIENTO ANTIC ANTICIPADA DEV/ DEVOL DEVOLUCION MOVMTOS MOVIMIENTOS ANUL ANULACION DIF DIFERENCIA MDB MULTIDEPOSITO APORT APORTACION DIN DINERO N/A NO APLICA AUT AUTOMATICO DISP DISPOSICION OPER OPERACION BCA BANCA DLLS DOLARES OPS OPERACIONES BCOS BANCOS DOC DOCUMENTO ORD ORDEN BMOV BBVA MÉXICO ELECT ELECTRONICA P/ PAG PAGO BONIF BONIFICACION EMP EMPRESARIAL PAT PATRIMONIAL COD. CODIGO DE LEYENDA EXTEM EXTEMPORANEA REDESC REDESCUENTO CAJ CAJERO EXT EXTRANJERO RFC REGISTRO FEDERAL DE CANC CANCELACION FALLEC FALLECIMIENTO CONTRIBUYENTES CGO CARGO FALT FALTANTE REF. REFERENCIA CW CASH WINDOWS GAT GANANCIA ANUAL TOTAL RESP RESPONSABILIDAD CH/CHQ CHEQUE GAR/GTIA GARANTIA RET RETIRO CI COBRO INMEDIATO GPO GRUPO REV REVERSO COMER COMERCIO HONOR HONORARIOS SBC SALVO BUEN COBRO COM COMISION IVA IMPUESTO AL VALOR AGREGADO SEG SEGURO CIE CONCENTRACION INMEDIATO ISR IMPUESTO SOBRE LA RENTA SERV SERVICIO CONF EMPRESARIAL INDEMN INDEMNIZACION SOBR SOBREGIRO CONS CONFIRMACION INF INFORMACION SOC SOCIEDADES CONV CONSULTA INSP INSPECCION TARJ TARJETA CORREC CONVENIO INT INTERESES TDC TARJETA DE CREDITO CRED CORRECCION INTS INTERESES TDE TARJETA DE DEBITO EMPRESARIAL CTA CREDITO INT/ INTNAL INTERNACIONAL TPV TERMINAL PUNTO DE VENTA CED CUENTA INV INVERSION TIB TESORERIA INTEGRAL BANCARIA CUENTA EN DOLARES LIQ LIQUIDACION TRANS TRANSFERENCIA DCD DINAMICA DE CONVERSION MP MARCA PROPIA TRASP TRASPASO DE DIVISAS MDO MERCADO VTAS VENTAS BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 ',
//     //         'BBVA No. de Cuenta No. de Cliente Estado de Cuenta Libretón Básico Cuenta Digital PAGINA 11 / 11 1513873997 15838131 Cuida el medio ambiente consultando tu estado de cuenta en www.bbva.mx recuerda que el medio ambiente es responsabilidad de todos Nombre del Receptor : ARTURO RAMON FERREIRA MARTINEZ Código Postal de Domicilio Fiscal : 15530 Régimen Fiscal : 605 - Sueldos y Salarios e Ingresos Asimilados a Salarios Uso de CFDI : S01 - Sin efectos fiscales. Exportación : 01 - No aplica Folio Fiscal: Certificado 1 D96D7BA-9089-4E8D-8976-4D5B2D9EF5C0 00001000000509478830 Sello Digital IYoq u6Tqg LI B FAS6nvbqj/OnveWN NxAneF/zH PQWI2JvtRoDu M/N h noyu nTNU 8q pxc2L4XeE pzF H N H7/uY-FE0OHAQ/2u h Uz+ FEXk/022EYzKIWgwT/tn4xwqcFn49Md mDB M b HVUGaw9n IsWeTAL5CU pSi30i i IV/-FJqld5 F40dvzycoTK8u I bEXm-Fwf eX8u7JHvAj1X9Fky/HA4G02vm/7BXAC-FsdqP0YzBmKe89CUFC0PdLRjMfTdk919w/W7VXBJqss1IgHn1y10jMdxH56a5 HwJWc+p24qvl nfm Pq3zsh I lg izdj rGTHS Bd NK124czn1wA3yKII0J3FUYUa9WEA= Sello SAT hD660K9oKbpKWaLcMSwolkToh78E1hDI IwF4YFWKxz0hBaQj88aK5FkABryWxDF03vBFPkJ/zg2U/op7vpMpFhPM13QRt jzp/iwdg Bg PgkWrJVBm7u N/eZK7q5o5f2dTbu ms-FvD0de/Fz4S pQq8Bw03q IyHC59wKDAD nclle7U M/Zn FgOwzu/c4N F5 aCiMIGUvsyxxLnr6IUYPuz0K80EznEMZG2qYvYXcBEVfqsxV4sCnhRdot3oqfLkDSRRKQFncw4U9a3GlarkVNhbHA3zY HebNOXSkx4bM08Y0g8LyB4eRaWDY1kMDPOjd/PWws0EL/pQHrwKpU3Rd7bX2mQ= No. de Serie del Certificado del SAT: 00001000000509846663 Fecha y hora de certificación:2024-02-13T17:58:31 Cadena Original del complemento de certificacion digital del SAT: W1.111 D96D7BA-9089-4E8D-8976-4D5B2D9EF5C012024-02-13T17:58:3111Yoq_u6TqgLIB FAS6nvbqj/OnveWNNxAneF/zH P QWI2JvtRoDu M/N h noyu nTNU8q pxc2L4XeE pzF H N H7/uY+E0OHAQ/2u h Uz+ FEXk/022EYzKIWgwT/tn4xwqcFn49Md m DB M b HVUGaw9n IsWeTAL5CUpS i30i i IV/-FJq Id5 F40dvzycoTK8u I bEXm-FwfeX8u7J HvAj1X9 F ky/HA4G02vm/7BXAC-FsdqPOY zBmKe89CUFC0PdLRjMfTdk919w/W7VXBJgss11g Hn1y10jMdxH56a5 HwJWc-F p24qvl nfmPq3zshl1gizdj rGTHSBdNKI24cz nlwA3yKII0J3FUYUa9WEA=100001000000509846663]1 Este documento es una representación impresa de un CFDI. Emitido en Ciudad de México, México a 13 de Febrero de 2024 a las 10:31:06 "Estimado Cliente, le informamos que por Disposición Oficial a partir del 11 de enero de 2018, si realiza transferencias de fondos nacionales en moneda extranjera y transferencias de fondos internacionales, BBVA está obligado a compartir con otras Entidades Financieras la información correspondiente a esa operación con fines de consulta, por lo que si efectúa dichas operaciones se entenderá que otorga su consentimiento para ello." (Sólo aplica para personas Físicas) Estimado cliente: Ponemos a su disposición la última versión del Aviso de Privacidad en www.bbva.mx o en cualquiera de nuestras sucursales. BBVA MEXICO, S.A., INSTITUCION DE BANCA MULTIPLE, GRUPO FINANCIERO BBVA MEXICO Régimen Fiscal: Av. Paseo de la Reforma 510, Col. Juárez, Alcaldía Cuauhtémoc; C.P. 06600, Ciudad de México, México, R.F.C. BBA830831U2 Régimen General de la Ley Personas Morales ',
//     //       ],
//     //     ],
//     //     OutputFileUrl:
//     //       'https://ff8eaf8ceb86346cc0617ddd3b042e2b4bde42aa013ad1a1e05851c-apidata.googleusercontent.com/download/storage/v1/b/ocrwebservice/o/bd50_102ed6f0-2e92-43d3-8baf-c4c4bb310e27.pdf?jk=AYBlUPASqdVpI1fUvwdSSjxJ5Kl1n3JPsMZoTmemtoMOOGuwt7e8UOYH_eEk2VLE9mfI7DsMY1zoIZM8CAk-zS_FKDtXIGduoYcJWMHd481i0eCfkOutTEmtXyz2a4nks8q8nM9Kcw6o2QO4cj8BMYotwzlpqYeQLxcEOsYGOa_unz4ejdK1I8AwKUNaNwJL63JFtqdQdMwUu1ebtmCL7LsbbXm2P5mnuk5ufJffA8OdkQu2QimypQp51fBXLJnVi_3fVBplJ1paNjeOz3lgqHxO5Qbojc1o3FIkisOP4-mhKC6YCNDIXET7SQk-Gc86bqwK8a4hsgBK6kb_7MPVgzfCocj7vFAzTzycohlB0Z8lpUFO8y5zJchETxGiVrw8nrtfcPkOd76llWp6qvwiOQZCsmHAF65UyVjvw4WX1XjvS_-w-6ZifOulgOyDRvodLqpl2HRt1G8TliUx6XZHm9zmBq0J4Lb8sfB3KbdCwkLVbaDEf9eiP5hMkzwi4zfTfNl3xdDxXY7Z04AiCCq1yMJBDQuvAcRual-w4LcWdu2BI3K-r1R_HQW90bfCbz5o0UDyn7m4mTt4GdLwejrSx9EfW14E2P-Hhfdx2h0OvpaGAaUYPcy_jDqKpoezpnB9c7MRIgE_JOTQbMJP-JVw7ftiyomXQ5vXg_6DCB3QLYzZhD2RvfUZDglw-QWEWYMnxEijrTlWLbmdkBL8PwePuQknaRt3SibxPOiZBMe_INeyp5UzNC5l81B-PV3FP35IyqDkhBqLp0JiI81w__iGIh6jocbLzcmKBHegUAKChGQ7OXQ5MAaG1YAfkrfaeQGP9D4QHrSsKtbuUjKRUK4mf6AjNqF5ostIF2bv2bPHBJ0AqOH_YJQz3y7eKbIFz7RkUMwVzF3XdQHQsvJZPolvOazIdJpm3HzIbRI07n82pc9bGfxGbdxfVbnYPqDOlm_WbXYArElXd_pDk-3XnW2TUw9WMwDshCDWr6GxXcBT_3vWEYXrBtNfA_quYDI6EAPfIL5uWByZYFy_lOQKAVSsYyulIUDtOnBpWtFVFoD7YcevNiiG79E10Jbo-_BTkb1REaR0Tr2gtn7WiQpDqpiKWNEXTuD37nY7Q0DM1uHDJC5cxa_ziR7m1VtDD530DoDhl9XWlQsq_jWilrmnG9jKUgtz_wPs4Pk&isca=1',
//     //     OutputFileUrl2: '',
//     //     OutputFileUrl3: '',
//     //     Reserved: [],
//     //     OCRWords: [],
//     //     TaskDescription: null,
//     //   },
//     // };

//     if (response.status === 401) {
//       // Please provide valid username and license code
//       console.error('Unauthorized request');
//       return res.status(401).send({ message: 'Unauthorized request' });
//     }

//     // Decode Output response
//     const jobj = response.data;

//     const ocrError: string = jobj.ErrorMessage;

//     if (ocrError !== '') {
//       // Error occurs during recognition
//       console.error('Recognition Error: ' + ocrError);
//       return res.status(400).send({ message: ocrError });
//     }

//     const findPhrases = (data: { data: { OCRText: string[][] } }): string[] => {
//       const phrasesToFind = [
//         'DEMERGE MEXICO SA DE CV',
//         'UBR PAGOS MEXICO SA DE CV',
//         'DIDI MOBILITY MEXICO SA DE CV',
//       ];
//       const foundPhrases: string[] = [];
//       data.data.OCRText.forEach((page) => {
//         page.forEach((line) => {
//           phrasesToFind.forEach((phrase) => {
//             if (line.includes(phrase)) {
//               foundPhrases.push(phrase);
//             }
//           });
//         });
//       });
//       return foundPhrases;
//     };

//     const foundPhrases = findPhrases(response);

//     if (foundPhrases.length > 0) {
//       await AdmissionRequestBankOcr.findByIdAndUpdate(
//         { requestId },
//         { allTransactions: response.data.OCRText }
//       );
//       axios.post('https://us-central1-onecarnow-dcf84.cloudfunctions.net/bank-to-json', {
//         url: response.data.OutputFileUrl,
//       });

//       return res.status(200).send({ message: 'Found phrases', foundPhrases });
//     }
//     return res.status(404).send({ message: 'No phrases found' });
//   } catch (error: any) {
//     console.error('An error occurred:', error);
//     return res.status(400).send({ message: error.message });
//   }
// };

// export const ocrWebhook: AsyncController = async (req, res) => {
//   const { data, requestId, step } = req.body;
//   if (!data || !requestId || !step) {
//     return res.status(400).send({ message: 'Data not found' });
//   }

//   const id = requestId; // Suponiendo que requestId es un string válido de ObjectId
//   await AdmissionRequestBankOcr.findById(id);

//   if (!id) {
//     return res.status(404).send({ message: 'Request not found' });
//   }

//   switch (step) {
//     case 'allTransactions':
//       await AdmissionRequestBankOcr.findByIdAndUpdate(
//         requestId,
//         { allTransactions: data },
//         { upsert: true, new: true }
//       );
//       break;
//     case 'earnings':
//       await AdmissionRequestBankOcr.findByIdAndUpdate(
//         requestId,
//         { earnings: data },
//         { upsert: true, new: true }
//       );
//       break;
//     case 'spending':
//       await AdmissionRequestBankOcr.findByIdAndUpdate(
//         requestId,
//         { spending: data },
//         { upsert: true, new: true }
//       );
//       break;
//     case 'platforms':
//       const result = await AdmissionRequestBankOcr.findByIdAndUpdate(
//         requestId,
//         { platforms: { data } },
//         { upsert: true, new: true }
//       );
//       console.log(result);
//       break;
//   }
//   return res.status(200).send({ message: 'Webhook received' });
// };
export const getVideo: AsyncController = async (req, res) => {
  try {
    const { requestId, platform } = req.params;
    if (!requestId) {
      return res.status(400).json({ message: 'Missing requestId' });
    }

    const request = await AdmissionRequestMongo.findById(requestId);
    if (!request) {
      return res.status(404).json({ message: 'Request not found' });
    }

    if (!request.video || !Array.isArray(request.video) || request.video.length === 0) {
      return res.status(404).json({ message: 'No video data found for this request' });
    }
    const platformData = request.video.find((p: { platform: string }) => p.platform === platform);

    if (!platformData) {
      return res.status(404).json({ message: 'Platform not found for this request' });
    }

    const urlData = await replaceDocWithUrl(platformData.document._id);

    if (!urlData) {
      return res.status(404).json({ message: 'URL not found for this document' });
    }

    return res.status(200).json({ url: urlData.url });
  } catch (error: any) {
    console.error('Error fetching video:', error);
    return res.status(500).json({ message: 'Error fetching video' });
  }
};

const parseTimeUsingApp = (value: string) => {
  if (!value || typeof value !== 'string') return 0;

  const lowerValue = value.toLowerCase();

  // Handle "New" or invalid input
  if (lowerValue === 'new') {
    return 0; // or return 0, based on your requirements
  }

  const regex = /(\d+(\.\d+)?)\s*(year|years|month|months|day|days)/i;
  const match = lowerValue.match(regex);

  if (!match) return 0;

  const numericValue = parseFloat(match[1]);
  const unit = match[3].toLowerCase();

  switch (unit) {
    case 'year':
    case 'years':
      return numericValue;
    case 'month':
    case 'months':
      return numericValue / 12; // Convert months to years
    case 'day':
    case 'days':
      return numericValue / 365; // Convert days to years
    default:
      return 0; // Shouldn't reach here
  }
};

const saveUberDriverMetrics = async (platform: string, requestId: string, result: any) => {
  //check if record already exist then modify existing record
  if (!result.rating || !result.completedTrips || !result.timeUsingApp) {
    throw new Error('Invalid Data');
  }
  if (result) {
    const existingMetric = await MetricMongo.findOne({ requestId: requestId, platform: platform });
    if (existingMetric) {
      existingMetric.acceptanceRate = result.acceptanceRate ? parseFloat(result.acceptanceRate) : 0;
      existingMetric.rating = result.rating ? parseFloat(result.rating) : 0;
      existingMetric.lifetimeTrips = result.completedTrips ? parseInt(result.completedTrips) : 0;
      existingMetric.timeSinceFirstTrip = result.timeUsingApp ? parseTimeUsingApp(result.timeUsingApp) : 0;
      existingMetric.cancellationRate = result.cancellationRate ? parseFloat(result.cancellationRate) : 0;
      existingMetric.save();
      logger.info({
        message: 'Existing Uber Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveUberDriverMetrics',
        earning: JSON.stringify(existingMetric),
      });
    } else {
      const metric = await MetricMongo.create({
        platform,
        requestId,
        acceptanceRate: result.acceptanceRate ? parseFloat(result.acceptanceRate) : 0,
        cancellationRate: result.cancellationRate ? parseFloat(result.cancellationRate) : 0,
        rating: result.rating ? parseFloat(result.rating) : 0,
        lifetimeTrips: result.completedTrips ? parseInt(result.completedTrips) : 0,
        timeSinceFirstTrip: result.timeUsingApp ? parseTimeUsingApp(result.timeUsingApp) : 0,
        activationStatus: 'documents_analysis',
      });
      logger.info({
        message: 'New Uber Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveUberDriverMetrics',
        earning: JSON.stringify(metric),
      });
    }
  }
};

const getMonday12WeeksAgoUTC = () => {
  const now = new Date();

  // Calculate 12 weeks ago
  const weeksAgo = 12 * 7; // 12 weeks in days
  const date12WeeksAgo = new Date(now.getTime() - weeksAgo * 24 * 60 * 60 * 1000);

  // Get the day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const dayOfWeek = date12WeeksAgo.getUTCDay();

  // Calculate how many days to subtract to get to the previous Monday (UTC)
  const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

  // Adjust to the previous Monday
  date12WeeksAgo.setUTCDate(date12WeeksAgo.getUTCDate() - daysToMonday);
  date12WeeksAgo.setUTCHours(0, 0, 0, 0);
  // Return the adjusted date
  return date12WeeksAgo;
};

const getLastSundayUTC = () => {
  const now = new Date();

  // Get the day of the week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
  const dayOfWeek = now.getUTCDay();

  // Calculate how many days to subtract to get to the last Sunday
  const daysToLastSunday = dayOfWeek === 0 ? 0 : dayOfWeek;

  // Adjust the date to the last Sunday
  const lastSunday = new Date(now);
  lastSunday.setUTCDate(now.getUTCDate() - daysToLastSunday);

  // Return the adjusted date
  return lastSunday;
};

const convertStringToDate = (dateString: string): Date => {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day);
};

const checkIfDateIsInBetween = (startDate: Date, endDate: Date, searchDate: Date) => {
  // Check if the date is between (or equal to) the start and end dates
  return searchDate.getTime() >= startDate.getTime() && searchDate.getTime() <= endDate.getTime();
};

const saveUberDriverEarnings = async (platform: string, requestId: string, data: any) => {
  const mondayThirteenWeeksAgo = getMonday12WeeksAgoUTC();
  const lastSunday = getLastSundayUTC();
  if (!data || data?.length === 0) {
    throw new Error('Invalid Data');
  }
  const request = await AdmissionRequestMongo.findById(requestId).select('personalData.country');
  const isCountryUS = request?.personalData?.country === Country.us;

  let dataIsStale = true;
  for (const value of data) {
    const startInMilliSeconds = new Date(value.startDate);
    const startDate = new Date(startInMilliSeconds);
    if (startDate >= mondayThirteenWeeksAgo) {
      dataIsStale = false;
      break;
    }
  }
  if (dataIsStale) {
    throw new Error('Invalid Data');
  }
  for (const value of data) {
    const date = new Date(value.startDate);
    if (checkIfDateIsInBetween(mondayThirteenWeeksAgo, lastSunday, date)) {
      //check if earning record already exists for that week
      const existingEarning = await EarningMongo.findOne({
        platform: platform,
        requestId: requestId,
        earningDate: date.toISOString(),
      });
      if (existingEarning) {
        existingEarning.amount = value.amount;
        await existingEarning.save();
        logger.info({
          message: 'Existing Uber Earning Updated',
          requestId: requestId,
          platform: platform,
          method: 'saveUberDriverEarnings',
          earning: JSON.stringify(existingEarning),
        });
      } else {
        const earning = new EarningMongo({
          amount: value.amount,
          currency: isCountryUS ? CurrencyCode.usd : CurrencyCode.mxn,
          earningDate: date.toISOString(),
          countTrips: 0,
          requestId,
          cashAmount: 0,
          platform,
        });
        await earning.save();
        logger.info({
          message: 'New Uber Earning Saved',
          requestId: requestId,
          platform: platform,
          method: 'saveUberDriverEarnings',
          earning: JSON.stringify(earning),
        });
      }
    }
  }
};

const saveDidiDriverMetrics = async (platform: string, requestId: string, result: any) => {
  //check if record already exist then modify existing record
  if (isNaN(result?.rating) || isNaN(result?.passengersServed) || isNaN(result?.yearsActive)) {
    throw new Error('Invalid Data');
  }
  if (result) {
    const existingMetric = await MetricMongo.findOne({ requestId: requestId, platform: platform });
    if (existingMetric) {
      existingMetric.acceptanceRate = result?.acceptanceRate;
      existingMetric.rating = result?.rating;
      existingMetric.lifetimeTrips = result?.passengersServed;
      existingMetric.timeSinceFirstTrip = result?.yearsActive;
      existingMetric.cancellationRate = 100 - result?.completedTripsRate;
      existingMetric.save();
      logger.info({
        message: 'Existing Didi Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveDidiDriverMetrics',
        earning: JSON.stringify(existingMetric),
      });
    } else {
      const metric = await MetricMongo.create({
        platform,
        requestId,
        acceptanceRate: result?.acceptanceRate,
        cancellationRate: 100 - result?.completedTripsRate,
        rating: result?.rating,
        lifetimeTrips: result?.passengersServed,
        timeSinceFirstTrip: result?.yearsActive,
        activationStatus: 'documents_analysis',
      });
      logger.info({
        message: 'New Didi Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveDidiDriverMetrics',
        earning: JSON.stringify(metric),
      });
    }
  }
};

const saveDidiDriverEarnings = async (platform: string, requestId: string, data: any) => {
  const mondayThirteenWeeksAgo = getMonday12WeeksAgoUTC();
  const lastSunday = getLastSundayUTC();
  if (!data?.weeklyEarnings || data?.weeklyEarnings?.length === 0) {
    throw new Error('Invalid Data');
  }
  let dataIsStale = true;
  for (const value of data?.weeklyEarnings) {
    const startInMilliSeconds = new Date(value.startDate);
    const startDate = new Date(startInMilliSeconds);
    if (startDate >= mondayThirteenWeeksAgo) {
      dataIsStale = false;
      break;
    }
  }
  if (dataIsStale) {
    throw new Error('Invalid Data');
  }
  for (const value of data.weeklyEarnings) {
    //check if earning record already exists for that week
    const date = convertStringToDate(value.startDate);
    if (checkIfDateIsInBetween(mondayThirteenWeeksAgo, lastSunday, date)) {
      const existingEarning = await EarningMongo.findOne({
        platform: platform,
        requestId: requestId,
        earningDate: date.toISOString(),
      });
      if (existingEarning) {
        existingEarning.amount = value.amount;
        existingEarning.save();
        logger.info({
          message: 'Exsiting Didi Earning Upaded',
          requestId: requestId,
          platform: platform,
          method: 'saveDidiDriverEarnings',
          earning: JSON.stringify(existingEarning),
        });
      } else {
        const earning = new EarningMongo({
          amount: value.amount,
          currency: 'mxn',
          earningDate: date.toISOString(),
          countTrips: 0,
          requestId,
          cashAmount: 0,
          platform,
        });
        await earning.save();
        logger.info({
          message: 'New Didi Earning Saved',
          requestId: requestId,
          platform: platform,
          method: 'saveDidiDriverEarnings',
          earning: JSON.stringify(earning),
        });
      }
    }
  }
};

const addNewPalancaAccount = async (requestId: string, platform: string, document: any) => {
  const newAccount = {
    _id: new Types.ObjectId(),
    accountId: new Date().getTime().toString(),
    platform,
    earnings: {
      status: 'pending',
      _id: new Types.ObjectId(),
    },
    metrics: {
      status: 'pending',
      _id: new Types.ObjectId(),
    },
    status: 'pending',
  };
  await AdmissionRequestMongo.updateOne(
    { _id: requestId },
    {
      $addToSet: {
        'palenca.accounts': newAccount, // This will add `newAccount` only if it's not already in the array.
      },
      $push: {
        screenshots: { platform, document }, // This will update the screenshots field.
      },
    }
  );
  logger.info({
    message: 'New Palanca account created and file added',
    requestId: requestId,
    platform: platform,
    method: 'addNewPalancaAccount',
    document: JSON.stringify(document),
  });
};

const addDocumentToAdmissionRequestAccount = async (requestId: string, platform: string, document: any) => {
  await AdmissionRequestMongo.updateOne(
    { _id: requestId },
    {
      $push: {
        screenshots: { platform, document }, // This will update the screenshots field.
      },
    }
  );
  logger.info({
    message: 'File added to AdmissionRequest object',
    requestId: requestId,
    platform: platform,
    method: 'addDocumentToAdmissionRequestAccount',
    document: JSON.stringify(document),
  });
};

const addPlatformToAdmissionRequest = async (requestId: string, platform: string, document: any) => {
  const admissionRequest = await AdmissionRequestMongo.findOne({
    _id: new Types.ObjectId(requestId),
  });
  if (!admissionRequest) {
    logger.error({
      status: 400,
      message: 'Admission Request record not found',
      requestId: requestId,
      platform: platform,
      method: 'addPlatformToAdmissionRequest',
    });
    throw new Error('AdmissionRequest record not found');
  }
  const existingAccount = admissionRequest?.palenca?.accounts?.filter((account) => {
    return account?.platform === platform;
  });
  if (existingAccount && existingAccount.length > 0) {
    //add document to existing account
    addDocumentToAdmissionRequestAccount(requestId, platform, document);
  } else {
    //add new account
    addNewPalancaAccount(requestId, platform, document);
  }
};

const extractUberData = async (
  requestId: string,
  platform: string,
  uploadType: string,
  file: Express.Multer.File
  // eslint-disable-next-line max-params
) => {
  await uploadToS3(file, `verification-screenshots/request-${requestId}-${platform}/`);
  const document = new Document({
    originalName: file.originalname,
    path: `verification-screenshots/request-${requestId}-${platform}/` + file.originalname,
  });
  await document.save();
  logger.info({
    message: 'File Uploaded on S3',
    requestId: requestId,
    platform: platform,
    file: JSON.stringify(document),
  });
  await addPlatformToAdmissionRequest(requestId, platform, document);
  // save data to mongo db
  let result = null;
  if (uploadType === 'profile') {
    result = await getUberProfileData(file);
    await saveUberDriverMetrics(platform, requestId, result);
  } else {
    //uploadType === 'earnings'
    result = await getUberEarningsData(file);
    await saveUberDriverEarnings(platform, requestId, result.weeklyEarnings);
  }
  return result;
};

const extractDiddiData = async (
  requestId: string,
  platform: string,
  uploadType: string,
  file: Express.Multer.File
  // eslint-disable-next-line max-params
) => {
  await uploadToS3(file, `verification-screenshots/request-${requestId}-${platform}/`);
  const document = new Document({
    originalName: file.originalname,
    path: `verification-screenshots/request-${requestId}-${platform}/` + file.originalname,
  });
  await document.save();
  logger.info({
    message: 'File Uploaded on S3',
    requestId: requestId,
    platform: platform,
    file: JSON.stringify(document),
  });
  let result = null;
  await addPlatformToAdmissionRequest(requestId, platform, document);
  // save data to mongo db
  if (uploadType === 'profile') {
    result = await getDidiProfileData(file);
    await saveDidiDriverMetrics(platform, requestId, result);
  } else {
    //uploadType === 'earnings'
    result = await getDidiEarningsData(file);
    await saveDidiDriverEarnings(platform, requestId, result);
  }
  // save in screenshot
  return result;
};

// eslint-disable-next-line prettier/prettier
const extractInDriveData = () => { };

const parseLyftExperience = (value: string) => {
  if (!value || typeof value !== 'string') return 0;

  const lowerValue = value.toLowerCase();

  // Handle "New" or invalid input
  if (lowerValue === 'new') {
    return 0; // or return 0, based on your requirements
  }

  const regex = /(\d+(\.\d+)?)\s*(year|years|month|months|day|days)/i;
  const match = lowerValue.match(regex);

  if (!match) return 0;

  const numericValue = parseFloat(match[1]);
  const unit = match[3].toLowerCase();

  switch (unit) {
    case 'year':
    case 'years':
      return numericValue;
    case 'month':
    case 'months':
      return parseFloat((numericValue / 12).toFixed(2)); // Convert months to years
    case 'day':
    case 'days':
      return parseFloat((numericValue / 365).toFixed(2)); // Convert days to years
    default:
      return 0; // Shouldn't reach here
  }
};

const saveLyftDriverMetrics = async (platform: string, requestId: string, result: any) => {
  //check if record already exist then modify existing record
  if (isNaN(result?.rating) || isNaN(result?.rides) || isNaN(parseLyftExperience(result?.experience))) {
    throw new Error('Invali Data');
  }
  if (result) {
    const existingMetric = await MetricMongo.findOne({ requestId: requestId, platform: platform });
    if (existingMetric) {
      existingMetric.acceptanceRate = 0;
      existingMetric.rating = result?.rating;
      existingMetric.lifetimeTrips = result?.rides;
      existingMetric.timeSinceFirstTrip = parseLyftExperience(result?.experience);
      existingMetric.cancellationRate = 0;
      existingMetric.save();
      logger.info({
        message: 'Existing Lyft Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveLyftDriverMetrics',
        earning: JSON.stringify(existingMetric),
      });
    } else {
      const metric = await MetricMongo.create({
        platform,
        requestId,
        acceptanceRate: 0,
        cancellationRate: 0,
        rating: result?.rating,
        lifetimeTrips: result?.rides,
        timeSinceFirstTrip: parseLyftExperience(result?.experience),
        activationStatus: 'documents_analysis',
      });
      logger.info({
        message: 'New Lyft Metric Saved',
        requestId: requestId,
        platform: platform,
        method: 'saveLyftDriverMetrics',
        earning: JSON.stringify(metric),
      });
    }
  }
};

const saveLyftDriverEarnings = async (platform: string, requestId: string, data: any) => {
  const mondayThirteenWeeksAgo = getMonday12WeeksAgoUTC();
  const lastSunday = getLastSundayUTC();
  if (!data || data?.length === 0) {
    throw new Error('Invalid Data');
  }
  const request = await AdmissionRequestMongo.findById(requestId).select('personalData.country');
  const isCountryUS = request?.personalData?.country === Country.us;

  let dataIsStale = true;
  for (const value of data) {
    const startInMilliSeconds = new Date(value.startDate);
    const startDate = new Date(startInMilliSeconds);
    if (startDate >= mondayThirteenWeeksAgo) {
      dataIsStale = false;
      break;
    }
  }
  if (dataIsStale) {
    throw new Error('Invalid Data');
  }
  for (const value of data) {
    const date = new Date(value.startDate);
    if (checkIfDateIsInBetween(mondayThirteenWeeksAgo, lastSunday, date)) {
      //check if earning record already exists for that week
      const existingEarning = await EarningMongo.findOne({
        platform: platform,
        requestId: requestId,
        earningDate: date.toISOString(),
      });
      if (existingEarning) {
        existingEarning.amount = value.amount;
        await existingEarning.save();
        logger.info({
          message: 'Existing Lyft Earning Updated',
          requestId: requestId,
          platform: platform,
          method: 'saveLyftDriverEarnings',
          earning: JSON.stringify(existingEarning),
        });
      } else {
        const earning = new EarningMongo({
          amount: value.amount,
          currency: isCountryUS ? CurrencyCode.usd : CurrencyCode.mxn,
          earningDate: date.toISOString(),
          countTrips: 0,
          requestId,
          cashAmount: 0,
          platform,
        });
        await earning.save();
        logger.info({
          message: 'New Lyft Earning Saved',
          requestId: requestId,
          platform: platform,
          method: 'saveLyftDriverEarnings',
          earning: JSON.stringify(earning),
        });
      }
    }
  }
};

const extractLyftData = async (
  requestId: string,
  platform: string,
  uploadType: string,
  file: Express.Multer.File
  // eslint-disable-next-line max-params
) => {
  await uploadToS3(file, `verification-screenshots/request-${requestId}-${platform}/`);
  const document = new Document({
    originalName: file.originalname,
    path: `verification-screenshots/request-${requestId}-${platform}/` + file.originalname,
  });
  await document.save();
  logger.info({
    message: 'File Uploaded on S3',
    requestId: requestId,
    platform: platform,
    file: JSON.stringify(document),
  });
  await addPlatformToAdmissionRequest(requestId, platform, document);
  // save data to mongo db
  let result = null;
  if (uploadType === 'profile') {
    result = await getLyftProfileData(file);
    await saveLyftDriverMetrics(platform, requestId, result);
  } else {
    // uploadType === 'earnings'
    result = await getLyftEarningsData(file);
    await saveLyftDriverEarnings(platform, requestId, result.weeklyEarnings);
  }
  return result;
};

const extractOtherData = async (
  requestId: string,
  platform: string,
  uploadType: string,
  file: Express.Multer.File
  // eslint-disable-next-line max-params
) => {
  await uploadToS3(file, `verification-screenshots/request-${requestId}-${platform}/`);
  const document = new Document({
    originalName: file.originalname,
    path: `verification-screenshots/request-${requestId}-${platform}/` + file.originalname,
  });
  await document.save();
  logger.info({
    message: 'File Uploaded on S3',
    requestId: requestId,
    platform: platform,
    file: JSON.stringify(document),
  });
  await addPlatformToAdmissionRequest(requestId, platform, document);
  // save data to mongo db
  // let result = null;
  // if (uploadType === 'profile') {
  //   result = await getLyftProfileData(file);
  //   await saveLyftDriverMetrics(platform, requestId, result);
  // } else {
  //   //uploadType === 'earnings'
  // }
  return {
    message: 'Data extracted successfully',
    requestId: requestId,
    platform: platform,
    uploadType: uploadType,
  };
};

export const extractFinanceRecords: AsyncController = async (req, res) => {
  try {
    const { requestId, platform, uploadType } = req.body;
    logger.info({
      requestId: requestId,
      platform: platform,
      uploadType: uploadType,
      method: 'extractFinanceRecords',
    });
    const file: Express.Multer.File | undefined = req.file;
    if (!file || !requestId || !platform || !uploadType) {
      logger.error({
        status: 400,
        message: 'Missing data',
        requestId: requestId,
        platform: platform,
        method: 'extractFinanceRecords',
      });
      return res.status(400).send({ message: 'Missing data' });
    }
    let data = {};
    switch (platform) {
      case 'uber':
        data = await extractUberData(requestId, platform, uploadType, file);
        break;

      case 'didi':
        data = await extractDiddiData(requestId, platform, uploadType, file);
        break;

      case 'indrive':
        extractInDriveData();
        break;

      case 'lyft':
        data = await extractLyftData(requestId, platform, uploadType, file);
        break;

      case 'other':
        data = await extractOtherData(requestId, platform, uploadType, file);
        break;

      // Handle other platforms here
      default:
        logger.error({
          status: 400,
          message: 'Error: Incorrect Platform',
          requestId: requestId,
          platform: platform,
          method: 'extractFinanceRecords',
        });
        return res.status(400).json({ message: 'Error: Incorrect Platform' });
    }
    logger.info({
      requestId: requestId,
      platform: platform,
      uploadType: uploadType,
      method: 'extractFinanceRecords',
      data: data,
    });
    return res.status(200).json(data);
  } catch (error: any) {
    console.error('Error in extracting data:', error);
    logger.error(JSON.stringify(error));
    return res.status(500).json({ message: error.message });
  }
};

const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getUTCDate() === date2.getUTCDate() &&
    date1.getUTCMonth() === date2.getUTCMonth() && // getMonth() returns 0 for January, 1 for February, etc.
    date1.getUTCFullYear() === date2.getUTCFullYear()
  );
};

export const getProfileData: AsyncController = async (req, res) => {
  try {
    const { requestId, platform, locale = 'en-US' } = req.params;
    logger.info({
      requestId: requestId,
      platform: platform,
      locale: locale,
      method: 'getProfileData',
    });
    if (!requestId || !platform) {
      logger.error({
        status: 400,
        message: 'Missing data',
        requestId: requestId,
        platform: platform,
        locale: locale,
        method: 'getProfileData',
      });
      return res.status(400).send({ message: 'Missing data' });
    }
    const earnings = await EarningMongo.find({ requestId: requestId, platform: platform }).sort({
      earningDate: -1,
    });
    logger.info({
      requestId: requestId,
      platform: platform,
      locale: locale,
      method: 'getProfileData',
      earnings: earnings,
    });
    let monday = getMonday12WeeksAgoUTC();
    logger.info({
      requestId: requestId,
      platform: platform,
      locale: locale,
      method: 'getProfileData',
      monday: monday,
    });
    const weeksWithStatuses = [];
    for (let i = 0; i < 12; i++) {
      const sunday = addDays(monday, 6);
      const keyFirstHalf = monday.toLocaleDateString(locale, { day: 'numeric', month: 'short' });
      const keySecondHalf = sunday.toLocaleDateString(locale, { day: 'numeric', month: 'short' });
      const key = `${keyFirstHalf} - ${keySecondHalf}`;
      const filteredEarning = earnings.find((earning) => isSameDay(earning.earningDate, monday));
      weeksWithStatuses.push({ earningWeek: key, earning: !!filteredEarning });
      monday.setDate(monday.getDate() + 7);
    }
    let data = {
      profileMetrics: await MetricMongo.findOne({ requestId: requestId, platform: platform }),
      earnings: weeksWithStatuses,
    };
    logger.info({
      requestId: requestId,
      platform: platform,
      locale: locale,
      method: 'getProfileData',
      data: data,
    });
    return res.status(200).json(data);
  } catch (error: any) {
    console.error('Error in extracting data:', error);
    logger.error(JSON.stringify(error));
    return res.status(500).json({ error });
  }
};

export const getDriverProfileData: AsyncController = async (req, res) => {
  try {
    const { requestId, platform } = req.params;
    logger.info({
      requestId: requestId,
      platform: platform,
      method: 'getDriverProfileData',
    });
    if (!requestId || !platform) {
      logger.error({
        status: 400,
        message: 'Missing data',
        requestId: requestId,
        platform: platform,
        method: 'getDriverProfileData',
      });
      return res.status(400).send({ message: 'Missing data' });
    }
    const data = {
      profileMetrics: await MetricMongo.findOne({ requestId: requestId, platform: platform }),
      earnings: await EarningMongo.find({ requestId: requestId, platform: platform }).sort({
        earningDate: -1,
      }),
    };
    logger.info({
      requestId: requestId,
      platform: platform,
      method: 'getDriverProfileData',
      data: data,
    });
    return res.status(200).json(data);
  } catch (error: any) {
    console.error('Error in extracting data:', error);
    logger.error(JSON.stringify(error));
    return res.status(500).json({ error });
  }
};

// Helper function to calculate week dates based on current date
const calculateWeekDates = (currentDate: Date = new Date()): Date[] => {
  const weekDates: Date[] = [];

  // Find the most recent Monday (week 1 start)
  const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Days to go back to Monday

  const mostRecentMonday = new Date(currentDate);
  mostRecentMonday.setDate(currentDate.getDate() - daysToSubtract);
  mostRecentMonday.setHours(0, 0, 0, 0);

  // Generate 12 week start dates going backwards from most recent Monday
  for (let i = 0; i < 12; i++) {
    const weekStart = new Date(mostRecentMonday);
    weekStart.setDate(mostRecentMonday.getDate() - i * 7);
    weekDates.push(weekStart);
  }

  return weekDates;
};

export const bulkUpdateDriverProfileData: AsyncController = async (req, res) => {
  const file: Express.Multer.File | undefined = req.file;
  const userId = req.authUser?.userId ?? '';

  if (!file) {
    return res.status(400).send({ message: 'No CSV file provided' });
  }

  try {
    const csvContent = file.buffer.toString('utf-8');
    const csvData = csvContent
      .split('\n')
      .slice(1) // Skip header row
      .map((line: string) => {
        if (!line.trim()) return null;

        // Parse CSV line respecting quoted fields
        const fields: string[] = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
          const char = line[i];

          if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
              // Handle escaped quotes
              current += '"';
              i++; // Skip next quote
            } else {
              inQuotes = !inQuotes;
            }
          } else if (char === ',' && !inQuotes) {
            fields.push(current.trim());
            current = '';
          } else {
            current += char;
          }
        }
        fields.push(current.trim()); // Add last field

        if (fields.length !== 20) {
          return null; // Invalid row - expecting 8 base fields + 12 week fields
        }

        const [
          requestId,
          platform,
          earnings, // Keep for backward compatibility, will be ignored
          acceptanceRate,
          cancellationRate,
          rating,
          timeSinceFirstTrip,
          totalTrips,
          week1,
          week2,
          week3,
          week4,
          week5,
          week6,
          week7,
          week8,
          week9,
          week10,
          week11,
          week12,
        ] = fields;

        return {
          requestId,
          platform,
          earnings,
          acceptanceRate,
          cancellationRate,
          rating,
          timeSinceFirstTrip,
          totalTrips,
          weeks: [week1, week2, week3, week4, week5, week6, week7, week8, week9, week10, week11, week12],
        };
      })
      .filter(Boolean);

    const results = {
      processed: 0,
      errors: [] as any[],
      success: 0,
    };

    // Track requestIds that need earnings analysis (executed once per requestId)
    const pendingEarningsAnalysis = new Set<string>();

    for (const row of csvData) {
      if (!row) continue;
      results.processed++;

      try {
        const {
          requestId,
          platform,
          acceptanceRate,
          cancellationRate,
          rating,
          timeSinceFirstTrip,
          totalTrips,
          weeks,
        } = row;

        if (
          !requestId ||
          !platform ||
          !weeks ||
          isNaN(parseFloat(acceptanceRate)) ||
          isNaN(parseFloat(cancellationRate)) ||
          isNaN(parseFloat(rating)) ||
          isNaN(parseFloat(timeSinceFirstTrip)) ||
          isNaN(parseInt(totalTrips))
        ) {
          results.errors.push({
            row: results.processed,
            error: 'Missing or invalid data in CSV row',
            data: row,
          });
          continue;
        }

        const request = await AdmissionRequestMongo.findById(requestId);
        if (!request) {
          results.errors.push({
            row: results.processed,
            error: 'Request not found',
            requestId,
          });
          continue;
        }

        // Calculate week dates starting from the most recent Monday
        const weekDates = calculateWeekDates();

        if (request.palenca.accounts.some((account: any) => account.platform === platform)) {
          await AdmissionRequestMongo.updateOne(
            { _id: requestId },
            { $pull: { 'palenca.accounts': { platform } } }
          );
        }

        const existingEarnings = await EarningMongo.find({ requestId, platform });
        const savedEarnings = new Map();
        existingEarnings.forEach((earning) => {
          const dateKey = new Date(earning.earningDate).toISOString().split('T')[0];
          savedEarnings.set(dateKey, earning);
        });

        const isCountryUS = request.personalData.country === Country.us;

        // Process each week's earnings
        for (let i = 0; i < weeks.length; i++) {
          const weekEarning = weeks[i];
          const earningDate = weekDates[i];
          const dateKey = earningDate.toISOString().split('T')[0];

          // Skip if no earning data or empty string
          if (!weekEarning || weekEarning.trim() === '' || isNaN(parseFloat(weekEarning))) {
            continue;
          }

          const earningAmount = parseFloat(weekEarning);
          const existingEarning = savedEarnings.get(dateKey);

          if (existingEarning) {
            if (existingEarning.amount !== earningAmount) {
              await EarningMongo.updateOne({ _id: existingEarning._id }, { $set: { amount: earningAmount } });
              await auditTrail.logFieldChange({
                userId,
                entityId: requestId,
                entityType: EntityType.admission_request,
                actionType: ActionType['driver_earning.updated'],
                metadata: {
                  field: 'amount',
                  oldValue: existingEarning.amount,
                  newValue: earningAmount,
                  displayName: 'ganancia',
                },
                message: `CSV bulk update - ${platform} week${i + 1} (${dateKey}) ganancia actualizado de ${existingEarning.amount} a ${earningAmount}`,
              });
            }
          } else {
            const earningData = new EarningMongo({
              amount: earningAmount,
              currency: isCountryUS ? CurrencyCode.usd : CurrencyCode.mxn,
              earningDate: earningDate,
              countTrips: 0,
              requestId,
              cashAmount: 0,
              platform: platform.toLowerCase(),
            });
            await earningData.save();

            if (earningAmount > 0) {
              await auditTrail.logFieldChange({
                userId,
                entityId: requestId,
                entityType: EntityType.admission_request,
                actionType: ActionType['driver_earning.added'],
                metadata: {
                  field: 'amount',
                  oldValue: null,
                  newValue: earningAmount,
                  displayName: 'ganancia',
                },
                message: `CSV bulk update - ${platform} week${i + 1} (${dateKey}) ganancia agregada ${earningAmount}`,
              });
            }
          }
        }

        const savedMetric = await MetricMongo.findOne({ requestId, platform });
        const acceptanceRateNum = parseFloat(acceptanceRate);
        const cancellationRateNum = parseFloat(cancellationRate);
        const ratingNum = parseFloat(rating);
        const timeSinceFirstTripNum = parseFloat(timeSinceFirstTrip);
        const totalTripsNum = parseInt(totalTrips);

        if (savedMetric) {
          const updates: any = {};

          if (savedMetric.acceptanceRate !== acceptanceRateNum) {
            updates.acceptanceRate = acceptanceRateNum;
            await auditTrail.logFieldChange({
              userId,
              entityId: requestId,
              entityType: EntityType.admission_request,
              actionType: ActionType['driver_metric.updated'],
              metadata: {
                field: 'acceptanceRate',
                oldValue: savedMetric.acceptanceRate,
                newValue: acceptanceRateNum,
                displayName: 'Tasa de aceptación',
              },
              message: `CSV bulk update - ${platform} Tasa de aceptación actualizado de ${savedMetric.acceptanceRate} a ${acceptanceRateNum}`,
            });
          }
          if (savedMetric.cancellationRate !== cancellationRateNum) {
            updates.cancellationRate = cancellationRateNum;
            await auditTrail.logFieldChange({
              userId,
              entityId: requestId,
              entityType: EntityType.admission_request,
              actionType: ActionType['driver_metric.updated'],
              metadata: {
                field: 'cancellationRate',
                oldValue: savedMetric.cancellationRate,
                newValue: cancellationRateNum,
                displayName: 'Tasa de cancelación',
              },
              message: `CSV bulk update - ${platform} Tasa de cancelación actualizado de ${savedMetric.cancellationRate} a ${cancellationRateNum}`,
            });
          }
          if (savedMetric.rating !== ratingNum) {
            updates.rating = ratingNum;
            await auditTrail.logFieldChange({
              userId,
              entityId: requestId,
              entityType: EntityType.admission_request,
              actionType: ActionType['driver_metric.updated'],
              metadata: {
                field: 'rating',
                oldValue: savedMetric.rating,
                newValue: ratingNum,
                displayName: 'Clasificación',
              },
              message: `CSV bulk update - ${platform} Clasificación actualizado de ${savedMetric.rating} a ${ratingNum}`,
            });
          }
          if (savedMetric.lifetimeTrips !== totalTripsNum) {
            updates.lifetimeTrips = totalTripsNum;
            await auditTrail.logFieldChange({
              userId,
              entityId: requestId,
              entityType: EntityType.admission_request,
              actionType: ActionType['driver_metric.updated'],
              metadata: {
                field: 'lifetimeTrips',
                oldValue: savedMetric.lifetimeTrips,
                newValue: totalTripsNum,
                displayName: 'Viajes totales',
              },
              message: `CSV bulk update - ${platform} Viajes totales actualizado de ${savedMetric.lifetimeTrips} a ${totalTripsNum}`,
            });
          }
          if (savedMetric.timeSinceFirstTrip !== timeSinceFirstTripNum) {
            updates.timeSinceFirstTrip = timeSinceFirstTripNum;
            await auditTrail.logFieldChange({
              userId,
              entityId: requestId,
              entityType: EntityType.admission_request,
              actionType: ActionType['driver_metric.updated'],
              metadata: {
                field: 'timeSinceFirstTrip',
                oldValue: savedMetric.timeSinceFirstTrip,
                newValue: timeSinceFirstTripNum,
                displayName: 'Tiempo desde el primer viaje',
              },
              message: `CSV bulk update - ${platform} Tiempo desde el primer viaje actualizado de ${savedMetric.timeSinceFirstTrip} a ${timeSinceFirstTripNum}`,
            });
          }

          if (Object.keys(updates).length > 0) {
            await savedMetric.updateOne({ $set: updates });
          }
        } else {
          await MetricMongo.create({
            platform: platform.toLowerCase(),
            requestId,
            acceptanceRate: acceptanceRateNum,
            cancellationRate: cancellationRateNum,
            rating: ratingNum,
            lifetimeTrips: totalTripsNum,
            timeSinceFirstTrip: timeSinceFirstTripNum,
            activationStatus: 'documents_analysis',
          });

          await auditTrail.logCustomEvent({
            userId,
            entityId: requestId,
            entityType: EntityType.admission_request,
            actionType: ActionType['driver_metric.added'],
            message: `CSV bulk update - ${platform} métricas agregadas (aceptación: ${acceptanceRateNum}, cancelación: ${cancellationRateNum}, rating: ${ratingNum}, viajes: ${totalTripsNum})`,
            metadata: {
              platform,
              acceptanceRate: acceptanceRateNum,
              cancellationRate: cancellationRateNum,
              rating: ratingNum,
              totalTrips: totalTripsNum,
              timeSinceFirstTrip: timeSinceFirstTripNum,
            },
          });
        }

        const newAccount = {
          _id: new Types.ObjectId(),
          accountId: new Date().getTime().toString(),
          platform: platform.toLowerCase(),
          earnings: {
            status: 'success',
            _id: new Types.ObjectId(),
          },
          metrics: {
            status: 'success',
            _id: new Types.ObjectId(),
          },
          status: 'success',
        };

        await AdmissionRequestMongo.updateOne(
          { _id: requestId },
          { $push: { 'palenca.accounts': newAccount }, status: AdmissionRequestStatus.earnings_analysis }
        );

        const modelResults = [
          {
            modelName: MLModels.RIDESHARE_PERFORMANCE,
            result: {
              ...modelResultMongoAdapter(request.modelScores?.[MLModels.RIDESHARE_PERFORMANCE]),
              status: AnalysisStatus.pending,
            },
          },
          {
            modelName: MLModels.FINANCIAL_ASSESSMENT,
            result: {
              ...modelResultMongoAdapter(request.modelScores?.[MLModels.FINANCIAL_ASSESSMENT]),
              status: AnalysisStatus.pending,
            },
          },
        ];

        await repoSaveAllModelResults(requestId, modelResults);

        // Mark this requestId for earnings analysis (will be executed once after all platforms are processed)
        pendingEarningsAnalysis.add(requestId);

        results.success++;
      } catch (error: any) {
        results.errors.push({
          row: results.processed,
          error: error.message,
          requestId: row?.requestId,
        });
      }
    }

    // Execute earnings analysis once per requestId after all platforms have been processed
    logger.info(
      `[bulkUpdateDriverProfileData] Processing earnings analysis for ${pendingEarningsAnalysis.size} unique requestIds`
    );

    for (const requestId of pendingEarningsAnalysis) {
      try {
        logger.info(`[bulkUpdateDriverProfileData] Starting earnings analysis for requestId ${requestId}`);
        const precaResult = await calculateEarningsAnalysis(requestId);
        logger.info(
          `[bulkUpdateDriverProfileData] Earnings analysis completed for requestId ${requestId} with status: ${precaResult.earningsAnalysis?.status}`
        );

        // Update request status to documents_analysis if prequalification is successful
        if (precaResult.earningsAnalysis?.status) {
          await AdmissionRequestMongo.updateOne({ _id: requestId }, { status: 'documents_analysis' });
          logger.info(
            `[bulkUpdateDriverProfileData] Updated status to documents_analysis for requestId ${requestId}`
          );
        } else {
          logger.warn(
            `[bulkUpdateDriverProfileData] Prequalification status not available for requestId ${requestId}`
          );
        }
      } catch (earningsError: any) {
        logger.error(
          `[bulkUpdateDriverProfileData] Error in earnings analysis for requestId ${requestId}: ${earningsError.message}`
        );
        // Don't fail the entire operation if earnings analysis fails
        results.errors.push({
          row: 'earnings_analysis',
          error: `Earnings analysis failed: ${earningsError.message}`,
          requestId,
        });
      }
    }

    return res.status(200).json({
      message: 'CSV bulk update completed',
      results: results,
    });
  } catch (error: any) {
    console.error('Error in bulk update:', error);
    return res.status(500).json({ message: 'Error processing CSV file', error: error.message });
  }
};
