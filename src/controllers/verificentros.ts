import type { AsyncController } from '../types&interfaces/types';

import StockVehicle from '../models/StockVehicleSchema';
import { uploadFile } from '../aws/s3';
import { getSignedUrl as getSignedUrlSDK } from '@aws-sdk/s3-request-presigner';
import { S3Client } from '@aws-sdk/client-s3';
import fs from 'fs';
import { logger } from '../clean/lib/logger';
import { EmissionsVerification } from '../vendor-platform/modules/emissions-verification/models/emissions-verification.model';
import { VerificationLogicService } from '../vendor-platform/modules/emissions-verification/services/verification-logic.service';

export const isElectric: AsyncController = async (req, res) => {
  try {
    const { plates } = req.params;
    const vehicle = await StockVehicle.findOne({ 'carPlates.plates': plates.trim().toUpperCase() });
    if (!vehicle) return res.status(404).send({ message: 'Vehículo no encontrado' });

    return res.status(200).send({ isElectric: vehicle.isElectric });
  } catch (error) {
    console.error('Error in isElectric:', error);
    return res.status(500).json({ error: 'Vehiculo no encontrado' });
  }
};

/**
 * Obtener información de verificación por link único
 */
export const getVerificationInfo: AsyncController = async (req, res) => {
  try {
    const { uniqueLink } = req.params;

    if (!uniqueLink) {
      return res.status(400).json({
        success: false,
        message: 'Link único requerido',
      });
    }

    const verification = await EmissionsVerification.findOne({ uniqueLink })
      .populate('verificationCenterId', 'name code location')
      .populate('vehicleId', 'carPlates brand model year color');

    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'Verificación no encontrada o link inválido',
      });
    }

    // Verificar si el link ha expirado (opcional - puedes ajustar la lógica)
    const daysSinceCreation = Math.floor(
      (new Date().getTime() - verification.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceCreation > 30) {
      // Link expira después de 30 días
      return res.status(410).json({
        success: false,
        message: 'El link de verificación ha expirado',
      });
    }

    return res.json({
      success: true,
      data: {
        verification: {
          id: verification._id,
          vehiclePlate: verification.vehiclePlate,
          verificationDate: verification.verificationDate,
          nextVerificationDate: verification.nextVerificationDate,
          status: verification.status,
          verificationCenter: verification.verificationCenterId,
          vehicle: verification.vehicleId,
          customerEvidence: verification.customerEvidence,
          isCompleted: verification.status === 'completed',
          hasCustomerEvidence: !!(
            verification.customerEvidence?.verificationCertificate &&
            verification.customerEvidence?.hologramPhoto
          ),
        },
      },
    });
  } catch (error) {
    console.error('Error in getVerificationInfo:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};

/**
 * Subir evidencias del usuario (certificado de verificación y foto del holograma)
 */
export const uploadCustomerEvidence: AsyncController = async (req, res) => {
  try {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const { uniqueLink } = req.params;
    const { hologramType, isExempt, customerNotes } = req.body;

    if (!uniqueLink) {
      return res.status(400).json({
        success: false,
        message: 'Link único requerido',
      });
    }

    // Buscar la verificación
    const verification = await EmissionsVerification.findOne({ uniqueLink });
    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'Verificación no encontrada o link inválido',
      });
    }

    if (verification.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Esta verificación ya ha sido completada',
      });
    }

    // Validar que se subieron los archivos requeridos
    if (!files?.verificationCertificate || !files?.hologramPhoto) {
      return res.status(400).json({
        success: false,
        message: 'Se requieren tanto el certificado de verificación como la foto del holograma',
      });
    }

    const certificateFile = files.verificationCertificate[0];
    const hologramFile = files.hologramPhoto[0];
    const uploadedFiles: Express.Multer.File[] = [];

    // Función para limpiar archivos temporales
    const cleanupFiles = async (filesToClean: Express.Multer.File[]) => {
      for (const file of filesToClean) {
        if (file.path) {
          try {
            await fs.promises.unlink(file.path);
          } catch (error) {
            if (error instanceof Error && !error.message?.includes('ENOENT')) {
              logger.error(`Error deleting temporary file ${file.path}:`, error);
            }
          }
        }
      }
    };

    try {
      // Subir certificado de verificación
      const certificateCommand = await uploadFile(
        certificateFile,
        `${Date.now()}-certificate-${certificateFile.originalname}`,
        `verificentros/${verification.vehiclePlate}/${uniqueLink}/customer/`
      );
      uploadedFiles.push(certificateFile);

      // Subir foto del holograma
      const hologramCommand = await uploadFile(
        hologramFile,
        `${Date.now()}-holograma-${hologramFile.originalname}`,
        `verificentros/${verification.vehiclePlate}/${uniqueLink}/customer/`
      );
      uploadedFiles.push(hologramFile);

      // Obtener las URLs firmadas
      const certificateUrl = await getSignedUrlSDK(new S3Client({}), certificateCommand);
      const hologramUrl = await getSignedUrlSDK(new S3Client({}), hologramCommand);

      // Actualizar evidencias del customer
      verification.customerEvidence = {
        ...verification.customerEvidence,
        verificationCertificate: certificateCommand.input.Key || certificateUrl,
        hologramPhoto: hologramCommand.input.Key || hologramUrl,
        hologramType: hologramType as '00' | '0' | '1' | '2',
        isExempt: isExempt === 'true' || isExempt === true,
        customerNotes: customerNotes || '',
        uploadedAt: new Date(),
      };

      // Si se proporciona el tipo de holograma, recalcular fecha de próxima verificación
      if (hologramType) {
        const vehicle = verification.vehicleId ? await StockVehicle.findById(verification.vehicleId) : null;

        const isHybridOrElectric = vehicle?.isElectric || false;
        // Usar createdAt como fecha de registro del vehículo si está disponible
        const vehicleRegistrationDate = vehicle?.createdAt ? new Date(vehicle.createdAt) : undefined;

        const calculationResult = VerificationLogicService.calculateNextVerificationDate(
          verification.vehiclePlate,
          verification.verificationDate,
          hologramType as '00' | '0' | '1' | '2',
          isHybridOrElectric,
          vehicleRegistrationDate
        );

        verification.nextVerificationDate = calculationResult.nextVerificationDate;
        verification.calculatedNextDate = calculationResult.nextVerificationDate;
        verification.exemptUntil = calculationResult.exemptUntil;
      }

      // Marcar como completado
      verification.status = 'completed';

      await verification.save();
      await cleanupFiles(uploadedFiles);

      return res.status(200).json({
        success: true,
        message: 'Evidencias subidas exitosamente. Proceso de verificación completado.',
        data: {
          verification: {
            id: verification._id,
            vehiclePlate: verification.vehiclePlate,
            status: verification.status,
            nextVerificationDate: verification.nextVerificationDate,
            exemptUntil: verification.exemptUntil,
            customerEvidence: verification.customerEvidence,
          },
        },
      });
    } catch (uploadError) {
      await cleanupFiles(uploadedFiles);
      console.error('Error uploading customer evidence:', uploadError);
      return res.status(500).json({
        success: false,
        message: 'Error al subir los archivos',
      });
    }
  } catch (error) {
    console.error('Error in uploadCustomerEvidence:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};

/**
 * Validar si el proceso de verificación está completo
 */
export const getVerificationStatus: AsyncController = async (req, res) => {
  try {
    const { uniqueLink } = req.params;

    if (!uniqueLink) {
      return res.status(400).json({
        success: false,
        message: 'Link único requerido',
      });
    }

    const verification = await EmissionsVerification.findOne({ uniqueLink });
    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'Verificación no encontrada o link inválido',
      });
    }

    const isComplete =
      verification.status === 'completed' &&
      verification.customerEvidence?.verificationCertificate &&
      verification.customerEvidence?.hologramPhoto;

    return res.json({
      success: true,
      data: {
        isComplete,
        status: verification.status,
        missingFields: {
          certificate: !verification.customerEvidence?.verificationCertificate,
          hologramPhoto: !verification.customerEvidence?.hologramPhoto,
          hologramType: !verification.customerEvidence?.hologramType,
        },
        nextVerificationDate: verification.nextVerificationDate,
        exemptUntil: verification.exemptUntil,
        hologramType: verification.customerEvidence?.hologramType,
      },
    });
  } catch (error) {
    console.error('Error in getVerificationStatus:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};

/**
 * Obtener opciones de hologramas disponibles
 */
export const getHologramOptions: AsyncController = async (_req, res) => {
  try {
    const hologramOptions = [
      {
        type: '00',
        name: 'Doble Cero',
        description: 'Exento de verificación por 2 años',
        color: 'Verde',
        exemptionYears: 2,
      },
      {
        type: '0',
        name: 'Cero',
        description: 'Verificación cada 6 meses',
        color: 'Verde',
        exemptionYears: 0,
      },
      {
        type: '1',
        name: 'Uno',
        description: 'Verificación cada 6 meses',
        color: 'Amarillo',
        exemptionYears: 0,
      },
      {
        type: '2',
        name: 'Dos',
        description: 'Verificación cada 6 meses',
        color: 'Rosa',
        exemptionYears: 0,
      },
    ];

    return res.json({
      success: true,
      data: hologramOptions,
    });
  } catch (error) {
    console.error('Error in getHologramOptions:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};

/**
 * Función simplificada para finalizar verificación usando placas como identificador
 */
export const submitVerificationByPlate: AsyncController = async (req, res) => {
  try {
    const { plate } = req.params;
    const {
      nextVerificationDate,
      hologramType,
      isHybridElectric,
      registrationDate,
      certificate,
      hologram,
      customerNotes,
    } = req.body;

    if (!plate) {
      return res.status(400).json({
        success: false,
        message: 'Placas requeridas',
      });
    }

    // Validar campos requeridos
    if (!nextVerificationDate || !hologramType || !certificate || !hologram) {
      return res.status(400).json({
        success: false,
        message: 'Faltan campos requeridos: nextVerificationDate, hologramType, certificate, hologram',
      });
    }

    // Buscar la verificación por placas (la más reciente si hay varias)
    let verification = await EmissionsVerification.findOne({
      vehiclePlate: plate.toUpperCase(),
    }).sort({ createdAt: -1 });

    // Si no existe, crear una nueva verificación
    if (!verification) {
      verification = new EmissionsVerification({
        vehiclePlate: plate.toUpperCase(),
        verificationDate: new Date(),
        status: 'pending',
        uniqueLink: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // Generar link único
      });
    }

    // Verificar si ya está completada
    if (verification.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Esta verificación ya ha sido completada',
      });
    }

    // Actualizar la verificación con los datos del frontend
    verification.nextVerificationDate = new Date(nextVerificationDate);
    verification.calculatedNextDate = new Date(nextVerificationDate);

    // Si es híbrido/eléctrico y holograma 00, calcular fecha de exención
    if (isHybridElectric && hologramType === '00') {
      const exemptDate = new Date(registrationDate);
      exemptDate.setFullYear(exemptDate.getFullYear() + 8); // 8 años de exención
      verification.exemptUntil = exemptDate;
    } else if (hologramType === '00') {
      const exemptDate = new Date();
      exemptDate.setFullYear(exemptDate.getFullYear() + 2); // 2 años de exención
      verification.exemptUntil = exemptDate;
    }

    // Actualizar evidencias del customer
    verification.customerEvidence = {
      ...verification.customerEvidence,
      verificationCertificate: certificate,
      hologramPhoto: hologram,
      hologramType: hologramType as '00' | '0' | '1' | '2',
      isExempt: hologramType === '00',
      customerNotes: customerNotes || '',
      uploadedAt: new Date(),
    };

    // Marcar como completado
    verification.status = 'completed';

    await verification.save();

    return res.status(200).json({
      success: true,
      message: 'Verificación completada exitosamente',
      data: {
        verification: {
          id: verification._id,
          vehiclePlate: verification.vehiclePlate,
          status: verification.status,
          nextVerificationDate: verification.nextVerificationDate,
          exemptUntil: verification.exemptUntil,
          customerEvidence: verification.customerEvidence,
        },
      },
    });
  } catch (error) {
    console.error('Error in submitVerificationByPlate:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};

/**
 * Obtener información de verificación por placas
 */
export const getVerificationByPlate: AsyncController = async (req, res) => {
  try {
    const { plate } = req.params;

    if (!plate) {
      return res.status(400).json({
        success: false,
        message: 'Placas requeridas',
      });
    }

    // Buscar la verificación más reciente por placas
    const verification = await EmissionsVerification.findOne({
      vehiclePlate: plate.toUpperCase(),
    }).sort({ createdAt: -1 });

    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'No se encontró verificación para estas placas',
      });
    }

    return res.json({
      success: true,
      data: {
        verification: {
          id: verification._id,
          vehiclePlate: verification.vehiclePlate,
          status: verification.status,
          verificationDate: verification.verificationDate,
          nextVerificationDate: verification.nextVerificationDate,
          exemptUntil: verification.exemptUntil,
          customerEvidence: verification.customerEvidence,
          uniqueLink: verification.uniqueLink,
        },
      },
    });
  } catch (error) {
    console.error('Error in getVerificationByPlate:', error);
    return res.status(500).json({
      success: false,
      message: 'Error interno del servidor',
    });
  }
};
