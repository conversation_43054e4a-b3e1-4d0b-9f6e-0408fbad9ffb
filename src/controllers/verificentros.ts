import type { AsyncController } from '../types&interfaces/types';

import StockVehicle from '../models/StockVehicleSchema';

export const isElectric: AsyncController = async (req, res) => {
  try {
    const { plates } = req.params;
    const vehicle = await StockVehicle.findOne({ 'carPlates.plates': plates.trim().toUpperCase() });
    if (!vehicle) return res.status(404).send({ message: 'Vehículo no encontrado' });

    return res.status(200).send({ isElectric: vehicle.isElectric });
  } catch (error) {
    console.error('Error in isElectric:', error);
    return res.status(500).json({ error: 'Vehiculo no encontrado' });
  }
};
