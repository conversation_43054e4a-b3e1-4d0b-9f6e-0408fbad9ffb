/* eslint-disable @typescript-eslint/no-use-before-define */
import Contract from '../models/contractSchema';
import MainContract from '../models/mainContractSchema';
import MainContractHistory from '../models/mainContractSchemaHistory';
import StockVehicle from '../models/StockVehicleSchema';
import type { AsyncController } from '../types&interfaces/types';
import { stockVehiclesText, genericMessages, wire4Data, steps, CountriesEnum } from '../constants';
import Associate, { IAssociate } from '../models/associateSchema';
import AssociatePayments from '../models/associatePayments';
// import { differenceInWeeks, parse } from 'date-fns';
import { getLastContractNumber } from '../services/getTheLastContractNumber';
import axios from 'axios';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../constants/payments-api';
import { formatDate } from '../services/formatDate';
import { createWire4BankAccount } from '../services/createWire4BankAccount';
import { MyRequest } from '../types&interfaces/interfaces';
import { CustomError } from '../services/customErrors';
import { removeEmptySpacesNameFile } from '../services/removeEmptySpaces';
import Document from '../models/documentSchema';
import { deleteFileFromS3, uploadFile } from '../aws/s3';
import { getCurrentDateTime } from '../services/timestamps';
import StartPayFlow from '../models/start-pay-flow';
import { Types } from 'mongoose';
import { deleteWeetrustDocument } from '../modules/Associate/services/weetrust';
import { logger } from '../clean/lib/logger';
import OverHauling from '@/models/overHaulingSchema';
// import moment from 'moment';
import { DateTime } from 'luxon';

export const getContracts: AsyncController = async (req, res) => {
  try {
    const getAllContracts = await Contract.find();
    return res.status(200).send(getAllContracts);
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: stockVehiclesText.errors.error, error });
  }
};

export const addContract: AsyncController = async (req, res) => {
  const { region, stockVehicleId, documentId } = req.body;
  if (!region || !stockVehicleId)
    return res.status(400).send({ message: genericMessages.errors.missingBody });

  try {
    const contractNumber = (await getLastContractNumber(region)) || 1;
    const alias = region + contractNumber.toString().padStart(4 - contractNumber.toString().length, '0');
    const contract = new Contract({ region, contractNumber, alias, stockVehicleId, documentId });
    const savedContract = await contract.save();
    return res
      .status(200)
      .send({ message: stockVehiclesText.success.contractCreated, contract: savedContract });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.contract.addContract, error });
  }
};

export const getTheNextNumber: AsyncController = async (req, res) => {
  const { region } = req.body;
  if (!region) return res.status(400).send({ message: genericMessages.errors.missingBody });
  try {
    const nextNumber = (await getLastContractNumber(region)) || 1;
    if (typeof nextNumber === 'number' && nextNumber.toString().length > 3) {
      return res.status(200).send({ region: region, nextNumber, alias: region + '0' + nextNumber });
    }
    const ceros = '0'.repeat(3 - nextNumber.toString().length);
    if (nextNumber === 0)
      return res.status(200).send({ region: region, nextNumber: 0, alias: region + '000' });
    const alias = region + ceros + nextNumber;
    return res.status(200).send({ region: region, nextNumber, alias });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.contract.findContractNumber, error });
  }
};

export const addAssociatedContract2: AsyncController = async (req, res) => {
  const {
    contractNumber,
    stockId,
    associatedId,
    documentId,
    weeklyRent,
    finalPrice,
    allPayments,
    downPayment,
    totalPrice,
    deliveredDate,
    restPayments,
  } = req.body;

  delete req.body.allPayments;

  if (
    !contractNumber ||
    !stockId ||
    !associatedId ||
    !weeklyRent ||
    !finalPrice ||
    !allPayments ||
    !downPayment ||
    !totalPrice ||
    !documentId ||
    !deliveredDate
  )
    return res.status(400).send({ message: genericMessages.errors.missingBody });

  try {
    const stockVehicle = await StockVehicle.findById(stockId);
    if (!stockVehicle) return res.status(400).send({ message: genericMessages.errors.missingVehicleId });
    const extension = stockVehicle.extensionCarNumber;

    const mainContract = await MainContract.findOne({ contractNumber }).lean();

    if (mainContract) {
      logger.info(`[addAssociatedContract2] Contract number ${contractNumber} found, creating history`);
      await MainContractHistory.create(mainContract);
      await MainContract.deleteOne({ contractNumber: contractNumber });
      logger.info(`[addAssociatedContract2] Contract number ${contractNumber} deleted`);
    }

    const newContract = new MainContract({
      contractNumber: extension ? `${contractNumber}-${extension}` : contractNumber,
      stockId,
      associatedId,
      weeklyRent,
      finalPrice,
      allPayments: restPayments && restPayments.length > 0 ? restPayments : allPayments,
      downPayment,
      totalPrice,
      documentId,
      deliveredDate,
    });

    await StockVehicle.findByIdAndUpdate(stockId, { $push: { deliveredDate } }, { new: true });

    const associate = await Associate.findById(associatedId);

    const map = (e: any) => ({
      ...e,
      payed: false,
      weeklyRent,
      block: false,
    });

    const newPaymentsArray =
      restPayments && restPayments.length > 0 ? restPayments.map(map) : allPayments.map(map);

    const alias = stockVehicle.extensionCarNumber
      ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
      : stockVehicle.carNumber;
    const userData = JSON.stringify({
      alias,
      currency_code: wire4Data.bankAccount.currency_code,
      email: [associate?.email],
      name: `${associate?.firstName} ${associate?.lastName}`,
    });

    const { clabe } = await createWire4BankAccount(userData);

    console.log('MONEX CLABE', clabe);

    const region = stockVehicle.vehicleState;
    const dataToSend = {
      vehiclesId: stockId,
      associateId: associatedId,
      model: stockVehicle.model,
      monexClabe: clabe,
      associateEmail: associate?.email.trim().toLocaleLowerCase(),
      contractId: newContract._id,
      paymentsArray: newPaymentsArray,
      region: region.toUpperCase(),
      monexEmail: associate?.email.trim().toLocaleLowerCase(),
    };

    // console.log('Contract data to send', dataToSend);

    try {
      await axios.patch(
        `${PAYMENTS_API_URL}/clients/${associate?.clientId}`,
        {
          monexClabe: clabe,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );

      const createPayFlow = {
        clientId: associate?.clientId,
        products: req.body.products,
        downPaymentProduct: req.body.downPaymentProduct,
        depositProduct: req.body.depositProduct,
        startDate: deliveredDate,
        endDate: formatDate(allPayments[allPayments.length - 1].day),
      };

      // console.log('Create payflow', createPayFlow);

      const response = await axios.post(
        `${PAYMENTS_API_URL}/subscriptions/initiate-pay-flow`,
        createPayFlow,
        {
          headers: {
            Authorization: `Bearer ${PAYMENTS_API_KEY}`,
          },
        }
      );

      const associatePayments = new AssociatePayments(dataToSend);
      await associatePayments.save();

      await newContract.save();

      console.log('Response from payments api', response.data);
    } catch (error: any) {
      console.log('Error creating payflow', error.response.data);
    }

    return res.status(200).send({
      message: genericMessages.success.contract.addContract,
      contract: newContract,
      deliveredDate,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.contract.addContract, error });
  }
};

export const addAssociatedContract: AsyncController = async (req, res) => {
  const {
    // contractNumber,
    stockId,
    associatedId,
    // documentId,
    weeklyRent,
    finalPrice = 100000,
    allPayments,
    downPayment,
    totalPrice,
    deliveredDate,
    restPayments,
    deliveryDateContractTimezone,
  } = req.body;

  const { rentingProduct, assistanceProduct, depositProduct, downPaymentProduct } = req.body;

  const { currentAssociate } = await createContract(req, stockId);

  const documentId = currentAssociate?.unSignedContractDoc;

  if (
    !stockId ||
    !associatedId ||
    !weeklyRent ||
    !finalPrice ||
    !allPayments ||
    !downPayment ||
    !totalPrice ||
    !deliveredDate
  ) {
    return res.status(400).send({ message: genericMessages.errors.missingBody });
  }

  try {
    const stockVehicle = await StockVehicle.findById(stockId);
    if (!stockVehicle) return res.status(400).send({ message: genericMessages.errors.missingVehicleId });

    if (stockVehicle.country === CountriesEnum['United States']) {
      if (!rentingProduct) {
        const message = 'Cannot create contract without rental products';
        return res.status(400).send({ message: message });
      }
    } else {
      if (!rentingProduct || !assistanceProduct) {
        const message = 'Cannot create contract without rental and assistance products';
        return res.status(400).send({ message: message });
      }
    }

    const contractNumber = stockVehicle.carNumber;
    const extension = stockVehicle.extensionCarNumber;
    const contractNoExtension = extension ? `${contractNumber}-${extension}` : contractNumber;

    const mainContract = await MainContract.findOne({ contractNumber: contractNoExtension }).lean();

    if (mainContract) {
      logger.info(`[addAssociatedContract2] Contract number ${contractNoExtension} found, creating history`);
      await MainContractHistory.create(mainContract);
      await MainContract.deleteOne({ contractNumber: contractNoExtension });
      logger.info(`[addAssociatedContract2] Contract number ${contractNumber} deleted`);
    }

    const totalPriceCalculated =
      restPayments && restPayments.length > 0
        ? restPayments.length * weeklyRent
        : allPayments.length * weeklyRent;

    const newContract = new MainContract({
      contractNumber: contractNoExtension,
      stockId,
      associatedId,
      weeklyRent,
      finalPrice,
      allPayments: restPayments && restPayments.length > 0 ? restPayments : allPayments,
      downPayment,
      totalPrice: totalPriceCalculated,
      documentId,
      deliveredDate: deliveredDate,
      deliveryData: {
        date: deliveredDate,
        timezone: deliveryDateContractTimezone,
      },
    });

    await StockVehicle.findByIdAndUpdate(stockId, { $push: { deliveredDate } }, { new: true });

    const associate = await Associate.findById(associatedId);

    if (!associate) return res.status(400).send({ message: 'No se encontro el asociado' });

    const map = (e: any) => ({
      ...e,
      payed: false,
      weeklyCost: weeklyRent,
      block: false,
    });

    const newPaymentsArray =
      restPayments && restPayments.length > 0 ? restPayments.map(map) : allPayments.map(map);

    const region = stockVehicle.vehicleState.toUpperCase();
    const dataToSend = {
      vehiclesId: stockId,
      associateId: associatedId,
      model: stockVehicle.model,
      // monexClabe: clabe,
      associateEmail: associate?.email.trim().toLocaleLowerCase(),
      contractId: newContract._id,
      paymentsArray: newPaymentsArray,
      region: region.toUpperCase(),
      monexEmail: associate?.email.trim().toLocaleLowerCase(),
    };

    // console.log('dataToSend', dataToSend);

    /**
     * we don't need to register clients from here, we are already registering
     * client from create and assign associate endpoint, it's necessary to follow this
     * approach, as we are also registering US clients on Stripe from Payment Module which
     * is mandatory and should happen when creating a new associate/client.
     */
    // const createClient: CreateClient = {
    //   contractNumber: contractNoExtension,
    //   name: associate.firstName,
    //   lastName: associate.lastName,
    //   email: associate.email,
    //   phone: associate.phone.toString(),
    //   tax_system: associate.tax_system || '616',
    //   region,
    //   rfc: associate.rfc as string,
    //   legal_name: `${associate.firstName} ${associate.lastName}`.toUpperCase(),
    //   zip: associate.postalCode.toString(),
    //   associateId: associate._id.toString(),
    // };

    // if (associate.use_cfdi) {
    //   Object.assign(createClient, { use_cfdi: associate.use_cfdi });
    // }

    // try {
    //   const response = await axios.post(`${PAYMENTS_API_URL}/clients`, createClient, {
    //     headers: {
    //       Authorization: `Bearer ${PAYMENTS_API_KEY}`,
    //     },
    //   });

    //   // console.log('Client created', response.data.data);
    //   const clientId = response.data.data.id;
    //   console.log('Client id', clientId);
    //   associate.clientId = clientId;
    // } catch (error: any) {
    //   console.log('Error creating client', error.response.data);
    //   await onErrorCreatingContract(stockId);
    //   return res.status(500).send({ message: 'Error al crear al conductor en apartado de pagos', error });
    // }

    const payflowExists = await StartPayFlow.findOne({
      // associateId: associatedId,
      // stockId,
      // contractNumber: {
      //   $in: [contractNumber, contractNoExtension],
      // },
      contractNumber: contractNoExtension,
      isCreated: false,
    });

    const createPayFlow = {
      contractNumber: contractNoExtension,
      startDate: formatDate(allPayments[0].day),
      endDate: formatDate(allPayments[allPayments.length - 1].day),
      stockId,
      associateId: associatedId,
      clientId: associate.clientId,
      rentingProduct,
      assistanceProduct,
      downPaymentProduct: downPaymentProduct || null,
      depositProduct: depositProduct || null,
    };

    if (payflowExists) {
      console.log('Payflow exists', payflowExists._id);
      await StartPayFlow.findByIdAndUpdate(payflowExists._id, createPayFlow, { new: true });
    } else {
      console.log('Payflow does not exists, creating new one');
      await StartPayFlow.create(createPayFlow);
    }

    const checkAssociatePayments = await AssociatePayments.findOne({ vehiclesId: stockId });
    if (checkAssociatePayments) {
      logger.info(`[addAssociatedContract] Associate payments found, removing ${checkAssociatePayments._id}`);
      await checkAssociatePayments.remove();
      logger.info(`[addAssociatedContract] Associate payments removed`);
    }
    const associatePayments = new AssociatePayments(dataToSend);
    console.log('Associate payments', associatePayments.associateEmail);
    await newContract.save();
    await associatePayments.save();
    await associate.save();
    return res.status(200).send({
      message: genericMessages.success.contract.addContract,
      contract: newContract,
      deliveredDate,
    });
  } catch (error: any) {
    console.log('error', error);
    // if (error instanceof CustomError) {
    //   if (error.message === 'Error creating contract') {
    //   }
    //   }
    await onErrorCreatingContract(stockId);
    return res.status(500).send({ message: genericMessages.errors.contract.addContract, error });
  }
};

export const updateContractDoc: AsyncController = async (req, res) => {
  // const contract = await Contract
  try {
    const { stockId } = req.body;

    if (!stockId || !req.file) return res.status(400).send({ message: genericMessages.errors.missingBody });

    const stockVehicle = await StockVehicle.findById(stockId);

    if (!stockVehicle)
      return res.status(400).send({ message: genericMessages.errors.vehicle.vehicleNotFound });

    const associate = await Associate.findById(
      stockVehicle.drivers[stockVehicle.drivers.length - 1]?._id
    ).select('+digitalSignature');

    if (!associate) return res.status(400).send({ message: 'Associate not found' });

    const findOldContract = await Document.findById(associate.unSignedContractDoc);

    if (findOldContract) {
      await deleteFileFromS3(findOldContract.path);
      await findOldContract.remove();
    }

    const contract: Express.Multer.File | undefined = req.file;

    const removeSpacesNamefile = removeEmptySpacesNameFile(contract);

    const newUnsignedContract = new Document({
      originalName: removeSpacesNamefile,
      path: `stock/${stockVehicle.carNumber}/contract/${removeSpacesNamefile}`,
      associateId: associate._id,
      vehicleId: stockVehicle._id,
    });

    await uploadFile(contract, removeSpacesNamefile, `stock/${stockVehicle?.carNumber}/contract/`);

    associate.unSignedContractDoc = newUnsignedContract._id;

    // console.log('BEFORE digital signature', associate.digitalSignature);

    if (associate.digitalSignature) {
      associate.digitalSignature.isSent = false;
      associate.digitalSignature.signed = false;

      if (associate.digitalSignature?.documentID) {
        try {
          await deleteWeetrustDocument(undefined, associate.digitalSignature.documentID);
        } catch (error: any) {
          console.error('Error deleting document from weetrust', error.message);
        }
        associate.digitalSignature.documentID = undefined;
      }
    }

    await newUnsignedContract.save();
    await associate.save();
    // console.log('---------------------------------------------');
    // console.log('AFTER digital signature', associate.digitalSignature);

    stockVehicle.updateHistory.push({
      userId: req.userId.userId,
      step: 'CONTRATO REEMPLAZADO',
      description: '',
      time: getCurrentDateTime(),
    });

    await stockVehicle.save();

    return res.status(200).send({ message: 'Contrato actualizado' });
  } catch (error: any) {
    return res.status(500).send({ message: genericMessages.errors.contract.addContract, error });
  }
};

export const easyAddAssociatedContract: AsyncController = async (req, res) => {
  const { email } = req.body;
  if (!email) return res.status(400).send({ message: genericMessages.errors.missingBody });
  const associate = await Associate.findOne({ email: email.trim().toLocaleLowerCase() });
  if (!associate) return res.status(400).send({ message: genericMessages.errors.users.notFound });
  const mainContract = await MainContract.findOne({ associatedId: associate._id }).sort({ createdAt: -1 });
  if (!mainContract)
    return res.status(400).send({ message: genericMessages.errors.contract.contractNotFound });
  const stockVehicle = await StockVehicle.findById(mainContract.stockId);
  if (!stockVehicle) return res.status(400).send({ message: genericMessages.errors.vehicle.vehicleNotFound });
  const { model } = stockVehicle;
  const { stockId, associatedId, deliveredDate, allPayments } = mainContract;

  const newPaymentsArray = allPayments;
  const region = stockVehicle.vehicleState;
  const dataToSend = {
    vehiclesId: stockId,
    associateId: associatedId,
    model,
    associateEmail: associate?.email.trim().toLocaleLowerCase(),
    contractId: mainContract._id,
    paymentsArray: newPaymentsArray,
    region: region.toUpperCase(),
    monexEmail: email,
  };

  const associatePayments = new AssociatePayments(dataToSend);
  await associatePayments.save();
  return res.status(200).send({
    associatePayments,
    deliveredDate,
  });
};

export const getPaymentsByVehicleId: AsyncController = async (req, res) => {
  const { id } = req.params;
  const payments = await AssociatePayments.find({ vehiclesId: id });
  return res.status(200).send({ message: 'Pagos del vehiculo', payments });
};

export const getLastPaymeOfVehicleById: AsyncController = async (req, res) => {
  const { id } = req.params;
  const lastPayment = await AssociatePayments.findOne({ vehiclesId: id })
    .select('paymentNumber contractId _id')
    .sort({ createdAt: -1 });
  const mainContract = await MainContract.findOne({ _id: lastPayment?.contractId }).select('allPayments _id');
  return res.status(200).send({
    message: 'Pagos del vehiculo',
    lastPayment,
    mainContract,
    restWeeks: (lastPayment?.paymentNumber || 1) - 1,
  });
};

export const updatePaymentById: AsyncController = async (req, res) => {
  const { id } = req.params;
  const { paymentNumber } = req.body;
  if (!paymentNumber) return res.status(400).send({ message: genericMessages.errors.missingBody });
  try {
    const payment = await AssociatePayments.findByIdAndUpdate(id, { paymentNumber });
    return res.status(200).send({ message: 'Pago actualizado', payment });
  } catch (error) {
    console.error(error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

async function createContract(req: MyRequest, vehicleId: string) {
  // const { vehicleId } = req.params

  const vehicleStock = await StockVehicle.findById(vehicleId);
  if (!vehicleStock) {
    throw new CustomError(stockVehiclesText.errors.vehicleNotFound, 404);
    // return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound })
  }
  const contract: Express.Multer.File | undefined = req.file;

  // console.log('Contract', contract);
  const removeSpacesNamefile = removeEmptySpacesNameFile(contract);

  try {
    if (contract && vehicleStock) {
      const newStockVehicleContract = new Document({
        originalName: removeSpacesNamefile,
        path: `stock/${vehicleStock.carNumber}/contract/${removeSpacesNamefile}`,
        vehicleId: vehicleStock?._id,
      });
      await uploadFile(contract, removeSpacesNamefile, `stock/${vehicleStock?.carNumber}/contract/`);
      await newStockVehicleContract.save();
      // console.log('document saved', newStockVehicleContract);
      // vehicleStock.contract = newStockVehicleContract._id
      vehicleStock.step.stepName = steps.contractCreated.name;
      vehicleStock.step.stepNumber = steps.contractCreated.number;

      vehicleStock.updateHistory.push({
        userId: req.userId.userId,
        step: 'CONTRATO GENERADO',
        description: '',
        time: getCurrentDateTime(),
      });

      const lastAssociate = await Associate.findById(
        vehicleStock.drivers[vehicleStock.drivers.length - 1]?._id
      );

      if (lastAssociate) {
        lastAssociate.unSignedContractDoc = newStockVehicleContract._id;
        await lastAssociate.save();
      }

      // vehicleStock.contract = newStockVehicleContract._id
      await vehicleStock.save();
    }
    const currentAssociate = await Associate.findById(
      vehicleStock.drivers[vehicleStock.drivers.length - 1]?._id
    );

    return { vehicleStock, currentAssociate };
    // return res.status(200)
    // .send({ message: stockVehiclesText.success.contractCreated, vehicleStock, currentAssociate })
  } catch (error) {
    console.error('[CREATE CONTRACT]', error);
    throw new CustomError('Error creating contract', 500);
    // return res.status(400).send({ message: stockVehiclesText.errors.error, error })
  }
}

async function onErrorCreatingContract(vehicleId: string) {
  const vehicleStock = await StockVehicle.findById(vehicleId);
  if (!vehicleStock) {
    throw new CustomError(stockVehiclesText.errors.vehicleNotFound, 404);
    // return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound })
  }

  vehicleStock.step.stepName = steps.driverAssigned.name;
  vehicleStock.step.stepNumber = steps.driverAssigned.number;

  vehicleStock.updateHistory.pop();

  const lastAssociate = (await Associate.findById(
    vehicleStock.drivers[vehicleStock.drivers.length - 1]?._id
  )) as IAssociate;

  let documentId: Types.ObjectId = new Types.ObjectId();

  if (lastAssociate) {
    if (lastAssociate.unSignedContractDoc) {
      documentId = lastAssociate.unSignedContractDoc;
      // delete lastAssociate.unSignedContractDoc;
      lastAssociate.unSignedContractDoc = null;
      await lastAssociate.save();
    }
  }

  const doc = await Document.findById(documentId);

  if (doc) {
    await deleteFileFromS3(doc.path);
    console.log('Document deleted', doc.path);
    await doc.remove();
  }
  console.log('Stock vehicle return back', documentId);
  await vehicleStock.save();
}

// async function calculateWeeksForSemiNews()

export const calculateWeeksForSemiNews: AsyncController = async (req, res) => {
  try {
    // console.log('----------------------------------------------');
    // console.log('----------------------------------------------');

    // console.log('Body', req.body);
    const stockId = req.body.stockId;
    const weeklyPayment = req.body.weeklyPayment;

    const hasInvoice = req.body.hasInvoice;
    const invoiceAmount = req.body.invoiceAmount;

    const invoiceFile = req.file as Express.Multer.File;
    // console.log('Invoice file', invoiceFile);

    if (!stockId) return res.status(400).send({ message: genericMessages.errors.missingBody });

    const stockVehicle = await StockVehicle.findById(stockId)
      .select('newCar drivers extensionCarNumber step ')
      .populate('associates', 'clientId ');

    if (!stockVehicle)
      return res.status(400).send({ message: genericMessages.errors.vehicle.vehicleNotFound });

    if (stockVehicle.newCar)
      return res.status(400).send({ message: 'No es un vehiculo semi-nuevo, no se puede calcular' });

    /* GET PREVIOUS ASSOCIATE */
    const associates = stockVehicle.associates;

    let associate: any;

    if (stockVehicle.step.stepNumber >= 1 && stockVehicle.step.stepNumber <= 2) {
      // If the vehicle is in the step 1 or 2, the associate is the last one
      associate = associates[associates.length - 1];
    }
    if (stockVehicle.step.stepNumber >= 3) {
      // If the vehicle is in the step 3 or higher, the associate is the second last one
      associate = associates[associates.length - 2];
    }
    if (!associate) return res.status(400).send({ message: 'No se encontro el conductor anterior' });

    /* GET LAST PAID PAYMENT */
    const payments = await getLastPaidPaymentByClientId(associate.clientId);
    if (!payments || !payments.length)
      return res.status(400).send({ message: 'No se encontraron pagos del conductor anterior' });

    /* GET MAIN CONTRACT */
    const mainContract = await MainContract.findOne({ associatedId: associate._id, stockId }).select(
      'allPayments contractNumber stockId associateId deliveredDate'
    );

    let associatePayments = await AssociatePayments.findOne({
      associateId: associate._id,
      vehiclesId: stockId,
    });

    if (!mainContract) return res.status(400).send({ message: 'No se encontro el contrato principal' });

    if (!associatePayments) {
      associatePayments = await AssociatePayments.findOne({
        monexEmail: associate.email,
      });
      // update associatePayments with correct data
      if (associatePayments) {
        associatePayments.associateId = associate._id;
        associatePayments.vehiclesId = stockId;
        associatePayments.contractId = mainContract._id;
        // console.log('Associate payment updated: ', associatePayments);
        await associatePayments.save();
      }
    }

    if (!associatePayments) return res.status(400).send({ message: 'No se encontro el pago del asociado' });

    const overHauling = await OverHauling.findOne({ stockId })
      .sort({ sameVehicleCount: -1 }) // Ordenar por sameVehicleCount de forma descendente, trae el valor más alto primero
      .select('invoiceAmount hasInvoice createdAt');

    if (!overHauling) return res.status(400).send({ message: 'No se encontro el overhauling' });

    if (invoiceFile) {
      // En lugar de crear un nuevo documento, actualizamos el documento existente si existe, eliminamos el archivo anterior de s3

      if (overHauling.invoiceFile) {
        const oldInvoiceDoc = await Document.findById(overHauling.invoiceFile);
        if (oldInvoiceDoc) {
          await deleteFileFromS3(oldInvoiceDoc.path);
          const removeSpaces = removeEmptySpacesNameFile(invoiceFile);
          const s3Res = await uploadFile(
            invoiceFile,
            removeSpaces,
            `overhauling/${stockVehicle.carNumber}/${overHauling.sameVehicleCount}/invoice/`
          );
          const path = s3Res.input.Key!;
          oldInvoiceDoc.originalName = removeSpaces;
          oldInvoiceDoc.path = path;
          oldInvoiceDoc.vehicleId = stockId;
          await oldInvoiceDoc.save();
          overHauling.invoiceFile = oldInvoiceDoc._id;
        }
      }
    }

    // overHauling.hasInvoice = hasInvoice;
    if (hasInvoice) {
      //
      // console.log('HAS INVOICE: ', hasInvoice);
      const verify = hasInvoice === 'true';
      // console.log('VERIFY: ', verify);
      overHauling.hasInvoice = verify;
      // console.log('CHECK AFTER VERIFY: ', overHauling.hasInvoice);
    }
    if (invoiceAmount) overHauling.invoiceAmount = invoiceAmount;

    const allPaymentsFormated = mainContract.allPayments.map((el) => {
      const [date, month, year] = el.day.split('-');
      return {
        day: `${year}-${month}-${date}`,
        number: el.number,
      };
    });
    const lastPayment = payments[0];

    const lastPaymentDate = DateTime.fromISO(lastPayment.createdAt, { zone: 'utc' }).setZone(
      'America/Mexico_City'
    );

    const dayOfWeek = lastPaymentDate.weekday; // En Luxon: 1 = Lunes, 4 = Jueves, 7 = Domingo

    let referenceMonday: any;

    if (dayOfWeek === 4) {
      // Si es jueves
      referenceMonday = lastPaymentDate.plus({ days: 4 }).startOf('week');
    } else if (dayOfWeek >= 1 && dayOfWeek <= 3) {
      // Lunes a miércoles
      referenceMonday = lastPaymentDate.minus({ weeks: 1 }).startOf('week');
    } else {
      // Viernes a domingo
      referenceMonday = lastPaymentDate.plus({ weeks: 1 }).startOf('week');
    }

    const paymentIndex = allPaymentsFormated.findIndex((payment) => {
      const paymentDate = DateTime.fromISO(payment.day, { zone: 'America/Mexico_City' });
      return paymentDate.hasSame(referenceMonday, 'day');
    });

    let totalWeeks;
    let previousTotalPayments = 156;

    if (stockVehicle.extensionCarNumber === 2) {
      // Para el segundo conductor, restamos de 156
      totalWeeks = 156 - (paymentIndex + 1);
    } else {
      // Para el tercer conductor o posterior, restamos del total de pagos del contrato
      totalWeeks = mainContract.allPayments.length - (paymentIndex + 1);
      previousTotalPayments = mainContract.allPayments.length;
    }
    console.log('overHauling', overHauling);
    const detail: any = {
      overhauling: {
        hasInvoice: overHauling.hasInvoice,
        // Checar si la propiedad hasInvoice existe y es true, si no existe poner que isOldVersion es true
        isOldVersion: !('hasInvoice' in overHauling.toObject()),

        _id: overHauling._id,
      },
      weeksWithoutOverhauling: totalWeeks,
      previousTotalPayments,
    };

    // console.log('overhauling', overHauling);
    if (overHauling.hasInvoice && overHauling.invoiceAmount && weeklyPayment) {
      // totalWeeks = Math.round(overHauling.invoiceAmount / weeklyPayment);
      // add the calculation of this to the previous weeks
      const calculatedWeeksFromOverHauling = Math.round(overHauling.invoiceAmount / weeklyPayment);
      totalWeeks += calculatedWeeksFromOverHauling;

      detail.overhauling.weeks = calculatedWeeksFromOverHauling;
      detail.overhauling.invoiceAmount = overHauling.invoiceAmount;
    }

    // Asegurarnos que no sea negativo
    totalWeeks = Math.max(totalWeeks, 0);

    // console.log('total weeks', totalWeeks);
    // console.log('----------------------------------------------');
    // console.log('----------------------------------------------');
    await overHauling.save();
    if (req.body.removeInvoice === 'true') {
      console.log('remove invoice');
      await OverHauling.updateOne(
        { _id: overHauling._id },
        {
          $unset: {
            invoiceAmount: 1,
            hasInvoice: 1,
            invoiceFile: 1,
          },
        }
      );
    }
    // console.log('detail', detail);
    return res.status(200).send({
      message: 'Semanas calculadas exitosamente',
      data: {
        weeks: totalWeeks,
        lastPaymentDate: lastPayment.createdAt,
        clientId: associate.clientId,
        referenceMonday: referenceMonday.toISO(),
        paymentsCompleted: paymentIndex + 1,
        detail,
      },
    });
  } catch (error) {
    console.error('Error en calculateWeeksForSemiNews:', error);
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

async function getLastPaidPaymentByClientId(clientId: string) {
  try {
    const { data } = await axios.get(`${PAYMENTS_API_URL}/payments`, {
      params: {
        status: 'success',
        limit: '1',
        totalMoreThan: '3000',
        clientId,
      },
      headers: {
        Authorization: `Bearer ${PAYMENTS_API_KEY}`,
      },
    });

    return data.data;
  } catch (error: any) {
    console.log('error response data', error.response.data);
    return null;
  }
}
