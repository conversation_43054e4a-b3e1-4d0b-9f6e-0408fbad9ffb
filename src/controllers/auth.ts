import { removeEmptySpacesNameFile } from './../services/removeEmptySpaces';
import User from '../models/userSchema';
import PermissionSet from '../models/permissionSetSchema';
import ExternalUser from '../models/externalUserSchema';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { addAndInvalidateTokenDB } from '../conf/blackList';
import { sendInvitationEmail, sendVerificationEmail } from '../middlewares/email';
import type { AsyncController } from '../types&interfaces/types';
import {
  accessTokenSecret,
  externalAccessTokenSecret,
  invitationSecret,
  recoverPasswordSecret,
  integrations,
  genericMessages,
  ONE_CAR_NOW_EMAIL_REGEX,
  CountriesEnum,
  Areas,
} from '../constants';
import { generateId } from '../services/functions';
import ExpiredToken from '../models/expiredTokens';
import Document from '../models/documentSchema';
import { replaceDocWithUrl } from '../services/getPropertyWithUrls';
import UserVehicleRestrictions from '../models/userVehicleRestriction';
import { verifyGoogleIdToken } from '../services/googleAuth/googleAuthClient';
import { logger } from '../clean/lib/logger';
import { encodeString } from './common/hashing';
import Associate from '@/models/associateSchema';
import { generateAndSendOTP, verifyCode } from '../services/otp';
import { AdmissionRequestMongo } from '@/models/admissionRequestSchema';
import { NotFoundException } from '@/clean/errors/exceptions';
import { Country, RequestGenerationSource, RequestIdType } from '@/clean/domain/enums';
import { errorCodeUtils } from '@/utils/error.utils';

export const registerUser: AsyncController = async (req, res) => {
  const { name, email, password, role, city, settings } = req.body;

  if (!name || !email || !password)
    return res.status(401).send({ message: genericMessages.errors.missingParams });

  const existingUser = await User.findOne({ email });

  if (existingUser) {
    return res.status(400).json({ message: genericMessages.errors.users.duplicateEmail });
  }

  const hashedPassword = await bcrypt.hash(password, 12);

  try {
    const user = new User({
      name,
      email,
      password: hashedPassword,
      role,
      city,
      settings,
    });
    await user.save();

    return res.status(200).send({ message: genericMessages.success.users.created });
  } catch (error: any) {
    return res.status(400).send({ message: error.message });
  }
};

type TimeUnit = 's' | 'm' | 'h' | 'd';

// type TimeUnitOptions = Record<TimeUnit, number>;

// LOGIN
// const timeUnitFactors: TimeUnitOptions = {
//   s: 1000, // segundos a milisegundos
//   m: 60 * 1000, // minutos a milisegundos
//   h: 60 * 60 * 1000, // horas a milisegundos
//   d: 24 * 60 * 60 * 1000, // dias a milisegundos
// };

export const googleLogin: AsyncController = async (req, res) => {
  const { email, googleProvidedIdToken, googleProvidedRefreshToken } = req.body;
  logger.info({
    message: `[googleLogin] user email: ${email} to login with google`,
  });

  if (!email || !googleProvidedIdToken || !googleProvidedRefreshToken) {
    logger.error({
      message:
        '[googleLogin] email or googleProvidedIdToken missing, googleLogin email: ${email}, googleProvidedIdToken: ${googleProvidedIdToken}',
    });
    return res.status(400).send({ message: genericMessages.errors.missingParams });
  }

  try {
    const idTokenPayload = await verifyGoogleIdToken(googleProvidedIdToken);

    logger.info({
      message: `[googleLogin] idTokenPayload decoded for user ${idTokenPayload?.email}`,
    });

    if (!idTokenPayload || (idTokenPayload.email && !ONE_CAR_NOW_EMAIL_REGEX.test(idTokenPayload.email))) {
      logger.error({
        message: '[googleLogin] idTokenPayload invalid',
      });
      return res.status(400).send({ message: genericMessages.errors.tokens.googleIdTokenInvalid });
    }
  } catch (err: any) {
    logger.error({
      message: '[googleLogin] verifyGoogleIdToken error',
      stack: err.stack,
    });
    return res.status(400).send({ message: genericMessages.errors.tokens.googleIdTokenInvalid });
  }

  try {
    const user = await User.findOne({ email });
    if (!user) {
      logger.error({
        message: '[googleLogin] user id not found in DB',
      });
      return res.status(400).send({ message: genericMessages.errors.users.googleRegisteredUserNotFound });
    }
    if (user.isActive === false) {
      logger.error({
        message: '[googleLogin] user is inActive',
      });
      return res.status(400).send({ message: genericMessages.errors.users.UserIsInactive });
    }
    req.userId = user._id;

    const expirationNumber = 10;
    const expirationString: TimeUnit = 'h';

    const accessToken = jwt.sign(
      { userId: user._id, role: user.role, email: user.email },
      accessTokenSecret,
      {
        expiresIn: `${expirationNumber}${expirationString}`,
      }
    );

    let image;

    if (user.image) {
      image = await replaceDocWithUrl(user.image._id.toString());
    } else {
      image = {
        docId: null,
        originalName: null,
        url: null,
      };
    }

    const permissionSet = await PermissionSet.findOne({
      role: user.role,
      area: user.area,
    }).lean();

    let permissions = [...(permissionSet?.permissions || []), ...(user.permissions || [])];

    const uniquePermissions = Array.from(
      new Map(permissions.map((p) => [`${p.section}.${p.subSection}.${p.capability}`, p])).values()
    );

    const userData = {
      email: user.email,
      id: user._id,
      name: user.name,
      role: user.role,
      area: user.area,
      image: image,
      city: user.city,
      googleLoginFailureCount: user.googleSignfailureCount,
      permissions: uniquePermissions || null,
    };

    const token = jwt.decode(accessToken) as jwt.JwtPayload;

    res.setHeader('expiration', (token.exp as number).toString());
    user.googleSignfailureCount = 0;
    user.googleRefreshToken = encodeString(googleProvidedRefreshToken);
    await user.save();
    return res
      .status(200)
      .send({ message: genericMessages.success.users.loggin, accessToken, user: userData });
  } catch (err: any) {
    logger.error({
      message: '[googleLogin] error',
      stack: err.stack,
    });
    return res.status(400).send({ message: genericMessages.errors.users.googleRegisteredUserNotFound });
  }
};

/**
 * This endpoint is temporary and needed only as long as the google Signin is not completely
 * onboarded and understood by the users. This endpoint will be used to count the number of times the user
 * has failed to login using google sign in, and after 3 failure attempts of google SignIn, the user will fallback
 * to the previoud login flow.
 */
export const googleLoginFailureCount: AsyncController = async (req, res) => {
  const { email } = req.body;
  logger.info({
    message: `[googleLoginFailureCount] googleLogin email: ${email} `,
  });
  if (!email) {
    logger.error({
      message: '[googleLoginFailureCount] email missing',
    });
    return res.status(400).send({ message: genericMessages.errors.missingParams });
  }
  try {
    const user = await User.findOne({ email });
    if (!user) {
      logger.error({
        message: '[googleLoginFailureCount] user id not found in DB',
      });
      return res.status(400).send({ message: genericMessages.errors.users.googleRegisteredUserNotFound });
    }

    user.googleSignfailureCount += 1;
    await user.save();

    req.userId = user._id;

    const userData = {
      email: user.email,
      id: user._id,
      googleSignInfailureCount: user.googleSignfailureCount,
    };

    return res
      .status(200)
      .send({ message: genericMessages.errors.users.googleSignInfailureCountUpdateSuccess, user: userData });
  } catch (err: any) {
    logger.error({
      message: '[googleLoginFailureCount] Error occured while updating googleSignInfailureCount',
      stack: err.stack,
    });
    return res
      .status(400)
      .send({ message: genericMessages.errors.users.googleSignInfailureCountUpdateFailure });
  }
};

export const loginUser: AsyncController = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) return res.status(400).send({ message: genericMessages.errors.missingParams });

  const user = await User.findOne({ email });
  if (!user) return res.status(400).send({ message: genericMessages.errors.users.notFound });
  if (user.isActive === false) {
    logger.error({
      message: '[googleLogin] user is inActive',
    });
    return res.status(400).send({ message: genericMessages.errors.users.UserIsInactive });
  }
  req.userId = user._id;

  //comparando contraseñas
  const rightPassword = await bcrypt.compare(password, user.password);
  if (!rightPassword) {
    return res.status(400).send({ message: genericMessages.errors.users.wrongCredentials });
  }

  const expirationNumber = 10;
  const expirationString: TimeUnit = 'h';

  const accessToken = jwt.sign({ userId: user._id, role: user.role, email: user.email }, accessTokenSecret, {
    expiresIn: `${expirationNumber}${expirationString}`,
  });

  let image;

  if (user.image) {
    image = await replaceDocWithUrl(user.image._id.toString());
  } else {
    image = {
      docId: null,
      originalName: null,
      url: null,
    };
  }

  const permissionSet = await PermissionSet.findOne({
    role: user.role,
    area: user.area,
  }).lean();

  let permissions = [...(permissionSet?.permissions || []), ...(user.permissions || [])];

  const uniquePermissions = Array.from(
    new Map(permissions.map((p) => [`${p.section}.${p.subSection}.${p.capability}`, p])).values()
  );

  const userData = {
    email: user.email,
    id: user._id,
    name: user.name,
    role: user.role,
    area: user.area,
    image: image,
    city: user.city,
    permissions: uniquePermissions || null,
  };

  const token = jwt.decode(accessToken) as jwt.JwtPayload;

  res.setHeader('expiration', (token.exp as number).toString());
  user.googleSignfailureCount = 0;
  await user.save();
  return res.status(200).send({ message: genericMessages.success.users.loggin, accessToken, user: userData });
};

// LOGOUT

export const logout: AsyncController = async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];

  // Si no se proporcionó un token, enviamos un error
  if (!token) {
    return res.status(400).json({ message: genericMessages.errors.unauthorized });
  }

  try {
    // Verificamos si el token está en la lista negra
    jwt.verify(token, accessTokenSecret);
    // const decodedToken = jwt.decode(token) as jwt.JwtPayload;
    const tokenDB = await ExpiredToken.findOne({ token });

    if (tokenDB) {
      return res.status(200).json({
        message: 'Sesión cerrada correctamente',
        jwt: 'El token ha sido invalidado',
      });
    }

    // Al hacer logout, se agrega a la lista negra para que si el usuario cerro sesión antes de la expiracion y el token sigue sin expirar, cuando se intente hacer un pedido con el token valido, primero se revisará si el token esta en la lista negra y no se pueda usar aunque sea valido
    // Esto es porque en resumen, si el usuario cierra sesion antes de 24 horas de iniciar sesion (tiempo definido al hacer login), el token sigue siendo valido, y de esta forma al hacer logout lo invalidamos manualmente
    addAndInvalidateTokenDB(token);

    return res.status(200).json({
      message: 'Sesión cerrada correctamente',
      jwt: 'El token ha sido invalidado',
    });
  } catch (error) {
    return res.status(500).json({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

// AÑADE Y ELIMINA EL TOKEN DE LA LISTA NEGRA CON EL TIEMPO RESTANTE DE EXPIRACIÓN
// esto es para invalidar el token de forma manual si el usuario cierra sesión antes del tiempo de expiración

// RECUPERAR CONTRASEÑA MEDIANTE EMAIL **FUNCION POST

export const recoverPassword: AsyncController = async (req, res) => {
  const { email }: { email: string } = req.body;
  const baseUrl = req.headers.origin!;

  if (!email) return res.status(400).send({ message: genericMessages.errors.users.missingEmail });

  const user = await User.findOne({ email });
  if (!user) return res.status(401).send({ message: genericMessages.errors.users.notFound });

  const recoverPasswordToken = jwt.sign({ userId: user._id }, recoverPasswordSecret, {
    expiresIn: '10m',
  });
  const objEmail = {
    email,
    user: user.name,
    token: recoverPasswordToken,
    baseUrl: baseUrl,
  };

  try {
    await sendVerificationEmail(objEmail);
    return res.status(200).send({ message: genericMessages.success.users.recoverPass });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

// VALIDACIÓN DEL TOKEN DE RECUPERACIÓN DE CONTRASEÑA ENVIADO POR EMAIL ** FUNCION GET

export const validateTokens: AsyncController = async (req, res) => {
  const { code, method } = req.query;

  if (!code) return res.status(401).send({ message: genericMessages.errors.users.codeNotFound });
  const token = code as string;
  if (!method) return res.status(401).send({ message: 'Metodo requerido' });

  const tokenDB = await ExpiredToken.findOne({ token });

  if (tokenDB) {
    return res.status(401).json({ message: genericMessages.errors.unauthorized_v2 });
  }

  // addAndInvalidateTokenDB(token);
  let secret = '';
  if (method === 'inv') {
    secret = invitationSecret;
  }
  if (method === 'recover-pass') {
    secret = recoverPasswordSecret;
  }

  try {
    jwt.verify(token, secret) as jwt.JwtPayload;

    return res.status(200).send({ message: 'Puedes ingresar tu contraseña' });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

// CAMBIO DE CONTRASEÑA **FUNCION POST

export const changeForgotPassword: AsyncController = async (req, res) => {
  const { code } = req.query;
  const { password } = req.body;

  if (!code) return res.status(401).send({ message: genericMessages.errors.users.codeNotFound });
  const token = code as string;

  const tokenDB = await ExpiredToken.findOne({ token });

  if (tokenDB) {
    return res.status(401).json({ message: genericMessages.errors.unauthorized_v2 });
  }

  addAndInvalidateTokenDB(token);

  try {
    const result = jwt.verify(token, recoverPasswordSecret) as jwt.JwtPayload;

    if (!password) return res.status(401).send({ message: genericMessages.errors.users.requiredPass });

    if (typeof password !== 'string')
      return res.status(401).send({ message: 'La contraseña debe ser un string' });

    const userId = result.userId;
    const user = await User.findById(userId);

    if (!user) return res.status(403).send({ message: genericMessages.errors.users.notFound });

    const hashedPassword = await bcrypt.hash(password, 12);
    user.password = hashedPassword;
    user.save();

    return res.status(200).send({ message: genericMessages.success.users.passChanged });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

// INVITACIÓN POR CORREO ELECTRONICO PARA LA CREACIÓN DE CUENTA

export const sendInvitation: AsyncController = async (req, res) => {
  const { email, role, adminId, city, name, frontUrl, allowedRegions, area } = req.body;
  // El userId es el id del que esta invitando un nuevo usuario y el role es el que se le va a asignar al invitado
  if (!email) return res.status(401).send({ message: 'Añade un email' });
  if (!role || !city || !name || !allowedRegions)
    return res.status(404).send({ message: 'Completa los campos' });

  if (!frontUrl) return res.status(404).send({ message: 'Añade url de redirección' });

  const adminCreator = await User.findById(adminId);
  if (!adminCreator) return res.status(401).send({ message: 'Usuario admin no encontrado' });
  if (adminCreator.role !== 'superadmin' && adminCreator.area !== area)
    return res.status(403).send({ message: 'No tienes permisos para ejecutar esta acción' });

  if (!role) return res.status(401).send({ message: 'Asigna un rol al usuario' });

  const domain = email.split('@')[1];
  if (domain !== 'onecarnow.com')
    return res.status(400).send({ message: 'No es posible enviar la invitación' });

  /*   const names = email.split('@')[0].split('.');
  const firstName: string = names[0].charAt(0).toUpperCase() + names[0].slice(1);
  const secondName: string = names[1] ? names[1].charAt(0).toUpperCase() + names[1].slice(1) : '';
  const CapitalNames = firstName + ' ' + secondName; */

  const userExists = await User.findOne({ email });
  if (userExists?.isVerified)
    return res.status(409).send({ message: 'Este usuario ya fue invitado y aceptó la invitación' });
  const password = generateId(12);
  // Esta contraseña se genera aleatoriamente, ya que la contraseña es requerida en el Schema para poder crear un usuario y al aceptar la invitación se reemplaza a la contraseña del invitado
  const hashedPassword = await bcrypt.hash(password, 12);

  let userId = userExists?._id;

  if (!userExists) {
    const user = new User({
      email,
      name: name.trim(),
      password: hashedPassword,
      role,
      city,
      settings: {
        allowedRegions,
      },
      isVerified: true,
      area: area ? area : Areas.superadmin,
    });
    userId = user._id;
    await user.save();
  } else {
    userExists.role = role;
    userExists.city = city;
    userExists.name = name.trim();
    userExists.settings = {
      allowedRegions,
    };
    userExists.isVerified = true;
    userExists.area = area ? area : Areas.superadmin;
    await userExists.save();
  }

  if (role === 'auditor') {
    const restriction = new UserVehicleRestrictions({
      userId,
    });

    await restriction.save();

    return res
      .status(200)
      .send({ message: 'Contraseña para el auditor copiada en el portapapeles', password });
  }

  const invitationToken = jwt.sign({ email }, invitationSecret, {
    expiresIn: '72h',
  });

  const objEmail = {
    email,
    user: name.trim(),
    token: invitationToken,
    role,
    baseUrl: frontUrl,
    name: name.trim(),
    area: area ? area : Areas.superadmin,
  };

  await sendInvitationEmail(objEmail);

  return res.status(200).send({ message: genericMessages.success.users.invitation });
};

// FUNCION PARA ACEPTAR LA INVITACIÓN Y SETEANDO LA CONTRASEÑA DEL USUARIO E IMAGEN (OPCIONAL)

export const acceptInvitation: AsyncController = async (req, res) => {
  const { password } = req.body;
  const image: Express.Multer.File | undefined = req.file;
  const { code } = req.query;
  try {
    if (!code) return res.status(400).send({ message: 'Token no encontrado' });
    const token = code as string;

    // Se verifica el token, si hay error se va al catch
    jwt.verify(token, invitationSecret);

    const tokenDB = await ExpiredToken.findOne({ token });

    if (tokenDB) {
      return res.status(401).json({ message: 'El token ya ha sido invalidado anteriormente' });
    }

    addAndInvalidateTokenDB(token);

    // Se decodifica el token para obtener el email que se agrego en la generación del token
    const decodedToken = jwt.decode(token) as jwt.JwtPayload;
    //Se busca al usuario creado mediante el email
    if (!password) return res.status(401).send({ message: genericMessages.errors.users.missingPassword });

    const user = await User.findOne({ email: decodedToken.email });
    if (!user) return res.status(400).send({ message: genericMessages.errors.users.notFound });

    const hashedPassword = await bcrypt.hash(password, 12);
    user.password = hashedPassword;
    user.isVerified = true;

    if (image) {
      const removeSpace = removeEmptySpacesNameFile(image);

      const imageDoc = new Document({
        path: `users/${user._id}/profile/${removeSpace}`,
        userId: user._id,
        originalName: removeSpace,
      });

      await imageDoc.save();
      user.image = imageDoc._id;
    }
    await user.save();

    return res.status(200).send({ message: genericMessages.success.users.created });
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const registerExternalUser: AsyncController = async (req, res) => {
  const { email, enterprice, password } = req.body;
  if (
    !integrations.externalsUsers.emails.includes(email) ||
    !integrations.externalsUsers.enterprices.includes(enterprice)
  )
    return res.status(400).send({ message: genericMessages.errors.email.unauthorized });
  if (!email || !enterprice || !password)
    return res.status(401).send({ message: genericMessages.errors.missingBody });
  const user = await ExternalUser.findOne({ email });
  if (user) return res.status(401).send({ message: genericMessages.errors.users.duplicateEmail });

  const hashedPassword = await bcrypt.hash(password, 12);
  const newUser = new ExternalUser({
    email,
    enterprice,
    password: hashedPassword,
  });
  await newUser.save();
  return res.status(200).send({ message: genericMessages.success.users.created });
};

export const externalLoginUser: AsyncController = async (req, res) => {
  const { email, password } = req.body;

  if (!email || !password) return res.status(400).send({ message: genericMessages.errors.missingBody });

  const user = await ExternalUser.findOne({ email });
  if (!user) return res.status(400).send({ message: genericMessages.errors.missingBody });
  req.userId = user._id;

  //comparando contraseñas
  const rightPassword = await bcrypt.compare(password, user.password);
  if (!rightPassword) {
    return res.status(400).send({ message: genericMessages.errors.missingBody });
  }
  const accessToken = jwt.sign({ userId: user._id }, externalAccessTokenSecret, {
    expiresIn: '72h',
  });

  const userData = {
    email: user.email,
  };

  return res.status(200).send({ message: genericMessages.success.users.loggin, accessToken, user: userData });
};

export const signInWithPhoneNumber: AsyncController = async (req, res) => {
  const { phoneNumber } = req.body;
  if (!phoneNumber) {
    logger.error(`[signInWithPhoneNumber] Required field is missing phone: ${phoneNumber}`);
    return res.status(400).send({ success: false, message: 'Phone number and country code are required' });
  }
  if (phoneNumber === '+************') {
    logger.info('[signInWithPhoneNumber] request from google play store');
    return res.status(200).send({ success: true, message: 'Dummy user request fulfilled' });
  }
  const associate = await Associate.findOne({
    phone: phoneNumber,
  }).lean();
  let errorMessage: string;
  if (associate && associate.active) {
    const associateId = associate?._id.toString();
    logger.info(`[signInWithPhoneNumber] associate found with ID: ${associateId}`);
    const isOTPGeneratedAndSend = await generateAndSendOTP({
      phone: phoneNumber,
      email: associate.email,
      id: associateId,
      country: associate.country,
    });
    logger.info(`[signInWithPhoneNumber] OTP generated and send status: ${isOTPGeneratedAndSend}`);
    if (isOTPGeneratedAndSend) {
      return res.status(200).send({ success: true, message: 'OTP send successfully' });
    } else {
      errorMessage = 'Unable to send OTP';
    }
  } else {
    logger.error(`[signInWithPhoneNumber] Associate not found`);
    errorMessage = 'Associate not found';
  }
  return res.status(404).send({ success: false, message: errorMessage });
};

export const verifyOTP: AsyncController = async (req, res) => {
  try {
    const { phone, otp } = req.body;

    // Validate inputs
    if (!otp || !phone) {
      logger.error(`[verifyOTP] Required fields are missing`);
      return res.status(400).send({
        success: false,
        message: 'Phone or OTP or country code is required',
      });
    }

    if (phone === '+************' && otp === '123456') {
      logger.info('[verifyOTP] request from google play store');
      return res.status(200).send({
        success: true,
        message: 'OTP verified successfully',
        data: {
          accessToken: 'Hi! this is an google testing account',
          associateId: '6565289ea8b3c0819a5ba6ff',
        },
      });
    }

    const associate = await Associate.findOne({ phone: phone }).lean();

    if (!associate || !associate.active) {
      logger.error(`[verifyOTP] Associate not found`);
      return res.status(404).send({ success: false, message: 'Associate not found' });
    }

    const associateId = associate!._id.toString();

    const isOTPVerified = await verifyCode({
      phone: phone,
      email: associate.email,
      id: associateId,
      country: associate.country,
      code: otp,
    });

    if (!isOTPVerified) {
      logger.error(`[verifyOTP] Invalid or expired OTP`);
      return res.status(403).send({ success: false, message: 'Invalid or expired OTP' });
    }
    logger.info(`[verifyOTP] Generating OTP token`);
    const accessToken = jwt.sign({ userId: associateId }, accessTokenSecret, {
      expiresIn: '365d',
    });
    logger.info(`[verifyOTP] OTP verified successfully and token generated`);
    return res.status(200).send({
      success: true,
      message: 'OTP verified successfully',
      data: {
        accessToken: accessToken,
        associateId: associateId,
      },
    });
  } catch (error) {
    logger.error(`[verifyOTP] Error verifying OTP`, error);
    return res.status(500).send({
      success: false,
      message: 'Error verifying OTP',
    });
  }
};

const getAdmissionRequestDetails = async (phone: String) => {
  const admissionRequest = await AdmissionRequestMongo.findOne({
    'personalData.phone': phone,
  }).lean();

  if (!admissionRequest) {
    logger.error('[signInWithPhoneNumber] phone number does not exist');
    throw new NotFoundException({ code: '404', message: 'Phone number does not exist' });
  }

  const id = admissionRequest._id.toString();
  const email = admissionRequest.personalData.email;
  const country =
    admissionRequest.personalData.country === Country.us
      ? CountriesEnum['United States']
      : CountriesEnum.Mexico;

  logger.info(`[signInWithPhoneNumber] admission request found with ID: ${id}`);

  return {
    id,
    email,
    country,
    idType: RequestIdType.admissionRequest,
  };
};

export const signInWithPhoneNumberV2: AsyncController = async (req, res) => {
  const { phoneNumber, source } = req.body;

  try {
    if (!phoneNumber || !source) {
      logger.error(
        `[signInWithPhoneNumber] Required field is missing phone: ${phoneNumber} or sourc: ${source}`
      );
      return res.status(400).send({ success: false, message: 'Phone number and source are required' });
    }

    if (phoneNumber === '+************') {
      logger.info('[signInWithPhoneNumber] request from google play store');
      return res.status(200).send({ success: true, message: 'Dummy user request fulfilled' });
    }

    let id: string;
    let email: string;
    let country: CountriesEnum;

    const associate = await Associate.findOne({
      phone: phoneNumber,
    }).lean();

    if (associate) {
      if (!associate.active) {
        logger.info(`[signInWithPhoneNumber] Associate found with phone ${phoneNumber} but not active`);
        return res.status(400).send(errorCodeUtils.ASSOCIATE_NOT_ACTIVE);
      }
      id = associate?._id.toString();
      email = associate.email;
      country = associate.country;
      logger.info(`[signInWithPhoneNumber] associate found with ID: ${id}`);
    } else if (source === RequestGenerationSource.onboardingSupportPlatform) {
      logger.info(
        `[signInWithPhoneNumber] request is generated from ${RequestGenerationSource.onboardingSupportPlatform} and associate is not found against phone ${phoneNumber} so rejecting request`
      );
      return res.status(400).send(errorCodeUtils.USER_NOT_ALLOWED);
    } else {
      ({ id, email, country } = await getAdmissionRequestDetails(phoneNumber));
    }

    logger.info('[signInWithPhoneNumber] Generating OTP...');
    const isOTPGeneratedAndSend = await generateAndSendOTP({
      id,
      email,
      country,
      phone: phoneNumber,
    });
    logger.info('[signInWithPhoneNumber] OTP generated and send');

    if (isOTPGeneratedAndSend) {
      logger.info(`[signInWithPhoneNumber] OTP send successfully to phone ${phoneNumber}`);
      return res.status(200).send({ success: true, message: 'OTP send successfully' });
    } else {
      logger.info(`[signInWithPhoneNumber] Unable to send OTP to phone ${phoneNumber}`);
      return res.status(400).send(errorCodeUtils.UNABLE_TO_GENERATE_OR_SEND_OTP);
    }
  } catch (error) {
    logger.error(`[signInWithPhoneNumber] Error verifying OTP`, error);
    if (error instanceof NotFoundException) {
      return res.status(404).send({
        code: 'OTP_NOT_FOUND',
        status: 404,
        message: error.message,
      });
    }
    return res.status(500).send({
      code: 'INTERNAL_SERVER_ERROR',
      status: 500,
      message: 'Error verifying user',
    });
  }
};

export const verifyOTPV2: AsyncController = async (req, res) => {
  try {
    const { phone, otp } = req.body;

    // Validate inputs
    if (!otp || !phone) {
      logger.error(`[verifyOTP] Required fields are missing`);
      return res.status(400).send({
        success: false,
        message: 'Phone or OTP or country code is required',
      });
    }

    if (phone === '+************' && otp === '123456') {
      logger.info('[verifyOTP] request from google play store');
      return res.status(200).send({
        success: true,
        message: 'OTP verified successfully',
        data: {
          id: '6565289ea8b3c0819a5ba6ff',
          idType: RequestIdType.associate,
          accessToken: 'Hi! this is an google testing account',
        },
      });
    }

    let id: string;
    let email: string;
    let idType: RequestIdType;
    let country: CountriesEnum;

    const associate = await Associate.findOne({ phone: phone }).lean();

    if (associate) {
      if (!associate.active) {
        logger.info(`[verifyOTP] Associate found with phone ${phone} but not active`);
        return res.status(400).send({
          code: 'ASSICIATE_NOT_ACTIVE',
          status: 1001,
          message: 'Associate found but not active yet',
        });
      }
      id = associate?._id.toString();
      email = associate.email;
      country = associate.country;
      idType = RequestIdType.associate;
      logger.info(`[verifyOTP] associate found with ID: ${id}`);
    } else {
      ({ id, email, country, idType } = await getAdmissionRequestDetails(phone));
    }

    const isOTPVerified = await verifyCode({
      id,
      phone,
      email,
      country,
      code: otp,
    });

    if (!isOTPVerified) {
      logger.error(`[verifyOTP] Invalid or expired OTP`);
      return res.status(400).send(errorCodeUtils.INVALID_OR_EXPIRED_OTP);
    }

    const accessToken = jwt.sign({ userId: id }, accessTokenSecret, {
      expiresIn: '365d',
    });

    logger.info(`[verifyOTP] OTP verified successfully and token generated`);

    return res.status(200).send({
      success: true,
      message: 'OTP verified successfully',
      data: {
        id,
        idType,
        accessToken,
      },
    });
  } catch (error) {
    logger.error(`[verifyOTP] Error verifying OTP`, error);
    if (error instanceof NotFoundException) {
      return res.status(404).send({
        success: false,
        message: error.message,
      });
    }
    return res.status(500).send({
      success: false,
      message: 'Error verifying OTP',
    });
  }
};
