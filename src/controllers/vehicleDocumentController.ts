import { Request, Response, NextFunction } from 'express';
import { logger } from '../clean/lib/logger';
import { repoGeneratePresignedPutUrl } from '../clean/data/s3Repositories';
import { vehicleDocumentProcessingService } from '../services/vehicleDocumentProcessingService';
import {
  PresignedUrlRequestItem,
  PresignedUrlResponseItem,
  ProcessDocumentsRequest,
} from '../types&interfaces/vehicleDocuments';
import { generateUniqueFileNameForS3 } from '../utils/fileNameUtils';
import { SLACK_VEHICLE_DOC_UPLOAD_CHANNEL_ID } from '../constants';

export const vehicleDOcumentsSingleOCRExtraction = async (req: Request, res: Response) => {
  try {
    const { documentType, vehicleId, plates, country = 'Mexico' } = req.body;
    const file = req.file as unknown as Express.Multer.File;

    // Note: Basic validation is now handled by the validateOCRExtractionRequest middleware
    // Only business logic validation remains here

    const result = await vehicleDocumentProcessingService.extractOCRDataOnly({
      file,
      documentType,
      vehicleId,
      country,
      plates,
    });

    // Handle validation failures and other errors
    if (!result.success) {
      const statusCode = result.statusCode || 500;
      return res.status(statusCode).json(result);
    }

    return res.json(result);
  } catch (error) {
    logger.error('[OCR Route] Error processing OCR request:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Error interno del servidor',
      },
    });
  }
};

export const generatePresignedUrlsForVehicleDocuments = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { filesToUpload } = req.body as { filesToUpload: PresignedUrlRequestItem[] };
  const userId = (req as any).userId.userId as string; // from verifyToken middleware

  // Note: Basic validation is now handled by the validatePresignedUrlRequest middleware

  try {
    logger.info(
      `[VehicleDocsCtrl] Generating presigned URLs for ${filesToUpload.length} files. User: ${userId}`
    );
    const presignedUrlsData: PresignedUrlResponseItem[] = [];

    for (const fileItem of filesToUpload) {
      if (!fileItem.fileName || !fileItem.contentType || !fileItem.documentCategory) {
        logger.warn(
          '[VehicleDocsCtrl] Skipping file due to missing fileName, contentType, or documentCategory:',
          fileItem
        );
        continue;
      }

      // Basic validation for file size can be done on frontend, but backend can also check metadata if provided
      // For now, we assume frontend handles the 5MB check before requesting URL

      const uniqueFileName = generateUniqueFileNameForS3(fileItem.fileName);
      // Structured S3 path: vehicle-documents/<category>/<userId>/<uniqueFileName>
      const s3Key = `vehicle-documents/${fileItem.documentCategory}/${userId}/${uniqueFileName}`;

      const presignedUrl = await repoGeneratePresignedPutUrl(s3Key, fileItem.contentType);
      presignedUrlsData.push({
        originalFileName: fileItem.fileName,
        presignedUrl,
        s3Key,
        documentCategory: fileItem.documentCategory,
        contentType: fileItem.contentType,
      });
    }

    if (presignedUrlsData.length === 0 && filesToUpload.length > 0) {
      return res
        .status(400)
        .json({ message: 'None of the provided file details were valid for URL generation.' });
    }

    logger.info(
      `[VehicleDocsCtrl] Successfully generated ${presignedUrlsData.length} presigned URLs. User: ${userId}`
    );
    return res.status(200).json({
      message: 'Presigned URLs generated successfully.',
      data: presignedUrlsData,
    });
  } catch (error) {
    logger.error('[VehicleDocsCtrl] Error generating presigned URLs:', error);
    return next(error); // Pass to global error handler
  }
};

export const processUploadedVehicleDocuments = async (req: Request, res: Response, next: NextFunction) => {
  const { documents, userName, userEmail, country, region, contractNumber } =
    req.body as ProcessDocumentsRequest;
  const userId = (req as any).userId.userId as string; // from verifyToken middleware

  // Note: Basic validation is now handled by the validateDocumentProcessingRequest middleware

  try {
    logger.info(
      `[VehicleDocsCtrl] Received request to process ${documents.length} documents. User: ${userName} (${userId})`
    );

    // --- Trigger asynchronous processing ---
    // No await here, so the request returns immediately.
    vehicleDocumentProcessingService.processVehicleDocumentBatch({
      documents,
      userId,
      userName,
      userEmail,
      slackChannelId: SLACK_VEHICLE_DOC_UPLOAD_CHANNEL_ID,
      // optional parameters passed for future use
      country,
      region,
      contractNumber,
    });

    return res.status(202).json({
      message: `Processing for ${documents.length} documents started. You will receive a Slack notification with the results.`,
    });
  } catch (error) {
    logger.error('[VehicleDocsCtrl] Error initiating document processing:', error);
    return next(error); // Pass to global error handler
  }
};
