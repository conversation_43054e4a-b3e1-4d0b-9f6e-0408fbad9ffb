import { Router } from 'express';
import {
  isElectric,
  getVerificationInfo,
  uploadCustomerEvidence,
  getVerificationStatus,
  getHologramOptions,
  submitVerification,
} from '../controllers/verificentros';
import { upload } from '../multer/multer';

const router = Router();
// API Endpoints with validation

const customerEvidenceFields = [
  { name: 'verificationCertificate', maxCount: 1 },
  { name: 'hologramPhoto', maxCount: 1 },
];

// Rutas existentes
router.get('/is-electric/:plates', isElectric);

// Nuevas rutas para customer evidence
router.get('/hologram-options', getHologramOptions);
router.get('/verification/:uniqueLink', getVerificationInfo);
router.get('/verification/:uniqueLink/status', getVerificationStatus);

// Ruta compleja con subida de archivos (mantener por compatibilidad)
router.post(
  '/verification/:uniqueLink/evidence',
  upload.fields(customerEvidenceFields),
  uploadCustomerEvidence
);

// Ruta simplificada - solo recibe datos JSON del frontend
router.post('/verification/:uniqueLink/submit', submitVerification);

export default router;
