import { Router } from 'express';
import {
  isElectric,
  getVerificationInfo,
  uploadCustomerEvidence,
  getVerificationStatus,
  getHologramOptions,
  submitVerificationByPlate,
  getVerificationByPlate,
} from '../controllers/verificentros';
import { upload } from '../multer/multer';

const router = Router();
// API Endpoints with validation

const customerEvidenceFields = [
  { name: 'verificationCertificate', maxCount: 1 },
  { name: 'hologramPhoto', maxCount: 1 },
];

// Rutas existentes
router.get('/is-electric/:plates', isElectric);

// Nuevas rutas para customer evidence
router.get('/hologram-options', getHologramOptions);

// Rutas por uniqueLink (mantener por compatibilidad)
router.get('/verification/:uniqueLink', getVerificationInfo);
router.get('/verification/:uniqueLink/status', getVerificationStatus);
router.post(
  '/verification/:uniqueLink/evidence',
  upload.fields(customerEvidenceFields),
  uploadCustomerEvidence
);

// Rutas simplificadas por placas - RECOMENDADAS
router.get('/plate/:plate', getVerificationByPlate);
router.post('/plate/:plate/submit', submitVerificationByPlate);

export default router;
