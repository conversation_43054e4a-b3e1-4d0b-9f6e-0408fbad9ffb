import { Router } from 'express';
import { isElectric, uploadVerificationData } from '../controllers/verificentros';
import { upload } from '../multer/multer';

const router = Router();
// API Endpoints with validation

const verificationFields = [
  { name: 'holograma', maxCount: 1 },
  { name: 'certificado', maxCount: 1 },
];

router.get('/is-electric/:plates', isElectric);
router.post('/upload-verification', upload.fields(verificationFields), uploadVerificationData);

export default router;
