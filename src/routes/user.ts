import { Router } from 'express';
import {
  createUserVehiclesRestrictions,
  deleteUser,
  editUserByAdmin,
  getAllHomeVisitors,
  getAllUsers,
  getUserAssignPermissions,
  getUserData,
  getUserVehiclesRestrictions,
  sideData,
  updateUserById,
  updateUserHomeVisitStatus,
  updateUserVehiclesRestrictions,
  verifyPassword,
} from '../controllers/user';
import { upload } from '../multer/multer';

const user = Router();

user.get('/homevisitors', getAllHomeVisitors);
user.get('/getAllUsers', getAllUsers);
user.get('/sideData/', sideData);
user.get('/:id', getUserData);
user.get('/assignedPermissions/:id', getUserAssignPermissions);
user.patch('/update/:id', editUserByAdmin);
user.patch('/:id', upload.single('image'), updateUserById);
user.post('/verifyPassword/:id', verifyPassword);
user.patch('/update-home-visit-status/:id', updateUserHomeVisitStatus);
user.delete('/:id', deleteUser);
/* VEHICLE RESTRICTIONS FOR THE USERS */

// user.use('/restrictions');
user.get('/restrictions/:userId', getUserVehiclesRestrictions);
user.post('/restrictions/create', createUserVehiclesRestrictions);
user.put('/restrictions/update', updateUserVehiclesRestrictions);

export default user;
