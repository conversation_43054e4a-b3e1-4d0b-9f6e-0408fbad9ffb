import { Request } from 'express';
import { UnprocessableEntityException } from '../errors/exceptions';
import { z, AnyZodObject, ZodError } from 'zod';
import { Types } from 'mongoose';
import {
  AdmissionRequestDocumentType,
  GigPlatform,
  HomeVisitStatus,
  MediaType,
  EntityType,
  VehicleType,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestAdditionalDocumentType,
  MLModels,
} from '../domain/enums';

// validate that the string is a valid ObjectId

const validateObjectId = z.string().refine((val) => Types.ObjectId.isValid(val), {
  message: 'Invalid ID',
});

// validate that the string is YYYY-MM-DD

const validateDateString = z.string().refine(
  (val) => {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    return dateRegex.test(val);
  },
  {
    message: 'Invalid date format',
  }
);

// Validate request or throw a UnprocessableEntityException and parse issues
export const validateRequestBody = (schema: AnyZodObject, body: Request['body']) => {
  try {
    schema.parse(body);
  } catch (error) {
    throw new UnprocessableEntityException(error as ZodError);
  }
};

// Validate request.params or throw a UnprocessableEntityException and parse issues
export const validateRequestParams = (schema: AnyZodObject, params: Request['params']) => {
  try {
    schema.parse(params);
  } catch (error) {
    throw new UnprocessableEntityException(error as ZodError);
  }
};

export const createAdmissionRequestValidation = z.object({
  vehicleType: z.nativeEnum(VehicleType).optional(),
});

export const updateAdmissionRequestCustomerValidation = z.object({
  firstName: z.string().min(2).max(255).optional().nullable(),
  lastName: z.string().min(2).max(255).optional().nullable(),
  phone: z.string().min(10).max(14).optional().nullable(),
  email: z.string().email().optional().nullable(),
});

export const getPublicAdmissionRequestValidation = z.object({
  requestId: validateObjectId,
});

// Decided to use only envelope validation since it can change in the future
export const palencaWebhookPayloadValidation = z.object({
  webhook_url: z.string(),
  data: z.object({
    webhook_action: z.string(),
    user_id: z.string(),
    account_id: z.string().optional().nullable(),
    external_id: z.string().optional().nullable(),
    country: z.string().optional().nullable(),
    platform: z.string().optional().nullable(),
  }),
});

export const taskEarningsValidation = z.object({
  accountId: z.string(),
  platform: z.string(),
  requestId: z.string(),
});

export const taskMetricsValidation = z.object({
  accountId: z.string(),
  platform: z.string(),
  requestId: z.string(),
});

export const updatePersonalDataValidation = z.object({
  firstName: z.string().min(2).max(255).optional().nullable(),
  lastName: z.string().min(2).max(255).optional().nullable(),
  phone: z.string().min(10).max(14).optional().nullable(),
  email: z.string().email().optional().nullable(),
  birthdate: validateDateString.optional().nullable(),
  taxId: z.string().min(12).max(13).optional().nullable(),
  nationalId: z.string().min(18).max(18).optional().nullable(),
  postalCode: z.string().min(5).max(5).optional().nullable(),
  city: z.string().min(3).max(255).optional().nullable(),
  state: z.string().min(3).max(255).optional().nullable(),
  neighborhood: z.string().max(255).optional().nullable(),
  street: z.string().min(3).max(255).optional().nullable(),
  streetNumber: z.string().max(255).optional().nullable(),
  department: z.string().max(255).optional().nullable(),
  ssn: z
    .string()
    .regex(/^\d{3}-\d{2}-\d{4}$/)
    .optional()
    .nullable(),
  rideShareTotalRides: z.number().optional().nullable(),
  avgEarningPerWeek: z.number().optional().nullable(),
  termsAndConditions: z.boolean().optional().nullable(),
  dataPrivacyConsentForm: z.boolean().optional().nullable(),
  ficoScore: z.string().optional().nullable(),
  criminalBackgroundCheck: z.string().optional().nullable(),
  motorVehicleRecordCheck: z.string().optional().nullable(),
  vehicleSelected: z.string().optional().nullable(),
  privacyPolicy: z.boolean().optional().nullable(),
  ocnBackgroundAndCreditCheckForApplication: z.boolean().optional().nullable(),
});

export const updateHomeVisitDataValidation = z.object({
  personalData: z
    .object({
      firstName: z.string().max(255).optional().nullable(),
      lastName: z.string().max(255).optional().nullable(),
      phone: z.string().max(14).optional().nullable(),
      email: z.string().email().optional().nullable(),
      birthdate: validateDateString.optional().nullable(),
      taxId: z.string().max(13).optional().nullable(),
      nationalId: z.string().max(18).optional().nullable(),
      postalCode: z.string().max(5).optional().nullable(),
      city: z.string().max(255).optional().nullable(),
      state: z.string().max(255).optional().nullable(),
      neighborhood: z.string().max(255).optional().nullable(),
      street: z.string().max(255).optional().nullable(),
      streetNumber: z.string().max(255).optional().nullable(),
      department: z.string().max(255).optional().nullable(),
      ssn: z
        .string()
        .regex(/^\d{3}-\d{2}-\d{4}$/)
        .optional()
        .nullable(),
      rideShareTotalRides: z.number().optional().nullable(),
      avgEarningPerWeek: z.number().optional().nullable(),
      termsAndConditions: z.boolean().optional().nullable(),
      dataPrivacyConsentForm: z.boolean().optional().nullable(),
      nationality: z.string().optional().nullable(),
      age: z.number().optional().nullable(),
      occupation: z.string().optional().nullable(),
      homePhone: z.string().optional().nullable(),
      timeInResidency: z.string().optional().nullable(),
      municipality: z.string().optional().nullable(),
      maritalStatus: z.string().optional().nullable(),
      dependents: z.string().optional().nullable(),
      spouseOrPartnerIncome: z.string().optional().nullable(),
      partnerSourceOfIncome: z.string().optional().nullable(),
      partnerName: z.string().optional().nullable(),
      partnerPhone: z.string().optional().nullable(),
      noOfDependents: z.number().optional().nullable(),
      dependendsInfo: z
        .array(
          z.object({
            dependendName: z.string(),
            dependendPhone: z.string().nullable(),
            dependentRelationship: z.string(),
          })
        )
        .optional()
        .nullable(),
      ownACar: z.string().optional().nullable(),
      carLeasingtime: z.string().optional().nullable(),
      carMake: z.string().optional().nullable(),
      carModel: z.string().optional().nullable(),
      ownDebt: z.string().optional().nullable(),
      outStandingDebt: z.string().optional().nullable(),
      doesDebtAffectPersonalFinance: z.string().optional().nullable(),
      references: z
        .object({
          reference1Name: z.string(),
          reference1Phone: z.string(),
          reference1Relationship: z.string(),
          reference1Address: z.string(),
          reference2Name: z.string(),
          reference2Phone: z.string(),
          reference2Relationship: z.string(),
          reference2Address: z.string(),
          reference3Name: z.string(),
          reference3Phone: z.string(),
          reference3Relationship: z.string(),
          reference3Address: z.string(),
        })
        .optional()
        .nullable(),
      learnAboutOcn: z.string().optional().nullable(),
      doesItApplyToElectricCars: z.string().optional().nullable(),
    })
    .optional(),
  homeVisitData: z
    .object({
      visitDate: z.preprocess((arg) => {
        if (typeof arg === 'string') {
          return new Date(arg);
        }
        return arg;
      }, z.date().optional().nullable()),
      visitTime: z.string().optional().nullable(),
      houseInformation: z
        .object({
          ownProperty: z.string(),
          nameOfOwner: z.string(),
          ownerRelative: z.string(),
          ownerRelativeRelation: z.string(),
          ownerPhone: z.string(),
          typeOfHousing: z.string(),
          noOfBedrooms: z.number(),
          livingRoom: z.string(),
          dinningRoom: z.string(),
          kitchen: z.string(),
          television: z.string(),
          audioSystem: z.string(),
          stove: z.string(),
          refrigerator: z.string(),
          washingMachine: z.string(),
        })
        .optional(),
      images: z.array(validateObjectId).optional(),
      proofOfPropertyOwnership: z.array(validateObjectId).optional(),
      visitorEmailAddress: z.string().optional(),
      doesProofOfAddressMatchLocation: z.string().optional(),
      characteristicsOfGarage: z.string().optional(),
      behaviourOfCustomerDuringCall: z.string().optional(),
      comments: z.string().optional(),
      status: z.nativeEnum(HomeVisitStatus).optional(),
      homeVisitStepsStatus: z
        .object({
          personal: z.string().optional(),
          contact: z.string().optional(),
          address: z.string().optional(),
          family: z.string().optional(),
          property: z.string().optional(),
          automobile: z.string().optional(),
          debt: z.string().optional(),
          references: z.string().optional(),
          outcome: z.string().optional(),
        })
        .optional(),
      reasonOfRejection: z.string().optional(),
      statusReason: z.string().optional(),
      suggestedStatus: z.string().optional(),
      homeImages: z
        .object({
          homeImagesFront: z.array(validateObjectId).optional(),
          homeImagesGarage: z.array(validateObjectId).optional(),
          homeImagesSurroundings: z.array(validateObjectId).optional(),
        })
        .optional(),
    })
    .optional(),
  isHomeVisitData: z.boolean(),
  isPersonalData: z.boolean().optional(),
});

export const mediaUploadValidation = z.object({
  mediaType: z.nativeEnum(MediaType),
});

export const updateRequestDocumentsValidation = z.object({
  mediaId: validateObjectId,
  type: z.nativeEnum({ ...AdmissionRequestDocumentType, ...AdmissionRequestAdditionalDocumentType }),
});

export const requestDocumentsValidation = z.object({
  updates: z.array(updateRequestDocumentsValidation),
});

export const updateRequestDocumentsValidationUS = z.object({
  mediaId: validateObjectId,
  type: z.nativeEnum(AdmissionRequestDocumentTypeUS),
});

export const requestDocumentsValidationUS = z.object({
  updates: z.array(updateRequestDocumentsValidationUS),
});

export const updateRequestStatusValidation = z.object({
  approved: z.boolean(),
});

export const addPalencaAccountValidation = z.object({
  accountId: z.string(),
  platform: z.nativeEnum(GigPlatform),
});

export const getPlatformEarningsValidation = z.object({
  requestId: validateObjectId,
  platform: z.nativeEnum(GigPlatform),
});

export const paginationOptionsValidation = z.object({
  itemsPerPage: z.number().min(1).max(100).optional(),
  page: z.number().min(1).optional(),
});

export const searchAdmissionRequestsValidation = z.object({
  country: z.string().min(0).max(255),
  q: z.string().min(0).max(255),
  status: z.string().min(0).max(255).optional(),
  options: paginationOptionsValidation,
});

export const getPlatformMetricsValidation = z.object({
  requestId: validateObjectId,
  platform: z.nativeEnum(GigPlatform),
});

export const getEventsValidation = z.object({
  entityId: validateObjectId,
  entityType: z.nativeEnum(EntityType),
});

export const updatePlatformMetricValidation = z.object({
  lifetimeTrips: z.number(),
  acceptanceRate: z.number(),
  cancellationRate: z.number(),
  rating: z.number(),
  timeSinceFirstTrip: z.number(),
});

export const addAvalDataValidation = z.object({
  name: z.string().min(2).max(255),
  phone: z.string().min(10).max(14),
  email: z.string().email(),
  location: z.string(),
});

export const retryModelAnalysisValidation = z.object({
  modelName: z.nativeEnum(MLModels, {
    errorMap: () => ({ message: 'Invalid model name provided' }),
  }),
});

export const latLongLookupValidation = z.object({
  latitude: z.number(),
  longitude: z.number(),
  ipAddress: z.string(),
});

export const ipLookupValidation = z.object({
  ipAddress: z.string(),
});
