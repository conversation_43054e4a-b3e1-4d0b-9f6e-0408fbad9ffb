/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Request, Response } from 'express';
import axios from 'axios';
import {
  createAdmissionRequestValidation,
  validateRequestBody,
  getPublicAdmissionRequestValidation,
  validateRequestParams,
  palencaWebhookPayloadValidation,
  updatePersonalDataValidation,
  mediaUploadValidation,
  requestDocumentsValidation,
  addPalencaAccountValidation,
  getPlatformEarningsValidation,
  searchAdmissionRequestsValidation,
  getPlatformMetricsValidation,
  getEventsValidation,
  requestDocumentsValidationUS,
  updatePlatformMetricValidation,
  updateHomeVisitDataValidation,
  addAvalDataValidation,
  latLongLookupValidation,
  ipLookupValidation,
} from './validators';
import {
  AdmissionRequest,
  Document,
  HomeVisit,
  PaginationSearchOptions,
  PalencaWebhook,
  PlatformMetric,
  RequestAvalData,
  RequestDocument,
  RequestPersonalData,
  RequestLocationData,
} from '../domain/entities';
import {
  createAdmissionRequest,
  updateRequestPersonalData,
  uploadMedia,
  updateRequestDocuments,
  addPalencaAccountToAdmissionRequest,
  retrieveMostRecent12WeeksEarnings,
  searchPaginatedAdmissionRequests,
  getRequestDocumentsAnalysis,
  approveRequestDocument,
  rejectRequestDocument,
  approveRequestDocumentAnalysis,
  getHomeVisit,
  retrieveEvents,
  approveRiskAnalysis,
  rejectRiskAnalysis,
  getAdmissionRequestByIdOrThrow,
  evaluatePalencaWebhook,
  rejectRequestDocumentAnalysis,
  updatePlatformMetric,
  updateHomeVisit,
  addAvalDataToAdmissionRequest,
  approveRequestSocialAnalysis,
  rejectRequestSocialAnalysis,
  executeSocialAnalysis,
  retryAnalysis,
  executeDocumentsAnalysis,
  addLocationDataToAdmissionRequest,
  googleReverseGeocoding,
  lookupIPAddress,
  sendHilosLocationGatheringMessage,
  resetHomeVisitScheduleLinkSendDateToToday,
  sendHomeImageUploadNotificationMessage,
} from '../domain/usecases';
import {
  AdmissionRequestSerializer,
  documentSerializer,
  paginatedSerializer,
  requestDocumentsAnalysisSerializer,
  requestHomeVisitSerializer,
  requestWeeklyEarningsSerializer,
  requestMetricSerializer,
  eventsSerializer,
  publicAdmissionRequestSerializer,
  PlatformMetricSerializer,
} from './serializers';

import {
  AdmissionRequestAdditionalDocumentType,
  AdmissionRequestDocumentType,
  Country,
  countryCodeMap,
  DocumentClassification,
  DriverSourceType,
  GigPlatform,
  PalencaWebhookAction,
  RequestDocumentStatus,
} from '../domain/enums';

import { MissingMediaFileException } from '../errors/exceptions';
import {
  DEFAULT_PAGINATION_LIMIT,
  DRIVER_WEB_APP_URL,
  HILOS_URL_SEND_TEMPLATE,
  REQUEST_URL,
  HILOS_TEMPLATE_ID,
  HILOS_API_KEY,
  HUBSPOT_TOKEN,
  HUBSPOT_URL,
  cityOptionsMap,
  cityOptions2Map,
} from '../../constants';
import { repoRetrievePalencaPlatformMetric } from '../data/mongoRepositories';
import { logger } from '../lib/logger';
import { AdmissionRequestMongo } from '../../models/admissionRequestSchema';
import {
  sendEmailToMXCustomerAfterRegistering,
  sendEmailToUSCustomerAfterRegistering,
} from '../../modules/platform_connections/emailFunc';
import { repoGetMediaSignedUrl } from '../data/s3Repositories';
import { dealUpdate, fileVerification } from '../../services/hubspot';
import HubspotBatch from '../../models/hubspotBatch';
import { getOcnUsername } from '../../services/onboarding/getOCNUser';
import { saveCampaignTracking } from '@/database/supabase';
import { vehicleViolationService } from '@/modules/VehicleViolation/services/vehicleViolation.service';


export const uploadFiles = async (files: any[], mediaType?: string): Promise<Document[]> => {
  if (!files || files.length === 0) {
    logger.info(`[uploadFiles] No files found in request.`);
    throw new MissingMediaFileException();
  }

  const uploadedFilesPromises = await Promise.allSettled(
    files.map(async (file: any) => {
      // If mediaType is not provided, use the fieldname from the file
      const fileMediaType = mediaType || file.fieldname;
      const uploaded = await uploadMedia({ file, mediaType: fileMediaType });
      logger.info(`[uploadFiles] File uploaded: ${uploaded.id}`);
      return uploaded;
    })
  );

  const uploadedFiles: Array<Document> = [];
  uploadedFilesPromises.forEach((promise) => {
    if (promise.status === 'fulfilled') {
      uploadedFiles.push(promise.value);
    }
  });

  return uploadedFiles;
};

export const createAdmissionRequestResource = async (req: Request, response: Response) => {
  validateRequestBody(createAdmissionRequestValidation, req.body);
  const {
    firstName,
    lastName,
    phone,
    email,
    vehicleType,
    city = 'CDMX/EDOMEX',
    country = 'mx',
    vehicleSelected,
    state,
    referrer,
    postalCode,
    source,
    fuente,
    clientIpAddress,
    sourceOption,
    isEdit,
  } = req.body;

  const props = req.body;
  const additionalProperties = [
    { key: 'nombre_del_ejecutivo_de_ventas', value: props.nombre_del_ejecutivo_de_ventas },
    { key: 'telefono_del_ejecutivo_de_ventas', value: props.telefono_del_ejecutivo_de_ventas },
    { key: 'nombre_de_la_agencia', value: props.nombre_de_la_agencia },
  ];


  const personalData = new RequestPersonalData({
    firstName,
    lastName,
    phone,
    email,
    country,
    vehicleSelected,
    city: city,
    state: state,
    postalCode: postalCode,
    referrer,
  });

  const userData = await getOcnUsername();
  const userId = userData._id.toString();
  // const countryCode = country === 'mx' ? '+52' : '+1';
  const countryCode = countryCodeMap[country as Country];
  let phoneNumber = phone.toString();
  if (!phoneNumber.includes('+')) { // If phone number does not start with +, add the country code
    phoneNumber = `${countryCode}${phone}`;
  }
  personalData.phone = phoneNumber;
  try {
    // const existRequest = await AdmissionRequestMongo.findOne({ 'personalData.email': email });
    // make a query to findOne by email or phone
    const existRequest = await AdmissionRequestMongo.findOne({
      $or: [
        // { 'personalData.email': email },
        { 'personalData.phone': phoneNumber },
        { 'personalData.email': email, 'personalData.phone': phone },
      ],
    });

    if (existRequest) {
     if(isEdit) {
       await AdmissionRequestMongo.deleteOne({ _id: existRequest._id });
     } else {
       const basicInfo = {
         firstName: existRequest.personalData.firstName,
         email: existRequest.personalData.email,
         phone: existRequest.personalData.phone,
       };
       logger.info(`[createAdmissionRequest] User with phone number already exists: ${existRequest._id}`);
       response.status(409).json({
         message: 'Client with email already exists',
         requestId: existRequest._id,
         hubspot: existRequest.hubspot,
         hilos: existRequest.hilos,
         basicInfo,
       });
       return;
    }
  }
  } catch (error) {
    console.error({ error });
    response.status(500).json({ message: 'Error al enviar solicitud' });
  }

  const admissionRequest = await createAdmissionRequest(
    personalData,
    userId,
    clientIpAddress,
    vehicleType,
    source
  );
  const serialized = AdmissionRequestSerializer(admissionRequest);
  const requestId = admissionRequest.id;

  if (requestId && personalData.country === Country.us) {
    await saveCampaignTracking(sourceOption, requestId);
  }
  if (requestId && personalData.country === Country.br) {
    // TO DO: Send campaign tracking for BR
  }

  try {
    // Llamadas a Hubspot e Hilos

    const properties: any = {
      firstname: firstName,
      lastname: lastName,
      email,
      phone: phoneNumber,
      city,
      request_id: `${REQUEST_URL}/${requestId}`,
      state,
      postal_code: postalCode,
      country: country.toUpperCase(),
      hs_lead_status: 'NEW',
    };

    if (admissionRequest.source && admissionRequest.source !== 'Agencia') {
      properties.fuente = admissionRequest.source;
    } else {
      if (req.body.fuente) {
        properties.fuente = req.body.fuente;
      }
      if (req.body.source) {
        properties.source = req.body.source;
      }
    }

    additionalProperties.forEach((property) => {
      if (property.value) {
        properties[property.key] = property.value;
      }
    });
    console.log('properties', properties);

    const cityWithSelector = cityOptionsMap[city] || cityOptions2Map[city];
    if (cityWithSelector) {
      properties.ciudad__con_selector_ = cityWithSelector
    }

    const hubspot = await axios.post(
      HUBSPOT_URL,
      {
        properties,
      },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_TOKEN}`,
        },
      }
    );
    serialized.data.hubspot = hubspot.data;
    if (!hubspot.data) {
      await AdmissionRequestMongo.deleteOne({ _id: requestId });
      logger.error(
        `[createAdmissionRequest] Hubsport error occured while registering new customer with id ${requestId}`
      );
      response.status(500).json({ message: 'Error al enviar solicitud' });
    }

    if (country === Country.mx) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      const sendWhatsApp = await sendHilosMessage({
        phone: typeof phone === 'string' ? phone : phone.toString(),
        requestId: requestId!,
        firstname: firstName,
        country,
      });
      serialized.data.hilos = sendWhatsApp?.data;

      if (email) {
        await sendEmailToMXCustomerAfterRegistering({
          customerEmail: email,
          customerName: `${firstName}`,
          customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
        });
      }

      await AdmissionRequestMongo.updateOne(
        { _id: requestId },
        { $set: { hubspot: hubspot.data, hilos: sendWhatsApp.data } }
      );
    }

    if (country === Country.us) {
      logger.info(`[createAdmissionRequest] Sending Registration email to US customer: ${email}`);
      await sendEmailToUSCustomerAfterRegistering({
        customerEmail: email,
        customerName: `${firstName}`,
        customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
      });
    }

    if (country === Country.br) {
      logger.info(`[createAdmissionRequest] Sending Registration email to BR customer: ${email}`);
      // await sendEmailToUSCustomerAfterRegistering({
      //   customerEmail: email,
      //   customerName: `${firstName}`,
      //   customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
      // });
    }

    response.status(200).json(serialized);
  } catch (error: any) {
    console.error({ error });
    logger.error(
      `[createAdmissionRequest] Error occured while registering new customer with id ${requestId}:`,
      {
        message: error.message,
        stack: error.stack,
      }
    );
    // console.error({ error });

    const statusCode = (error as any).response?.status || 500;

    if (statusCode === 409) {
      // const url = new URL(HUBSPOT_URL);
      const url = `https://api.hubapi.com/crm/v3/objects/contacts/${email}?idProperty=email`;
      const { data: contact } = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${HUBSPOT_TOKEN}`,
        },
      });

      if (contact?.id) {
        // update contact
        const updateUrl = `https://api.hubapi.com/crm/v3/objects/contacts/${contact.id}`;

        const properties: any = {
          firstname: firstName,
          lastname: lastName,
          email,
          phone: phoneNumber,
          city,
          request_id: `${REQUEST_URL}/${requestId}`,
          state,
          postal_code: postalCode,
          country: country.toUpperCase(),
          source,
          fuente,
        }

        const cityWithSelector = cityOptionsMap[city] || cityOptions2Map[city];

        if (cityWithSelector) {
          properties.ciudad__con_selector_ = cityWithSelector;
        }

        await axios.patch(
          updateUrl,
          {
            properties,
          },
          {
            headers: {
              Authorization: `Bearer ${HUBSPOT_TOKEN}`,
            },
          }
        );

        if (country === Country.mx) {
          // eslint-disable-next-line @typescript-eslint/no-use-before-define
          const sendWhatsApp = await sendHilosMessage({
            phone: typeof phone === 'string' ? phone : phone.toString(),
            requestId: requestId!,
            firstname: firstName,
            country,
          });

          if (email) {
            await sendEmailToMXCustomerAfterRegistering({
              customerEmail: email,
              customerName: `${firstName}`,
              customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
            });
          }

          await AdmissionRequestMongo.updateOne(
            { _id: requestId },
            { $set: { hubspot: contact, hilos: sendWhatsApp.data } }
          );
          serialized.data.hilos = sendWhatsApp?.data;
        }

        if (country === Country.us) {
          logger.info(`[createAdmissionRequest] Sending Registration email to US customer: ${email}`);
          await sendEmailToUSCustomerAfterRegistering({
            customerEmail: email,
            customerName: `${firstName}`,
            customerWebAppLink: `${DRIVER_WEB_APP_URL}/?id=${requestId}`,
          });
        }

        response.status(200).json(serialized);
      }
    } else {
      response.status(500).json({ message: 'Error al enviar solicitud' });
    }
  }
};

export const createAdmissionRequestResourceByCsv = async (req: Request, response: Response) => {
  const csv: Express.Multer.File | undefined = req.file;
  const user = req?.authUser;
  const { source, clientIpAddress } = req.body;

  if (!csv) {
    response.status(400).json({ message: 'CSV file is required' });
    return;
  }

  try {
    const csvContent = csv.buffer.toString('utf-8');
    const csvData = csvContent
      .split('\n')
      .slice(1)
      .map(
        (
          line: string
        ): {
          firstName: string;
          lastName: string;
          phone: string;
          email: string;
            country: string;
          city: string;
            state: string;
        } => {
          const [
            firstName,
            lastName,
            phone,
            email,
            country,
            city,
            state,
            // sourceLine,
            // postalCode,
          ] = line.trim().split(',');

          return {
            firstName,
            lastName,
            phone,
            email,
            country,
            city,
            state,
            // source: sourceLine,
            // postalCode,
          };
        }
      )
      .filter(Boolean); // Filtra las líneas vacías
    const requests: any[] = [];

    const batchSize = 10; // Ajusta el tamaño del lote según la limitación de HubSpot
    for (let i = 0; i < csvData.length; i += batchSize) {
      const batch = csvData.slice(i, i + batchSize);

      for (const row of batch) {
        try {
          const personalData = new RequestPersonalData({
            firstName: row.firstName,
            lastName: row.lastName,
            phone: row.phone,
            email: row.email,
            country: row.country.toLowerCase() as Country,
            // vehicleSelected: row.vehicleSelected,
            city: row.city,
            state: row.state,
            // postalCode: row.postalCode,
          });

          const countryCode = row.country.toLowerCase() === 'mx' ? '+52' : '+1';
          let phoneNumber = row.phone.toString();
          if (!phoneNumber.startsWith('+')) {
            phoneNumber = `${countryCode}${phoneNumber}`;
          }
          personalData.phone = phoneNumber;
          const admissionRequest = await createAdmissionRequest(
            personalData,
            user,
            clientIpAddress,
            undefined,
            source
          );
          const requestId = admissionRequest.id;

          try {
            const fuenteFound = DriverSourceType[source as keyof typeof DriverSourceType];

            const properties: any = {
              firstname: row.firstName,
              lastname: row.lastName,
              email: row.email,
              phone: phoneNumber,
              city: row.city,
              request_id: `${REQUEST_URL}/${requestId}`,
              ciudad__con_selector_: row.city.replace(/\s/g, ''),
            };
            if (fuenteFound) {
              properties.fuente = fuenteFound.toUpperCase();
            }
            const hubspot = await axios.post(
              HUBSPOT_URL,
              {
                properties,
              },
              {
                headers: {
                  Authorization: `Bearer ${HUBSPOT_TOKEN}`,
                },
              }
            );

            if (row.country.toLowerCase() === Country.mx) {
              await AdmissionRequestMongo.updateOne({ _id: requestId }, { $set: { hubspot: hubspot.data } });
            }

            requests.push(admissionRequest);
          } catch (error: any) {
            const statusCode = error.response?.status || 500;
            if (statusCode === 409) {
              console.log('YA CREADO');
              // const url = new URL(HUBSPOT_URL);
              const url = `https://api.hubapi.com/crm/v3/objects/contacts/${row.email}?idProperty=email`;
              const { data: contact } = await axios.get(url, {
                headers: {
                  Authorization: `Bearer ${HUBSPOT_TOKEN}`,
                },
              });

              if (contact?.id) {
                // update contact
                const updateUrl = `https://api.hubapi.com/crm/v3/objects/contacts/${contact.id}`;
                await axios.patch(
                  updateUrl,
                  {
                    properties: {
                      firstname: row.firstName,
                      lastname: row.lastName,
                      email: row.email,
                      phone: phoneNumber,
                      city: row.city,
                      request_id: `${REQUEST_URL}/${requestId}`,
                      state: row.state,
                      // postal_code: row.postalCode,
                      country: row.country.toUpperCase(),
                    },
                  },
                  {
                    headers: {
                      Authorization: `Bearer ${HUBSPOT_TOKEN}`,
                    },
                  }
                );
                if (row.country.toLowerCase() === Country.mx) {
                  // eslint-disable-next-line @typescript-eslint/no-use-before-define

                  await AdmissionRequestMongo.updateOne({ _id: requestId }, { $set: { hubspot: contact } });
                }

                response.status(200).json();
              }
            }
            console.error({ error });
            requests.push({ error: 'HubSpot Error', row });
          }
        } catch (error) {
          console.error({ error });
          requests.push({ error: 'Admission Request Error', row });
        }
      } // Fin del bucle for...of

      await sleep(3000); // Espera 1 segundo entre lotes
    }

    response.status(200).json(requests);
  } catch (error) {
    // console.log('errors', error);
    console.error({ error });
    response.status(500).json({ message: 'Error al enviar solicitud' });
  }
};

async function sendHilosMessage({
  phone,
  requestId,
  firstname = 'Driver',
}: {
  phone: string;
  requestId: string;
  firstname: string;
  country: string;
}) {
  const headers = {
    Authorization: `Token ${HILOS_API_KEY}`,
    'Content-Type': 'application/json',
  };

  // mexico template
  try {
    const res = await axios.post(
      `${HILOS_URL_SEND_TEMPLATE}/${HILOS_TEMPLATE_ID}/send`,
      {
        variables: [`https://hilos.io/api/file/p/7f908be9-dd9f-4a70-a3d1-685499104854`, firstname, requestId],
        phone,
      },
      { headers }
    );
    return res;
  } catch (error: any) {
    console.error({ error });
    logger.error(
      `[sendHilosMessage] Error occured while sending message to Hilos: ${phone}, ${requestId}, ${firstname}, ${error}`
    );
    return error;
  }
}

export const getAdmissionRequestResource = async (req: Request, response: Response) => {
  validateRequestParams(getPublicAdmissionRequestValidation, req.params);
  const { requestId } = req.params;

  const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const getPublicAdmissionRequestResource = async (req: Request, response: Response) => {
  validateRequestParams(getPublicAdmissionRequestValidation, req.params);
  const { requestId } = req.params;

  const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId);
  const serialized = publicAdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const palencaWebhookResource = async (req: Request, response: Response) => {
  logger.info(`[palencaWebhookResource] ${JSON.stringify(req.body)}`);
  const payload = palencaWebhookPayloadValidation.parse(req.body);

  // If no data, then ignore it
  if (!payload.data) {
    response.status(200).json({});
    return;
  }

  // If external_id, account_id, country, platform are not present, then ignore it
  if (
    !payload.data.external_id ||
    !payload.data.account_id ||
    !payload.data.country ||
    !payload.data.platform
  ) {
    response.status(200).json({});
    return;
  }

  const palencaData = new PalencaWebhook({
    webhookAction: payload.data.webhook_action as PalencaWebhookAction,
    userId: payload.data.user_id,
    accountId: payload.data.account_id,
    externalId: payload.data.external_id,
    country: payload.data.country as Country,
    platform: payload.data.platform as GigPlatform,
  });

  await evaluatePalencaWebhook(palencaData);

  response.status(200).json({});
};


export const updateRequestPersonalDataResource = async (req: Request, response: Response) => {
  const payload = updatePersonalDataValidation.parse(req.body);
  const requestId = req.params.requestId;
  const authUser = req?.authUser;

  const personalData = new RequestPersonalData({
    firstName: payload.firstName,
    lastName: payload.lastName,
    phone: payload.phone,
    email: payload.email,
    birthdate: payload.birthdate,
    taxId: payload.taxId,
    nationalId: payload.nationalId,
    postalCode: payload.postalCode,
    city: payload.city,
    state: payload.state,
    neighborhood: payload.neighborhood,
    street: payload.street,
    streetNumber: payload.streetNumber,
    department: payload.department,
    ssn: payload.ssn,
    rideShareTotalRides: payload.rideShareTotalRides,
    avgEarningPerWeek: payload.avgEarningPerWeek,
    termsAndConditions: payload.termsAndConditions,
    dataPrivacyConsentForm: payload.dataPrivacyConsentForm,
    ficoScore: payload.ficoScore,
    criminalBackgroundCheck: payload.criminalBackgroundCheck,
    motorVehicleRecordCheck: payload.motorVehicleRecordCheck,
    vehicleSelected: payload.vehicleSelected!,
    privacyPolicy: payload.privacyPolicy,
    ocnBackgroundAndCreditCheckForApplication: payload.ocnBackgroundAndCreditCheckForApplication,
  });
  const data = await updateRequestPersonalData(requestId, personalData, authUser);
  const serialized = AdmissionRequestSerializer(data);

  if (payload.ssn) {
    logger.info(`[updateRequestPersonalDataResource] Driver ${requestId} completed registration step 3`);
  } else {
    logger.info(`[updateRequestPersonalDataResource] Driver ${requestId} completed registration step 1`);
  }
  response.status(200).json(serialized);
};

export const updateRequestHomeVisitResource = async (req: Request, response: Response) => {
  logger.info({
    message: `[updateRequestHomeVisitResource] Updating home visit data for requestId = ${req.params.requestId}`,
  });
  const payload = updateHomeVisitDataValidation.parse(req.body);
  const requestId = req.params.requestId;

  const authUser = req?.authUser;

  const { isPersonalData, isHomeVisitData, personalData: personalDataPayload } = payload;

  let serialized: AdmissionRequest;
  if (isPersonalData) {
    const personalData = new RequestPersonalData(personalDataPayload!);
    const admissionRequest = await updateRequestPersonalData(requestId, personalData, authUser);
    serialized = admissionRequest;
  }

  if (isHomeVisitData) {
    const homeVisit = new HomeVisit(payload.homeVisitData!);
    const homeVisitData = await updateHomeVisit(requestId, homeVisit, authUser);
    serialized = homeVisitData;
  }

  const admissionRequestSerialized = AdmissionRequestSerializer(serialized!);
  response.status(200).json(admissionRequestSerialized);
};

export const mediaUploadResource = async (req: Request, response: Response) => {
  const { mediaType } = mediaUploadValidation.parse(req.body);

  const files = Object.values(req.files as any).flat(1);

  const uploadedFiles = await uploadFiles(files, mediaType);
  const serialized = documentSerializer(uploadedFiles);
  response.status(200).json(serialized);
};

export const updateRequestDocumentsResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const { country: customerCountry } = req.query;
  const authUser = req?.authUser;

  logger.info(
    `[updateRequestDocumentsResource] updating document resources of requestId ${requestId} and country is ${customerCountry}`
  );

  let payload;
  if (customerCountry === Country.us) {
    payload = requestDocumentsValidationUS.parse(req.body);
  } else {
    payload = requestDocumentsValidation.parse(req.body);
  }

  const toUpdate = payload.updates.map((update: any) => {
    return new RequestDocument({
      mediaId: update.mediaId,
      type: update.type,
      status: RequestDocumentStatus.pending_review,
    });
  });

  const updated = await updateRequestDocuments(requestId, toUpdate, authUser);
  const serialized = AdmissionRequestSerializer(updated);
  const admissionRequest = await AdmissionRequestMongo.findById(requestId);
  if (!admissionRequest?.firstBlockVerifier) {
    console.log('No se ha verificado el primer bloque');
    const hubspotBatch = await HubspotBatch.findOne({ requestId });
    if (hubspotBatch?.dealId !== undefined) {
      console.log('Se ha encontrado el dealId');
      await fileVerification({
        dealId: hubspotBatch.dealId,
        documents: admissionRequest?.documentsAnalysis.documents || [],
        requestId,
      });
    }
  }
  response.status(200).json(serialized);
};

export const addPalencaAccount = async (req: Request, response: Response) => {
  const payload = addPalencaAccountValidation.parse(req.body);
  const { requestId } = req.params;

  const updated = await addPalencaAccountToAdmissionRequest(requestId, payload.platform, payload.accountId);

  const serialized = AdmissionRequestSerializer(updated);

  response.status(200).json(serialized);
};

export const getPlatformEarningsResource = async (req: Request, response: Response) => {
  const { requestId, platform } = getPlatformEarningsValidation.parse(req.params);

  const weeklyEarnings = await retrieveMostRecent12WeeksEarnings(requestId, platform);

  const serialized = requestWeeklyEarningsSerializer(weeklyEarnings);

  response.status(200).json(serialized);
};

export const searchAdmissionRequestsResource = async (req: Request, response: Response) => {
  const { country, q, options, status } = searchAdmissionRequestsValidation.parse(req.body);

  const paginationOptions = new PaginationSearchOptions({
    page: options.page || 1,
    itemsPerPage: options.itemsPerPage || DEFAULT_PAGINATION_LIMIT,
  });

  const [pagination, admissionRequests] = await searchPaginatedAdmissionRequests(
    country,
    q,
    paginationOptions,
    status
  );

  const serialized = paginatedSerializer(admissionRequests, pagination);

  response.status(200).json(serialized);
};

export const findAdmissionRequestByAssociateId = async (req: Request, response: Response) => {

  const { associateId } = req.body;

  const admissionRequest = await AdmissionRequestMongo.findOne({
    $or: [{ associateId }],
  })
    .select({ _id: 1, personalData: 1, documentsAnalysis: 1, status: 1, typeOfPreapproval: 1 })
    .sort({ createdAt: -1 });


  if (!admissionRequest) {
    response.status(404).json({ message: 'No se encontró la solicitud' });
    return;
  }

  response.status(200).json(admissionRequest);
};

export const getRequestDocumentsAnalysisResource = async (req: Request, response: Response) => {
  const { requestId, documentClassification } = req.params;

  const analysis = await getRequestDocumentsAnalysis(
    requestId,
    documentClassification as DocumentClassification
  );

  const serialized = requestDocumentsAnalysisSerializer(analysis);

  response.status(200).json(serialized);
};

export const approveRequestDocumentResource = async (req: Request, response: Response) => {
  const { requestId, documentType } = req.params;
  const authUser = req.authUser;
  await approveRequestDocument(requestId, documentType as AdmissionRequestDocumentType, authUser);

  response.status(204).json();
};

export const rejectRequestDocumentResource = async (req: Request, response: Response) => {
  const { requestId, documentType } = req.params;
  const authUser = req.authUser;
  await rejectRequestDocument(requestId, documentType as AdmissionRequestDocumentType, authUser);

  response.status(204).json();
};

export const executeDocumentsAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  const { documentClassification } = req.body;
  logger.info(
    `[executeDocumentsAnalysisResource] updating document analysis resource for requestId ${requestId}`
  );
  const admissionRequest = await executeDocumentsAnalysis(requestId, authUser, documentClassification);
  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
}

export const approveDocumentAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  logger.info(
    `[approveDocumentAnalysisResource] updating document analysis resource for requestId ${requestId}`
  );
  const admissionRequest = await approveRequestDocumentAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  const hubspotBtach = await HubspotBatch.findOne({
    requestId: admissionRequest.id,
  });
  if (hubspotBtach?.dealId !== undefined) {
    await dealUpdate({
      dealId: hubspotBtach.dealId,
      properties: {
        dealstage: 'qualifiedtobuy',
      },
    });
  }

  response.status(200).json(serialized);
};

export const executeSocialAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  logger.info(
    `[executeSocialAnalysisResource] updating social analysis resource for requestId ${requestId}`
  );
  const admissionRequest = await executeSocialAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const approveSocialAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  logger.info(
    `[approveSocialAnalysisResource] updating social analysis resource for requestId ${requestId}`
  );
  const admissionRequest = await approveRequestSocialAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const rejectSocialAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  logger.info(
    `[rejectSocialAnalysisResource] updating social analysis resource for requestId ${requestId}`
  );
  const admissionRequest = await rejectRequestSocialAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const getHomeVisitResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;

  const homeVisit = await getHomeVisit(requestId);
  const serialized = requestHomeVisitSerializer(homeVisit);

  response.status(200).json(serialized);
};

export const getPlatformMetricsResource = async (req: Request, response: Response) => {
  const { requestId, platform } = getPlatformMetricsValidation.parse(req.params);

  const metric = await repoRetrievePalencaPlatformMetric(requestId, platform);

  const serialized = requestMetricSerializer(metric);

  response.status(200).json(serialized);
};

export const getEventsResource = async (req: Request, response: Response) => {
  const { entityType, entityId } = getEventsValidation.parse(req.params);

  const events = await retrieveEvents(entityId, entityType);

  const serialized = eventsSerializer(events);

  response.status(200).json(serialized);
};

export const approveRiskAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  const admissionRequest = await approveRiskAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  const hubspotBtach = await HubspotBatch.findOne({
    requestId: admissionRequest.id,
  });
  if (hubspotBtach?.dealId !== undefined) {
    await dealUpdate({
      dealId: hubspotBtach.dealId,
      properties: {
        dealstage: 'presentationscheduled',
      },
    });
  }

  response.status(200).json(serialized);
};

export const rejectRiskAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  const admissionRequest = await rejectRiskAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const rejectDocumentsAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  const admissionRequest = await rejectRequestDocumentAnalysis(requestId, authUser);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const retryAnalysisResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;

  const { modelName } = req.body;

  const admissionRequest = await retryAnalysis(requestId, modelName);

  const serialized = AdmissionRequestSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const upadtePlatformMetricResource = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  const payload = updatePlatformMetricValidation.parse(req.body);
  const platformMetric = new PlatformMetric({
    acceptanceRate: payload.acceptanceRate,
    cancellationRate: payload.cancellationRate,
    rating: payload.rating,
    lifetimeTrips: payload.lifetimeTrips,
    timeSinceFirstTrip: payload.timeSinceFirstTrip,
  });

  const admissionRequest = await updatePlatformMetric(requestId, platformMetric, authUser);
  const serialized = PlatformMetricSerializer(admissionRequest);

  response.status(200).json(serialized);
};

export const getAdmissionRequestScreenshots = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  logger.info(`[getAdmissionRequestScreenshots] Fetch Addmission request screenshots: ${requestId}`);
  const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId);
  let result: Object[] = [];
  if (admissionRequest.screenshots) {
    result = await Promise.all(
      (admissionRequest.screenshots as Array<any>).map(async (element) => {
        const { platform, url, name } = element;
        const path = await repoGetMediaSignedUrl(url);
        return {
          name,
          platform,
          path,
        };
      })
    );
  }
  response.status(200).json(result);
};

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const addAvalDataResource = async (req: Request, response: Response) => {
  const payload = addAvalDataValidation.parse(req.body);
  const requestId = req.params.requestId;
  const user = req?.authUser;

  const avalData = new RequestAvalData({
    name: payload.name,
    phone: payload.phone,
    email: payload.email,
    location: payload.location,
  });

  const updated = await addAvalDataToAdmissionRequest(requestId, avalData, user);
  const serialized = AdmissionRequestSerializer(updated);

  response.status(200).json(serialized);
};

export const addAvalDataResourceWithDocs = async (req: Request, response: Response) => {
  const payload = {
    name: req?.body?.name,
    phone: req?.body?.phone,
    email: req?.body?.email,
    location: req?.body?.location,
  };
  const user = req?.authUser;

  const validatedPayload = addAvalDataValidation.parse(payload);
  const requestId = req.params.requestId;

  const avalData = new RequestAvalData(validatedPayload);

  const files = Object.values(req.files as any).flat(1);

  if (files?.length > 0){
    const groupedFiles: Record<string, any[]> = {};
    files.forEach((file: any) => {
      if (!groupedFiles[file?.fieldname]) {
        groupedFiles[file?.fieldname] = [];
      }
      groupedFiles[file?.fieldname]?.push(file);
    });


    const toUpdatePromises = Object.entries(groupedFiles)?.map(async ([fieldname, fileArray]) => {
      const uploaded = await uploadFiles(fileArray);
      return uploaded.map((file) => new RequestDocument({
        mediaId: file?.id,
        type: fieldname as AdmissionRequestAdditionalDocumentType,
        status: RequestDocumentStatus.approved,
      }));
    });

    const toUpdate = (await Promise.all(toUpdatePromises))?.flat();
  
    await updateRequestDocuments(requestId, toUpdate, user);
  }

  await addAvalDataToAdmissionRequest(requestId, avalData, user);
  const documentsAnalysis = await getRequestDocumentsAnalysis(
    requestId,
    DocumentClassification.additional
  );

  const data = {
    success: true,
    data: {
      avalData: avalData,
      documentsAnalysis: documentsAnalysis,
    },
  }
  response.status(200).json(data);
};

export const latLongLookup = async (req: Request, response: Response) => {
  const payload = latLongLookupValidation.parse(req.body);
  const requestId = req.params.requestId;
  const location: string | null = await googleReverseGeocoding(payload.latitude, payload.longitude);
  if (location) {
    const locationData = new RequestLocationData({
      latitude: payload.latitude,
      longitude: payload.longitude,
      location: location,
      ipAddress: payload.ipAddress,
      isFromIP: false,
    });
    const updated = await addLocationDataToAdmissionRequest(requestId, locationData);
    const serialized = AdmissionRequestSerializer(updated);
    response.status(200).json(serialized);
  } else {
    response.status(400).json({ status: 400 });
  }
};

export const ipLookup = async (req: Request, response: Response) => {
  const payload = ipLookupValidation.parse(req.body);
  const requestId = req.params.requestId;
  const ipInfoData = await lookupIPAddress(payload.ipAddress);
  if (ipInfoData) {
    const [latitude, longitude] = ipInfoData.loc!.split(",");
    const locationData = new RequestLocationData({
      latitude: Number(latitude),
      longitude: Number(longitude),
      location: ipInfoData.region,
      ipAddress: payload.ipAddress,
      isFromIP: true,
    });
    const updated = await addLocationDataToAdmissionRequest(requestId, locationData);
    const serialized = AdmissionRequestSerializer(updated);
    response.status(200).json(serialized);
  } else {
    response.status(400).json({ status: 400 });
  }
};

export const sendLocationGatheringMessage = async (req: Request, response: Response) => {
  const requestId = req.params.requestId;
  try {
    await sendHilosLocationGatheringMessage(requestId)
    const serialized = AdmissionRequestSerializer({} as any);
    response.status(200).json(serialized);
  } catch(error) {
    response.status(400).json({ status: 400 })
  }
}

export const sendHomeImageUploadMessage = async (req: Request, response: Response) => {
  const requestId = req.params.requestId;
  try {
    await sendHomeImageUploadNotificationMessage(requestId)
    const serialized = AdmissionRequestSerializer({} as any);
    response.status(200).json(serialized);
  } catch(error) {
    response.status(400).json({ status: 400 })
  }
}

export const resetHomeVisitScheduleLinkSendDate = async (req: Request, response: Response) => {
  const { requestId } = req.params;
  const authUser = req.authUser;
  await resetHomeVisitScheduleLinkSendDateToToday(requestId, authUser);
  const serialized = AdmissionRequestSerializer({} as AdmissionRequest);
  response.status(200).json(serialized);
};

export const fetchVehiceViolation = async (req: Request, response: Response) => {
  const plate = req.params.plate;
  const results  = await vehicleViolationService.fetchViolationByPlate(plate);
  response.status(200).json({ message: 'Violation check initiated successfully', data: results });
};

export const fetchVehiceAmountViolation = async (req: Request, response: Response) => {
  const plate = req.params.plate;
  const results  = await vehicleViolationService.fetchVehiceAmountViolation(plate);
  response.status(200).json({ message: 'Violation check initiated successfully', data: results });
};
