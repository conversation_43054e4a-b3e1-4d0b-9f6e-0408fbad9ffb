export enum AdmissionRequestStatus {
  created = 'created', // The request is been created from the admin panel and we are waiting for the user to connect their gig platforms
  earnings_analysis = 'earnings_analysis', // The user has connected his gig platforms and we can start the earnings analysis
  documents_analysis = 'documents_analysis', // The user has uploaded all the required documents and we perform  the analysis
  // judicial_analysis = 'judicial_analysis',
  risk_analysis = 'risk_analysis', // We have all the risk variables and we can perform the risk analysis
  home_visit = 'home_visit', // We can perform the home visit
  social_analysis = 'social_analysis', // We can perform the social analysis and set the result
  approved = 'approved', // The request has been approved
  rejected = 'rejected', // The request has been rejected
}

export enum AdmissionRequestRejectionReason {
  earnings_analysis = 'Rejected after earnings analysis',
  documents_analysis = 'Rejected after documents analysis',
  judicial_analysis = 'Rejected after judicial analysis',
  risk_analysis = 'Rejected after risk analysis',
  home_visit = 'Rejected after home visit',
  social_analysis = 'Rejected after social analysis',
  final_evaluation = 'Rejected after final evaluation',
}

export enum PalencaWebhookAction {
  'user.created' = 'user.created',
  'profile.updated' = 'profile.updated',
  'earnings.updated' = 'earnings.updated',
  'login.created' = 'login.created',
  'login.success' = 'login.success',
  'login.error' = 'login.error',
  'login.incomplete' = 'login.incomplete',
}

export enum GigPlatform {
  uber = 'uber',
  didi = 'didi',
  lyft = 'lyft',
  didi_food = 'didi_food',
  cornershop = 'cornershop',
  indriver = 'indriver',
  uber_eats = 'uber_eats',
  rappi = 'rappi',
  other = 'other',
}

export enum Country {
  mx = 'mx',
  us = 'us',
  br = 'br',
}

export enum USCities {
  // Miami = 'Miami',
  Dallas = 'Dallas',
}

export enum CurrencyCode {
  mxn = 'mxn',
  usd = 'usd',
}

export const countryCodeMap = {
  mx: '+52',
  us: '+1',
  br: '+55',
} as const;

export enum MediaType {
  proof_of_tax_situation = 'proof_of_tax_situation',
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement = 'bank_statement',
  home_visit_evidence = 'home_visit_evidence',
  selfie_photo = 'selfie_photo',
  garage_photo = 'garage_photo',
  solidarity_obligor_identity_card_front = 'solidarity_obligor_identity_card_front',
  solidarity_obligor_identity_card_back = 'solidarity_obligor_identity_card_back',
  curp = 'curp',
}

export enum MediaStatus {
  pending = 'pending', // A document that has been uploaded but not yet processed
  active = 'active', // A document that is currently in use and valid
  deleted = 'deleted', // A document that has been marked for deletion
  archived = 'archived', // A document that is no longer in active use but retained for historical purposes
}

export enum RequestDocumentStatus {
  pending = 'pending',
  approved = 'approved',
  pending_review = 'pending_review',
  rejected = 'rejected',
}

export enum RequestDocumentsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum DocumentAnalysisStatus {
  pending = 'pending',
  valid = 'valid',
  invalid = 'invalid',
  error = 'error',
}

export enum RequestPersonalDataStepStatus {
  pending = 'pending', // User required details are not submitted
  completed = 'completed', // User required details are submitted but not verified
  approved = 'approved', // User required details are submitted and verified
}

export enum HomeVisitStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum ResidentOwnershipStatus {
  owned = 'owned',
  rented = 'rented',
}

export enum PalencaRetrievalStatus {
  pending = 'pending',
  queued = 'queued',
  success = 'success',
  error = 'error',
}

export enum PalencaAccountStatus {
  success = 'success',
  incomplete = 'incomplete',
  pending = 'pending',
  retry = 'retry',
  error = 'error',
}

export enum EarningsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
  approved_with_conditions = 'approved_with_conditions',
}

export enum AdmissionRequestDocumentType {
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  proof_of_address = 'proof_of_address',
  bank_statement_month_1 = 'bank_statement_month_1',
  bank_statement_month_2 = 'bank_statement_month_2',
  bank_statement_month_3 = 'bank_statement_month_3',
}

export enum AdmissionRequestAdditionalDocumentType {
  proof_of_tax_situation = 'proof_of_tax_situation',
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  selfie_photo = 'selfie_photo',
  garage_photo = 'garage_photo',
  solidarity_obligor_identity_card_front = 'solidarity_obligor_identity_card_front',
  solidarity_obligor_identity_card_back = 'solidarity_obligor_identity_card_back',
  curp = 'curp',
}

export enum AdmissionRequestDocumentTypeUS {
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement_month_1 = 'bank_statement_month_1',
  bank_statement_month_2 = 'bank_statement_month_2',
  bank_statement_month_3 = 'bank_statement_month_3',
  bank_statement_month_4 = 'bank_statement_month_4',
  bank_statement_month_5 = 'bank_statement_month_5',
  bank_statement_month_6 = 'bank_statement_month_6',
  // proof_of_completion_of_safety_courses = 'proof_of_completion_of_safety_courses',
  // avg_weekly_income_of_last_twelve_weeks = 'avg_weekly_income_of_last_twelve_weeks',
  // ride_share_ride_history = 'ride_share_ride_history',
  // ride_share_dates = 'ride_share_dates',
  // driving_record = 'driving_record',
  // signature = 'signature',
}

// Identifies what kind of entity the action is performed on (e.g., Document, User Profile)
export enum EntityType {
  admission_request = 'admission_request',
}

//  Describes the specific action taken in the log entry.
export enum ActionType {
  'admission_request.created' = 'admission_request.created',
  'home_visit.created' = 'home_visit.created',
  'home_visit.approved' = 'home_visit.approved',
  'home_visit.rejected' = 'home_visit.rejected',
  'home_visit.pending' = 'home_visit.pending',
  'home_visit.updated' = 'home_visit.updated',
  'admission_request.approved' = 'admission_request.approved',
  'admission_request.rejected' = 'admission_request.rejected',
  'documents_analysis.completed' = 'documents_analysis.completed',
  'documents_analysis.approved' = 'documents_analysis.approved',
  'documents_analysis.rejected' = 'documents_analysis.rejected',
  'risk_analysis.approved' = 'risk_analysis.approved',
  'risk_analysis.rejected' = 'risk_analysis.rejected',
  'social_analysis.executed' = 'social_analysis.executed',
  'social_analysis.approved' = 'social_analysis.approved',
  'social_analysis.rejected' = 'social_analysis.rejected',
  'document.uploaded' = 'document.uploaded',
  'document.approved' = 'document.approved',
  'document.rejected' = 'document.rejected',
  'aval_data.added' = 'aval_data.added',
  'aval_data.updated' = 'aval_data.updated',
  'personal_data.updated' = 'personal_data.updated',
  'driver_earning.updated' = 'driver_earning.updated',
  'driver_earning.added' = 'driver_earning.added',
  'driver_metric.updated' = 'driver_metric.updated',
  'driver_metric.added' = 'driver_metric.added',
  'admission_request.homeVisitScheduleLinkSendDate.reset' = 'admission_request.homeVisitScheduleLinkSendDate.reset',
}

export enum MLModels {
  RIDESHARE_PERFORMANCE = 'rideshare_performance',
  FINANCIAL_ASSESSMENT = 'financial_assessment',
  PERSONAL_INFORMATION = 'personal_information',
  HOMEVISIT_INFORMATION = 'homevisit_information',
  // DRIVING_AND_LEGAL_HISTORY = 'driving_and_legal_history',
}

export enum AnalysisStatus {
  pending = 'pending',
  completed = 'completed',
  error = 'error',
}

export enum RiskCategory {
  low = 'low',
  medium = 'medium',
  high = 'high',
}

export enum ScorecardVariableName {
  age = 'age',
  gig_platforms = 'gig_platforms',
  life_time_completed_trips = 'life_time_completed_trips',
  days_since_first_trip = 'days_since_first_trip',
  percentage_acceptance_rate = 'percentage_acceptance_rate',
  percentage_cancellation_rate = 'percentage_cancellation_rate',
  average_rating = 'average_rating',
  earnings_last_12_weeks = 'earnings_last_12_weeks',
  vehicle_condition = 'vehicle_condition',
  vehicle_type = 'vehicle_type',
  // risk_city = 'risk_city', We needs more definition on how to handle them
}

export enum ScorecardVersion {
  v1 = 'v1',
  v2 = 'v2',
}

export enum RiskCityList {
  'high_risk_list' = 'high_risk_list',
  'medium_risk_list' = 'medium_risk_list',
  'low_risk_list' = 'low_risk_list',
}

export enum VehicleCondition {
  new = 'new',
  used = 'used',
}

export enum VehicleType {
  car = 'car',
  motorcycle = 'motorcycle',
}

/**
 * *** DEPRECATED ***
 */
export enum ScorecardVariableCategoryType {
  numeric = 'numeric',
  categorical = 'categorical',
}

export enum DriverSourceType {
  aB2c73N = 'uber',
  vRx81pB = 'didi',
  lr81Bcw = 'lyft',
  a1Eu821 = 'competitor',
  vcEo43P = 'facebook',
  bT4q81M = 'didi qualified',
  nY5g17N = 'didi ev',
  tK1h54I = 'didi in app',
  '4dwVR60' = 'didi mailing',
  qQBQq8M = 'didi push notification',
  amsOqrq = 'flyer Campaign',
  jH3aw0g = 'referrals',
  vvAPlIX = 'dealership referrals',
  RqfGQ2m = 'vm referrals',
  Ft9NYGF = 'mn referrals',
  qXxI31U = 'mariel',
  NmWKE1Q = 'besta referrals',
  bv4SltD = 'xpanel ev didi',
  x72LBzC = 'whatsApp ev didi',
  a5tpzmI = 'network effect',
  '6ASimga' = 'in App uber ev',
  qLE2xf2 = 'email uber ev',
  pKqlRlW = 'webpage uber ev',
  Eulf9Rv = 'uberMatch uber ev',
  xJHnkd8 = 'in App uber ICE',
  Z87o1BH = 'email uber ICE',
  '7u69EsX' = 'webpage uber ICE',
  jeQ63Dj = 'uberMatch uber ICE',
  XuYzOtE = 'extra-12',
  VByXDt3 = 'extra-13',
  qsTb1t1 = 'extra-14',
  A6P0bNK = 'extra-15',
  suPEBYr = 'extra-16',
  xjKKyZ7 = 'extra-17',
  JrFLJqT = 'Mercadolibre',
  Hrf1V35 = 'Source 01',
  X7L9Q2Z = 'Source 02',
  RM8V5KT = 'Source 03',
  T1Z6YQD = 'Source 04',
  '9GEPK2M' = 'Source 05',
  W3N7BLA = 'Source 06',
  KD5QT8X = 'Source 07',
  '2YJZRVM' = 'Source 08',
  F9LTC3E = 'Source 09',
  MPX74NQ = 'Source 10',
  BQ6DYUZ = 'Source 11',
  X8G7L2M = 'Source 12',
  B9V3Q0N = 'Source 13',
  Z1K6P4R = 'Source 14',
  H7A9D3T = 'Source 15',
  M2J8E5W = 'Source 16',
  C4N7Y6Q = 'Source 17',
  R0F2L8Z = 'Source 18',
  U3K5X9M = 'Source 19',
  T6V1B4A = 'Source 20',
  Y9D0G2H = 'Source 21',
  L5E3W8P = 'Source 22',
  J8N4Z1C = 'Source 23',
  K2X7M6D = 'Source 24',
  A0Q9T5L = 'Source 25',
  F1B3V7Y = 'Source 26',
  P6H2J9X = 'Source 27',
  W7C5K3G = 'Source 28',
  N4M8A0R = 'Source 29',
  S3Z1L6T = 'Source 30',
  E9V2Q7U = 'Source 31',
}

export enum MaritalStatus {
  Single = 'Single',
  Married = 'Married',
  Widowed = 'Widowed',
  Divorced = 'Divorced',
}

export enum DocumentClassification {
  mandatory = 'mandatory_documents',
  additional = 'additional_documents',
  all = 'all_documents',
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  confidence: number;
}

export enum DocumentSpanishName {
  proof_of_tax_situation = 'Constancia de situación fiscal',
  identity_card_front = 'Identificación (frente)',
  identity_card_back = 'Identificación (reverso)',
  drivers_license_front = 'Licencia de conducir (frente)',
  drivers_license_back = 'Licencia de conducir (reverso)',
  proof_of_address = 'Comprobante de domicilio',
  bank_statement_month_1 = 'Estado de cuenta bancario (1)',
  bank_statement_month_2 = 'Estado de cuenta bancario (2)',
  bank_statement_month_3 = 'Estado de cuenta bancario (3)',
  bank_statement_month_4 = 'Estado de cuenta bancario (4)',
  bank_statement_month_5 = 'Estado de cuenta bancario (5)',
  bank_statement_month_6 = 'Estado de cuenta bancario (6)',
  selfie_photo = 'Foto Selfie',
  garage_photo = 'Foto Garage',
  solidarity_obligor_identity_card_front = 'INE del Obligado Solidario (Frontal)',
  solidarity_obligor_identity_card_back = 'INE del Obligado Solidario (Reverso)',
  curp = 'CURP',
}

export enum RequestGenerationSource {
  mobileApp = 'mobile_app',
  onboardingSupportPlatform = 'onboarding_support_platform',
}

export enum RequestIdType {
  associate = 'associate',
  admissionRequest = 'admission_request',
}
