import { ZodError } from 'zod';
import { ErrorCode } from './enums';
interface AppExceptionProps {
  message: string;
  code: string;
  errors?: any;
}

export class AppException extends Error implements AppExceptionProps {
  message: string;

  code: string;

  // We are using Zod Issues for simplicity,
  // since we are using Zod to validate the requests
  errors?: ZodError['issues'];

  constructor({ message, code, errors }: AppExceptionProps) {
    super(message);
    this.message = message;
    this.code = code;
    this.errors = errors;
  }
}

// Exception that represents a business exception, becuase some usecases decided
// to raise this exception.
// Serialized as HTTP 400 Error
export class BusinessException extends AppException {
  constructor({ message, code }: { message: string; code: string }) {
    super({
      message: message || 'Business Exception',
      code: code || 'business_exception',
    });
  }
}

// Exception that represent the lack of valid authentication credentials
// Serialized as HTTP 401 Error

export class UnauthorizedException extends AppException {
  constructor() {
    super({ message: 'Unauthorized', code: 'unauthorized', errors: {} });
  }
}

// Exception that represent the API cannot complete the requested action,
// or the request action is semantically incorrect or fails business validation.
// Serialized as HTTP 422 Unprocessable Entity Error
export class UnprocessableEntityException extends AppException {
  constructor(errors: ZodError) {
    const issues = errors.issues;
    super({
      message: 'Unprocessable Entity',
      code: 'unprocessable_entity',
      errors: issues,
    });
  }
}

// Exception that represent server understands the request but refuses to authorize it
// Serialized as HTTP 403 Error
export class ForbiddenException extends AppException {
  constructor() {
    super({ message: 'Forbidden', code: 'forbidden' });
  }
}

// Exception that represent that an entity does not exist or client isn't properly authenticated
// Serialized as HTTP 404 Error
export class NotFoundException extends AppException {
  constructor({ message, code }: { message: string; code: string }) {
    super({ message, code });
  }
}

// Exception that represents that the service is unavailable
// Serialized as HTTP 503 Error
export class ServiceUnavailableException extends AppException {
  constructor() {
    super({ message: 'Service Unavailable', code: 'service_unavailable' });
  }
}

// Exception that represent internal server error
// Serialized as HTTP 500 Error
export class InternalServerErrorException extends AppException {
  constructor({ message, code }: { message?: string; code?: string } = {}) {
    super({
      message: message || 'Internal Server Error',
      code: code || 'internal_server_error',
    });
  }
}

// All custom exceptions should be defined here

export class AdmissionRequestNotFoundException extends NotFoundException {
  constructor() {
    super({
      message: 'Admission Request not found',
      code: ErrorCode.admission_request_not_found,
    });
  }
}

export class PlatformMetricNotFoundException extends NotFoundException {
  constructor() {
    super({
      message: 'Platform Metric not found',
      code: ErrorCode.platform_request_not_found,
    });
  }
}

export class CouldNotRetrievePalencaEarningsException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not retrieve Palenca earnings',
      code: ErrorCode.could_not_retrieve_palenca_earnings,
    });
  }
}

export class CouldNotRetrievePalencaMetrics extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not retrieve Palenca metrics',
      code: ErrorCode.could_not_retrieve_palenca_metrics,
    });
  }
}

export class MissingMediaFileException extends BusinessException {
  constructor() {
    super({
      message: 'Missing media file',
      code: ErrorCode.missing_media_file,
    });
  }
}

export class CouldNotUploadMediaException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not upload media',
      code: ErrorCode.could_not_upload_media,
    });
  }
}

export class CouldNotUpdateRequestDocumentException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not update request document',
      code: ErrorCode.could_not_update_request_document,
    });
  }
}

export class CouldNotSignUrlException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not sign url',
      code: ErrorCode.could_not_sign_url,
    });
  }
}

export class ModelExecutionFailedException extends InternalServerErrorException {
  constructor(message: string) {
    super({
      message: message,
      code: ErrorCode.model_execution_failed,
    });
  }
}

export class RequestDocumentNotFoundException extends NotFoundException {
  constructor() {
    super({
      message: 'Request Document not found',
      code: ErrorCode.request_document_not_found,
    });
  }
}

export class DocumentAnalysisFailedException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'DocumentAnalysisFailedException';
  }
}

export class NotAllDocumentsApprovedException extends BusinessException {
  constructor() {
    super({
      message: 'Not all documents are approved',
      code: ErrorCode.not_all_documents_approved,
    });
  }
}

export class RequestNotInDocumentsAnalysisStatusException extends BusinessException {
  constructor() {
    super({
      message: 'Request is not in documents analysis status',
      code: ErrorCode.request_not_in_documents_analysis_status,
    });
  }
}

export class CouldNotFoundPlatformMetricsException extends NotFoundException {
  constructor() {
    super({
      message: 'Could not found platform metrics',
      code: ErrorCode.could_not_found_platform_metrics,
    });
  }
}

export class RequestNotInRiskAnalysisStatusException extends BusinessException {
  constructor() {
    super({
      message: 'Request is not in risk analysis status',
      code: ErrorCode.request_not_in_risk_analysis_status,
    });
  }
}

export class RiskAnalysisDataNotFoundException extends NotFoundException {
  constructor() {
    super({
      message: 'Risk analysis data not found',
      code: ErrorCode.risk_analysis_data_not_found,
    });
  }
}

/**
 * *** DEPRECATED ***
 */
export class CouldNotRetrieveScorecardConfigException extends InternalServerErrorException {
  constructor() {
    super({
      message: 'Could not retrieve scorecard config',
      code: ErrorCode.could_not_retrieve_scorecard_config,
    });
  }
}

export class RiskAnalysisNotCompletedException extends BusinessException {
  constructor() {
    super({
      message: 'Risk analysis not completed',
      code: ErrorCode.risk_analysis_not_completed,
    });
  }
}

export class RequestNotInSocialAnalysisStatusException extends BusinessException {
  constructor() {
    super({
      message: 'Request is not in social analysis status',
      code: ErrorCode.request_not_in_social_analysis_status,
    });
  }
}

export class AlreadyExistException extends BusinessException {
  constructor() {
    super({
      message: 'User already exist with the provided CURP',
      code: ErrorCode.already_exist,
    });
  }
}

export class AllRequiredDocumentsAreNotApproved extends BusinessException {
  constructor() {
    super({
      message: 'All required documents are not approved',
      code: ErrorCode.required_documents_not_approved,
    });
  }
}

export class SlotNotFoundException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Slot not found';
    super({
      message: errorMessage,
      code: ErrorCode.slot_not_found,
      errors: errorMessage,
    });
  }
}

export class AppointmentNotFoundException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Appointment not found';
    super({
      message: errorMessage,
      code: ErrorCode.appointment_not_found,
      errors: errorMessage,
    });
  }
}

export class AppointmentAlreadyExistException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Appointment already exist';
    super({
      message: errorMessage,
      code: ErrorCode.appointment_already_exists,
      errors: errorMessage,
    });
  }
}

export class SlotIsNotAvailableException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Slot is not available';
    super({
      message: errorMessage,
      code: ErrorCode.slot_is_not_available,
      errors: errorMessage,
    });
  }
}

export class AppointmentMaximumReScheduleException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Appointment maximum rescheduling limit reached';
    super({
      message: errorMessage,
      code: ErrorCode.appointment_max_reschedule_limit_exceeded,
      errors: errorMessage,
    });
  }
}

export class SlotBookingTimeLimitException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Slot booking time limit reached';
    super({
      message: errorMessage,
      code: ErrorCode.slot_booking_time_limit,
      errors: errorMessage,
    });
  }
}

export class ScheduleNotFoundException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Schedule not found';
    super({
      message: errorMessage,
      code: ErrorCode.schedule_not_found,
      errors: errorMessage,
    });
  }
}

export class MeetingLinkCreationException extends AppException {
  constructor({ message }: { message?: string }) {
    const errorMessage = message || 'Meeting link creation failed, please try again or contact support';
    super({
      message: errorMessage,
      code: ErrorCode.meeting_link_creation_failed,
      errors: errorMessage,
    });
  }
}
