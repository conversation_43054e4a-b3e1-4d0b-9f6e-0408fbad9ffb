import { Router } from 'express';
import { validatePlatesAndReturnNeighborHoodsScheduleConfig } from './general-controllers';
import { verifyTokenVendorPlatform } from './middlewares/verifycation-token';

const generalRoutes = Router();

// Test endpoint
generalRoutes.get('/test', verifyTokenVendorPlatform, (req, res) => {
  res.status(200).json({
    message: 'Vendor platform test endpoint working',
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
    user: req.userVendor
      ? {
          id: req.userVendor.userId,
          organizationId: req.userVendor.organizationId,
        }
      : null,
  });
});

//  Validate plates, get vehicle data and associate data
// then check the state of associate and find all neighborhoods that
// belong to the same city state of the associate
generalRoutes.post('/validate-plates/neighborhoods', validatePlatesAndReturnNeighborHoodsScheduleConfig);

export default generalRoutes;
