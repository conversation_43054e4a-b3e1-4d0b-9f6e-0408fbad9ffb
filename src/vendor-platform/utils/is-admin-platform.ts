import { Request, Response, NextFunction } from 'express';

export function isAdminPlatformRequest(headers: Request['headers'] = {}): boolean {
  const adpt = headers.adpt === 'true'; // Adpt means: Admin Platform
  return adpt;
}

export function isAdminPlatform(req: Request, res: Response, next: NextFunction): void {
  const isAdmin = isAdminPlatformRequest(req.headers);

  if (!isAdmin) {
    res.status(403).json({
      success: false,
      message: 'Acceso denegado. Se requiere plataforma de administrador.',
    });
    return;
  }

  next();
}
