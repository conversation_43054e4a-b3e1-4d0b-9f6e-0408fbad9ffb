/* eslint-disable prettier/prettier */
import { DateTime } from 'luxon';
import { InstallationAppointment, InstallationStatus } from '../models/installation-appointment.model';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { WeeklySchedule } from '@/models/Schedules';
import { NeighborhoodVendorModel } from '../../neighborhoods/models/neighborhood.model';
import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import Document from '@/models/documentSchema';
import { BucketNameEnum, uploadFileAndReturnUrl } from '@/aws/s3';
import { removeEmptySpacesNameFile } from '@/services/removeEmptySpaces';
import axios from 'axios';
import { HILOS_API_KEY, HILOS_URL_SEND_TEMPLATE } from '@/constants';
import { InstallationScheduleEvents } from '../utils/schedule-events';
import { isDev } from '@/constants';
import { CompanyPermissionsService } from './company-permissions.service';
import { Types } from 'mongoose';

interface ReminderTiming {
  nightBefore: { days: number; hour: number; minute: number };
  oneHourBefore: { hours: number };
}

const REMINDER_TIMING: Record<'production' | 'development', ReminderTiming> = {
  production: {
    nightBefore: { days: 1, hour: 21, minute: 0 }, // 9 PM night before
    oneHourBefore: { hours: 1 },
  },
  development: {
    nightBefore: { days: 0, hour: 0, minute: 2 }, // 2 minutes from now
    oneHourBefore: { hours: 0 }, // 5 minutes from now
  },
};

const TEMPLATES = {
  INSTALLATION_REQUEST: '067f7f3c-e559-73d9-8000-ffbac2d86537',
  // APPOINTMENT_CONFIRMATION: '067f7f5c-d387-7327-8000-5baab390b49a',
  // ONE_NIGHT_REMINDER: '067f80da-c16a-7e83-8000-721b1595855c',
  // ONE_HOUR_REMINDER: '067f7f7c-5489-7699-8000-a4cf4e8b34ad',
  APPOINTMENT_CONFIRMATION: '067f95a3-12d5-7ca3-8000-616cb5e1fd4d', // new one with plates variable at the end
  ONE_NIGHT_REMINDER: '067f95b1-8bb3-7b4c-8000-751615eab490', // new one with plates variable at the end
  ONE_HOUR_REMINDER: '067f95ac-79bd-754a-8000-52c4aff7fa58', // new one with plates variable at the end
  INSTALLATION_DRIVER_CONFIRMATION_REQUEST: '067f956e-73d1-789d-8000-79e28dfa382d',
};

export class InstallationScheduleService {
  static async getAvailableSlots({
    neighborhoodId,
    date,
  }: {
    neighborhoodId: string;
    date: string;
  }) {
    const neighborhood = await NeighborhoodVendorModel.findOne({
      _id: neighborhoodId,
    });

    if (!neighborhood) {
      throw HttpException.NotFound('Neighborhood not found');
    }

    const config = neighborhood.scheduleConfig;
    const targetDate = DateTime.fromISO(date, { zone: config.timezone ?? 'America/Mexico_City' });

    // Verificar si la fecha solicitada es el día actual
    const now = DateTime.now().setZone(config.timezone ?? 'America/Mexico_City');
    if (targetDate.hasSame(now, 'day')) {
      return []; // Retornar array vacío si es el día actual
    }

    const dayKey = this.getDayKey(targetDate);
    const daySchedule = config.weeklySchedule[dayKey];
    if (!daySchedule || !daySchedule.start || !daySchedule.end) {
      return []; // No schedule for this day
    }

    const slots: DateTime[] = [];
    let currentTime = targetDate.set({
      hour: parseInt(daySchedule.start.split(':')[0]),
      minute: parseInt(daySchedule.start.split(':')[1]),
    });

    const endTime = targetDate.set({
      hour: parseInt(daySchedule.end.split(':')[0]),
      minute: parseInt(daySchedule.end.split(':')[1]),
    });

    while (currentTime < endTime) {
      const isAvailable = await this.isSlotAvailable({
        neighborhoodId,
        startTime: currentTime,
        duration: config.installationDuration,
        maxSimultaneousInstallations: config.maxSimultaneousInstallations,
      });

      if (isAvailable) {
        slots.push(currentTime);
      }

      currentTime = currentTime.plus({ minutes: config.installationDuration });
    }

    return slots;
  }

  private static formatAddress(address: any): string {
    const parts = [
      address.street,
      address.exteriorNumber && `#${address.exteriorNumber}`,
      address.interiorNumber && `Int. ${address.interiorNumber}`,
      address.colony,
      address.zipCode && `C.P. ${address.zipCode}`,
    ].filter(Boolean);
    return parts.join(', ');
  }

  private static formatTimeWithZone(dateTime: DateTime): string {
    const timeFormat = dateTime.toFormat('hh:mm a');
    const zoneName = dateTime.zoneName || '';

    const zoneDisplayNames: Record<string, string> = {
      'America/Mexico_City': 'Hora México',
      'America/Tijuana': 'Hora Tijuana',
      'America/Monterrey': 'Hora Monterrey',
      // Agregar más zonas según sea necesario
    };

    const displayZone = zoneDisplayNames[zoneName] || zoneName;
    return displayZone ? `${timeFormat} ${displayZone}` : timeFormat;
  }

  static async createAppointment({
    neighborhoodId,
    startTime,
    associateId,
    stockId,
    address,
  }: {
    neighborhoodId: string;
    startTime: string;
    associateId: string;
    stockId: string;
    address: any;
  }) {
    const neighborhood = await NeighborhoodVendorModel.findById(neighborhoodId);

    if (!neighborhood) {
      throw HttpException.NotFound('Neighborhood not found');
    }

    const startDateTime = DateTime.fromISO(startTime, {
      zone: neighborhood.scheduleConfig.timezone,
    }).setLocale('es');

    const isAvailable = await this.isSlotAvailable({
      neighborhoodId,
      startTime: startDateTime,
      duration: neighborhood.scheduleConfig.installationDuration,
      maxSimultaneousInstallations: neighborhood.scheduleConfig.maxSimultaneousInstallations,
    });
    if (!isAvailable) {
      throw HttpException.BadRequest('Selected time slot is no longer available');
    }

    const appointment = new InstallationAppointment({
      companyId: neighborhood.companyId,
      cityId: neighborhood.cityId,
      crewId: neighborhood.crewId,
      neighborhoodId,
      startTime: startDateTime.toJSDate(),
      endTime: startDateTime.plus({ minutes: neighborhood.scheduleConfig.installationDuration }).toJSDate(),
      associateId,
      stockId,
      address,
      notifications: {
        oneNightBefore: { scheduled: false, sent: false },
        oneHourBefore: { scheduled: false, sent: false },
      },
    });

    await appointment.save();

    // Enviar confirmación de cita
    const associate = await Associate.findById(associateId).select('firstName phone');
    const stockVehicle = await StockVehicle.findById(stockId).select('carPlates');

    if (associate) {
      const formattedAddress = this.formatAddress(address);
      await axios.post(
        `${HILOS_URL_SEND_TEMPLATE}/${TEMPLATES.APPOINTMENT_CONFIRMATION}/send`,
        {
          variables: [
            associate.firstName,
            formattedAddress,
            startDateTime.toFormat("dd 'de' LLLL 'de' yyyy"),
            // startDateTime.toFormat('hh:mm a'),
            this.formatTimeWithZone(startDateTime),
            stockVehicle?.carPlates?.plates || 'N/A',
          ],
          phone: associate.phone.toString().replace('+52', ''),
        },
        {
          headers: {
            Authorization: `Token ${HILOS_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // Programar recordatorios
    await this.scheduleReminders(appointment, neighborhood.scheduleConfig.timezone);

    return appointment;
  }

  private static async isSlotAvailable({
    neighborhoodId,
    startTime,
    duration,
    maxSimultaneousInstallations,
  }: {
    neighborhoodId: string;
    startTime: DateTime;
    duration: number;
    maxSimultaneousInstallations: number;
  }) {
    const endTime = startTime.plus({ minutes: duration });

    const overlappingAppointments = await InstallationAppointment.countDocuments({
      neighborhoodId,
      startTime: { $lt: endTime.toJSDate() },
      endTime: { $gt: startTime.toJSDate() },
      status: { $nin: [InstallationStatus.canceled] },
    });

    return overlappingAppointments < maxSimultaneousInstallations;
  }

  private static getDayKey(dateTime: DateTime): keyof WeeklySchedule {
    const day = dateTime.weekday;
    const days: Record<number, keyof WeeklySchedule> = {
      1: 'monday',
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday',
      7: 'sunday',
    };
    return days[day] || 'monday';
  }

  static async getInstallationAppointments({
    startDate,
    endDate,
    cityId,
    crewId,
    neighborhoodId,
    userId,
  }: {
    startDate?: string;
    endDate?: string;
    cityId?: string;
    crewId?: string;
    neighborhoodId?: string;
    userId: string;
  }) {
    const query: any = { status: { $ne: 'cancelled' } };

    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) {
        // check if time is not provided, if not, setting 00:00:00 to the start date
        const splitted = startDate.split('T');
        if (splitted.length === 1) {
          startDate = startDate + 'T00:00:00';
        }

        query.startTime.$gte = new Date(startDate);
      }
      if (endDate) {
        // check if time is not provided, if not, setting 23:59:59 to the end date
        const splitted = endDate.split('T');
        if (splitted.length === 1) {
          endDate = endDate + 'T23:59:59';
        }

        query.startTime.$lte = new Date(endDate);
      };
    }

    if (cityId) {
      query.cityId = cityId;
    }

    if (crewId) {
      query.crewId = crewId;
    }

    if (neighborhoodId) {
      query.neighborhoodId = neighborhoodId;
    }

    const isAdminOrOwner = await CompanyPermissionsService.validateOwnerAndAdminAccess({
      userId,
      companyId: query.companyId,
    });

    if (!isAdminOrOwner.validation) { // If it's not admin or owner, filter by allowed cities and crews
      // query.crewId = { $in: isAdminOrOwner.permissions.allowedCrews };
      query.cityId = { $in: isAdminOrOwner.permissions.allowedCities };
      const totalCrews = isAdminOrOwner.permissions.allowedCrews.length;
      if (totalCrews > 0) { // If there is not allowed crews, don't filter by crew, we assume that the user has access to all crews
        query.crewId = { $in: isAdminOrOwner.permissions.allowedCrews };
      }
    }


    const appointments = await InstallationAppointment.find(query)
      .sort({ startTime: 1 })
      .populate('city', 'name state')
      .populate('crew', 'name')
      .populate('neighborhood', 'name')
      .select('+companyProof.imagesUrls +driverProof.imagesUrls')
      .exec();

    // get associate and stock data on every appointment
    const enrichedAppointments = await this.enrichAppointmentsWithAllData(appointments);
    return enrichedAppointments;
  }

  static async enrichAppointmentsWithAllData(appointments: any[]) {
    const enrichedAppointments: any[] = [];

    await Promise.allSettled(
      appointments.map(async (appointment) => {
        const associate = await Associate.findById(appointment.associateId)
          .select('firstName lastName email phone')
          .lean();
        const stockVehicle = await StockVehicle.findById(appointment.stockId)
          .select('carPlates.plates brand model carNumber extensionCarNumber vin')
          .lean();
        enrichedAppointments.push({
          ...appointment.toObject(),
          associate,
          stockVehicle,
        });
      })
    );
    return enrichedAppointments;
  }

  static async updateAppointmentWithProofImages({
    appointmentId,
    files,
    source,
    comments,
  }: {
    appointmentId: string;
    files: Express.Multer.File[];
    source: 'company' | 'driver';
    comments?: string;
  }) {
    const appointment = await InstallationAppointment.findById(appointmentId);

    if (!appointment) {
      throw new Error('Appointment not found');
    }
    // Validar el estado actual
    if (source === 'company' && appointment.status !== InstallationStatus.scheduled) {
      throw new Error('Appointment must be in scheduled status for company uploads');
    }

    if (source === 'driver' && appointment.status !== InstallationStatus.installed) {
      throw new Error('Appointment must be in installed status for driver uploads');
    }

    const documentIds = [];
    const imagesUrls = [];

    // Procesar cada archivo
    for (const file of files) {
      const removeSpacesFileName = removeEmptySpacesNameFile(file);

      // Definir la ruta en S3
      const s3Path = `installations/${appointment.stockId}/${appointment.associateId}/proof-${source}`;

      // Subir archivo a S3 como público
      const result = await uploadFileAndReturnUrl(file, {
        route: s3Path,
        isPublic: true,
        bucketName: BucketNameEnum.VENDOR_PLATFORM,
      });

      // const documentPath = `${s3Path}${removeSpacesFileName}`;

      // Crear documento en MongoDB
      const document = new Document({
        originalName: removeSpacesFileName,
        // path: documentPath,
        path: result.Key,
        vehicleId: appointment.stockId,
        associateId: appointment.associateId,
        mimeType: file.mimetype,
        status: 'active',
        type: 'installation_proof',
      });

      await document.save();
      documentIds.push(document._id);

      // Guardar la URL pública
      imagesUrls.push(result.url);
    }

    // Preparar actualización
    const update: any = {};
    const now = new Date();

    if (source === 'company') {
      update['companyProof.images'] = documentIds;
      update['companyProof.imagesUrls'] = imagesUrls;
      update['companyProof.uploadedAt'] = now;
      update['companyProof.comments'] = comments;
      update.status = InstallationStatus.installed

      // Enviar notificación al conductor
      const associate = await Associate.findById(appointment.associateId).select('firstName phone');
      const stockVehicle = await StockVehicle.findById(appointment.stockId).select('carPlates.plates');
      if (associate && stockVehicle) {
        try {
          const result = await axios.post(
            `${HILOS_URL_SEND_TEMPLATE}/${TEMPLATES.INSTALLATION_DRIVER_CONFIRMATION_REQUEST}/send`,
            {
              variables: [
                associate.firstName,
                stockVehicle.carPlates?.plates || 'N/A',
              ],
              phone: associate.phone.toString().replace('+52', '').replace('52', ''),
            },
            {
              headers: {
                Authorization: `Token ${HILOS_API_KEY}`,
                'Content-Type': 'application/json',
              },
            }
          );
          console.log('Sent message', result.data);
        } catch (error: any) {
          console.log('error', error?.response?.data);
        }
      }
    } else {
      update['driverProof.images'] = documentIds;
      update['driverProof.imagesUrls'] = imagesUrls;
      update['driverProof.uploadedAt'] = now;
      update['driverProof.comments'] = comments;
      update.status = 'completed';
    }

    // Actualizar appointment
    const updatedAppointment = await InstallationAppointment.findByIdAndUpdate(
      appointmentId,
      { $set: update },
      { new: true }
    );
    return updatedAppointment;
  }

  // static async rescheduleAppointment(appointmentId: string, newStartTime: string) {
  static async rescheduleAppointment({
    appointmentId,
    newStartTime,
    neighborhoodId,
    vendorUserId,
    adminUserId,
  }: {
    appointmentId: string;
    newStartTime: string;
    neighborhoodId?: string;
    vendorUserId?: string;
    adminUserId?: string;
  }) {

    const appointment = await InstallationAppointment.findById(appointmentId)
      .select('+notifications')
    if (!appointment) {
      throw new Error('Appointment not found');
    }

    const appointmentStartTime = DateTime.fromJSDate(appointment.startTime);
    const rescheduleLimit = appointmentStartTime.minus({ hours: 24 });
    const now = DateTime.now();
    // Only allow rescheduling if the appointment is more than 24 hours away
    // if a vendor or admin is rescheduling, allow it, no matter the time
    if (now > rescheduleLimit && (!adminUserId || !vendorUserId)) {
      // throw HttpException.BadRequest('La cita no puede ser reagendada menos de 24 horas antes de la hora de inicio');
      throw HttpException.BadRequest('La cita no puede ser reagendada menos de 24 horas antes de la hora de inicio');
    }

    const neighborhood = await NeighborhoodVendorModel.findById(appointment.neighborhoodId);
    if (!neighborhood) {
      throw new Error('Neighborhood not found');
    }

    const startDateTime = DateTime.fromISO(newStartTime, {
      zone: neighborhood.scheduleConfig.timezone,
    });
    const isAvailable = await this.isSlotAvailable({
      neighborhoodId: neighborhood._id.toString(),
      startTime: startDateTime,
      duration: neighborhood.scheduleConfig.installationDuration,
      maxSimultaneousInstallations: neighborhood.scheduleConfig.maxSimultaneousInstallations,
    });

    if (!isAvailable) {
      throw new Error('Selected time slot is no longer available');
    }

    // Eliminar recordatorios existentes si existen
    try {
      await this.deleteReminders(appointment);
    } catch (error) {
      console.error('Error deleting reminders:', error);
    }

    // Guardar el historial de reagendamiento
    appointment.reschedulingHistory.push({
      previousStartTime: appointment.startTime,
      newStartTime: startDateTime.toJSDate(),
      rescheduledAt: new Date(),
      previousNeighborhoodId: appointment.neighborhoodId,
      rescheduledByVendor: vendorUserId ? new Types.ObjectId(vendorUserId) : undefined,
      rescheduledByAdmin: adminUserId,
    });

    // Actualizar la cita
    appointment.startTime = startDateTime.toJSDate();
    if (neighborhoodId) {
      appointment.neighborhoodId = new Types.ObjectId(neighborhoodId);
    }
    appointment.endTime = startDateTime
      .plus({ minutes: neighborhood.scheduleConfig.installationDuration })
      .toJSDate();

    // Programar nuevos recordatorios
    await this.scheduleReminders(appointment, neighborhood.scheduleConfig.timezone);
    appointment.status = InstallationStatus.rescheduled;
    await appointment.save();
    const response = appointment.toObject();
    response.rescheduleCount = appointment.reschedulingHistory.length;

    return response;
  }

  private static async scheduleReminders(appointment: any, timezone: string) {
    const timing = REMINDER_TIMING[isDev ? 'development' : 'production'];
    const startDateTime = DateTime.fromJSDate(appointment.startTime).setZone(timezone);

    try {
      // Night before reminder
      const nightBeforeDate = isDev
        ? DateTime.now().plus({ minutes: timing.nightBefore.minute })
        : startDateTime.minus({ days: timing.nightBefore.days }).set({
          hour: timing.nightBefore.hour,
          minute: timing.nightBefore.minute,
        });
      // console.log('Is future night before date?', nightBeforeDate > DateTime.now(), nightBeforeDate);
      if (nightBeforeDate > DateTime.now()) { // Solo programar si la fecha es futura
        const result = await InstallationScheduleEvents.createScheduleEvent({
          appointmentId: appointment._id.toString(),
          scheduledTime: nightBeforeDate,
          timezone,
          reminderType: 'night-before',
          endpoint: `/public/installation-appointments/${appointment._id}/send-one-night-reminder`,
        });

        appointment.notifications.oneNightBefore = {
          eventId: result.ScheduleArn,
          scheduled: true,
          sent: false,
        };
      }

      // One hour before reminder
      const oneHourBeforeDate = isDev
        ? DateTime.now().plus({ minutes: 5 }) // 5 minutos desde ahora en desarrollo
        : startDateTime.minus({ hours: timing.oneHourBefore.hours });
      // console.log('Is future one hour before date?', oneHourBeforeDate > DateTime.now(), oneHourBeforeDate);
      // console.log('-------------------------------------------------')
      if (oneHourBeforeDate > DateTime.now()) { // Solo programar si la fecha es futura
        const result = await InstallationScheduleEvents.createScheduleEvent({
          appointmentId: appointment._id.toString(),
          scheduledTime: oneHourBeforeDate,
          timezone,
          reminderType: 'one-hour-before',
          endpoint: `/public/installation-appointments/${appointment._id}/send-one-hour-reminder`,
        });

        appointment.notifications.oneHourBefore = {
          eventId: result.ScheduleArn,
          scheduled: true,
          sent: false,
        };
      }
    } catch (error) {
      console.error('Error scheduling reminders:', error);
      throw error;
    }
  }

  private static async deleteReminders(appointment: any) {
    const deletePromises = [];

    if (appointment.notifications?.oneNightBefore?.eventId) {
      deletePromises.push(
        InstallationScheduleEvents.deleteScheduleEvent({
          eventId: appointment.notifications.oneNightBefore.eventId,
          // appointmentId: appointment._id.toString(),
          reminderType: 'night-before',
        })
      );
    }

    if (appointment.notifications?.oneHourBefore?.eventId) {
      deletePromises.push(
        InstallationScheduleEvents.deleteScheduleEvent({
          eventId: appointment.notifications.oneHourBefore.eventId,
          // appointmentId: appointment._id.toString(),
          reminderType: 'one-hour-before',
        })
      );
    }

    await Promise.all(deletePromises);
  }

  static async sendOneHourReminder(appointmentId: string) {
    const appointment = await InstallationAppointment.findById(appointmentId);
    if (!appointment) {
      throw new Error('Appointment not found');
    }

    const associate = await Associate.findById(appointment.associateId).select('firstName phone');
    const stockVehicle = await StockVehicle.findById(appointment.stockId).select('carPlates.plates');
    if (!associate) {
      throw new Error('Associate not found');
    }

    const formattedAddress = this.formatAddress(appointment.address);

    try {
      const result = await axios.post(
        `${HILOS_URL_SEND_TEMPLATE}/${TEMPLATES.ONE_HOUR_REMINDER}/send`,
        {
          variables: [
            associate.firstName,
            formattedAddress,
            stockVehicle?.carPlates?.plates || 'N/A',
          ],
          phone: associate.phone.toString().replace('+52', ''),
        },
        {
          headers: {
            Authorization: `Token ${HILOS_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('One hour reminder sent successfully', result.data);
    } catch (error: any) {
      console.error('Error sending one hour reminder:', error.response?.data, error.message);
      throw error;
    }

    appointment.notifications.oneHourBefore.sent = true;
    await appointment.save();
  }

  static async sendOneNightReminder(appointmentId: string) {
    const appointment = await InstallationAppointment.findById(appointmentId);
    if (!appointment) {
      throw new Error('Appointment not found');
    }

    const associate = await Associate.findById(appointment.associateId).select('firstName phone');
    const stockVehicle = await StockVehicle.findById(appointment.stockId).select('carPlates.plates');
    if (!associate) {
      throw new Error('Associate not found');
    }

    const startDateTime = DateTime.fromJSDate(appointment.startTime).setLocale('es');
    const formattedAddress = this.formatAddress(appointment.address);

    try {
      const result = await axios.post(
        `${HILOS_URL_SEND_TEMPLATE}/${TEMPLATES.ONE_NIGHT_REMINDER}/send`,
        {
          variables: [
            associate.firstName,
            formattedAddress,
            startDateTime.toFormat("dd 'de' LLLL 'de' yyyy"),
            // startDateTime.toFormat('hh:mm a'),
            this.formatTimeWithZone(startDateTime),
            stockVehicle?.carPlates?.plates || 'N/A',
          ],
          phone: associate.phone.toString().replace('+52', '').replace('52', ''),
        },
        {
          headers: {
            Authorization: `Token ${HILOS_API_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('Result of sending one night reminder:', result.data);
    } catch (error: any) {
      console.error('Error sending one night reminder:', error.response?.data, error.message);
      throw error;
    }

    appointment.notifications.oneNightBefore.sent = true;
    await appointment.save();
  }

  static async updateArrivalTime(appointmentId: string) {
    const appointment = await InstallationAppointment.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('Appointment not found');
    }

    // if (appointment.status !== InstallationStatus.scheduled) {
    //   throw HttpException.BadRequest('Appointment must be in scheduled status to update arrival time');
    // }

    const arrivedAtDate = new Date();

    appointment.arrivedAt = arrivedAtDate;
    await appointment.save();

    return appointment;
  }

  static async filterInstallationAppointments({
    startDate,
    endDate,
    status,
    cityId,
    crewId,
    neighborhoodId,
    companyId,
    // userId,
  }: {
    startDate?: string;
    endDate?: string;
    status?: string;
    cityId?: string;
    crewId?: string;
    neighborhoodId?: string;
    companyId?: string;
    userId: string;
  }) {
    const query: any = {};

    // Add filters to query
    if (startDate || endDate) {
      query.startTime = {};
      if (startDate) query.startTime.$gte = new Date(startDate);
      if (endDate) query.startTime.$lte = new Date(endDate);
    }

    if (status) {
      // Handle multiple statuses separated by comma
      const statuses = status.split(',');
      console.log('Status: ', status, 'Statuses: ', statuses);
      query.status = statuses.length > 1 ? { $in: statuses } : status;
    }

    if (cityId) query.cityId = cityId;
    if (crewId) query.crewId = crewId;
    if (neighborhoodId) query.neighborhoodId = neighborhoodId;
    if (companyId) query.companyId = companyId;

    // Get appointments with populated data
    const appointments = await InstallationAppointment.find(query)
      .select('-notifications -reschedulingHistory')
      .sort({ startTime: 1 })
      .populate('city', 'name state')
      .populate('crew', 'name')
      .populate('neighborhood', 'name')
      .select('+companyProof.imagesUrls +driverProof.imagesUrls')
      .exec();

    // Enrich appointments with associate and stock data
    const enrichedAppointments: any[] = [];

    await Promise.allSettled(
      appointments.map(async (appointment) => {
        const associate = await Associate.findById(appointment.associateId)
          .select('firstName lastName email phone')
          .lean();
        const stockVehicle = await StockVehicle.findById(appointment.stockId)
          .select('carPlates.plates brand model carNumber extensionCarNumber vin')
          .lean();
        if (!stockVehicle) {
          return;
        }
        enrichedAppointments.push({
          ...appointment.toObject(),
          associate,
          stockVehicle: {
            ...stockVehicle,
            contractNumber: stockVehicle.extensionCarNumber
              ? `${stockVehicle.carNumber}-${stockVehicle.extensionCarNumber}`
              : stockVehicle.carNumber,
          },
        });
      })
    );

    return enrichedAppointments;
  }

  static async updateNotAttendedReason({
    appointmentId,
    notAttendedReason,
  }: {
    appointmentId: string;
    notAttendedReason: string;
  }) {
    const appointment = await InstallationAppointment.findById(appointmentId);

    if (!appointment) {
      throw HttpException.NotFound('Installation appointment not found');
    }

    if (appointment.status === InstallationStatus.completed) {
      throw HttpException.BadRequest('La cita ya fue marcada como completada, no se puede actualizar la razón de no asistencia');
    }

    appointment.notAttendedReason = notAttendedReason;
    appointment.status = InstallationStatus['not-attended'];
    await appointment.save();

    return appointment;
  }

  /**
   * Busca citas de instalación con paginación y filtros
   * @param options - Opciones de búsqueda y paginación
   * @returns - Citas de instalación paginadas
   */
  static async searchInstallationAppointments({
    page = 1,
    limit = 10,
    query,
    startDate,
    endDate,
    status,
    cityId,
    crewId,
    neighborhoodId,
    companyId,
    userId,
  }: {
    page?: number;
    limit?: number;
    query?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
    cityId?: string;
    crewId?: string;
    neighborhoodId?: string;
    companyId?: string;
    userId: string;
  }) {
    // Construir el objeto de consulta
    const searchQuery: any = {};

    // Agregar filtros básicos
    if (startDate || endDate) {
      searchQuery.startTime = {};
      if (startDate) {
        // Verificar si se proporciona la hora, si no, establecer 00:00:00
        const splitted = startDate.split('T');
        if (splitted.length === 1) {
          startDate = startDate + 'T00:00:00';
        }
        searchQuery.startTime.$gte = new Date(startDate);
      }
      if (endDate) {
        // Verificar si se proporciona la hora, si no, establecer 23:59:59
        const splitted = endDate.split('T');
        if (splitted.length === 1) {
          endDate = endDate + 'T23:59:59';
        }
        searchQuery.startTime.$lte = new Date(endDate);
      }
    }

    if (status) {
      // Manejar múltiples estados separados por coma
      const statuses = status.split(',');
      searchQuery.status = statuses.length > 1 ? { $in: statuses } : status;
    }

    if (cityId) searchQuery.cityId = cityId;
    if (crewId) searchQuery.crewId = crewId;
    if (neighborhoodId) searchQuery.neighborhoodId = neighborhoodId;
    if (companyId) searchQuery.companyId = companyId;

    // Verificar permisos del usuario
    const isAdminOrOwner = await CompanyPermissionsService.validateOwnerAndAdminAccess({
      userId,
      companyId: searchQuery.companyId,
    });

    if (!isAdminOrOwner.validation) {
      // Si no es admin o propietario, filtrar por ciudades y equipos permitidos
      searchQuery.cityId = { $in: isAdminOrOwner.permissions.allowedCities };
      const totalCrews = isAdminOrOwner.permissions.allowedCrews.length;
      if (totalCrews > 0) {
        searchQuery.crewId = { $in: isAdminOrOwner.permissions.allowedCrews };
      }
    }

    // Agregar filtro de búsqueda por placas o carNumber
    if (query && query.trim() !== '') {
      // Primero, buscar vehículos que coincidan con la consulta
      const stockVehicles = await StockVehicle.find({
        $or: [
          { 'carPlates.plates': new RegExp(query.trim(), 'i') },
          { carNumber: new RegExp(query.trim(), 'i') },
        ],
      }).select('_id');

      // Obtener los IDs de los vehículos encontrados
      const stockIds = stockVehicles.map(vehicle => vehicle._id);

      // Agregar condición para buscar citas con esos vehículos
      if (stockIds.length > 0) {
        searchQuery.stockId = { $in: stockIds };
      } else {
        // Si no se encontraron vehículos, devolver un resultado vacío
        return {
          appointments: [],
          pagination: {
            total: 0,
            page,
            limit,
            pages: 0,
          },
        };
      }
    }

    // Calcular el total de documentos para la paginación
    const total = await InstallationAppointment.countDocuments(searchQuery);
    const pages = Math.ceil(total / limit);

    // Obtener las citas con paginación
    const appointments = await InstallationAppointment.find(searchQuery)
      .select('-notifications -reschedulingHistory')
      .sort({ startTime: 1 })
      .populate('city', 'name state')
      .populate('crew', 'name')
      .populate('neighborhood', 'name')
      .populate('stockId', 'carPlates.plates carNumber brand model year')
      .select('+companyProof.imagesUrls +driverProof.imagesUrls')
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();
    const enrichedAppointments = await this.enrichAppointmentsWithAllData(appointments);

    return {
      appointments: enrichedAppointments,
      pagination: {
        total: enrichedAppointments.length,
        totalResults: total,
        page,
        limit,
        pages,
      },
    };
  }
}
