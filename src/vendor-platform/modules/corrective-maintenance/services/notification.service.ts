import { emailSender, SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID } from '@/constants';
import { ICorrectiveMaintenanceOrder } from '../models/corrective-maintenance-order.model';
import { IQuotation } from '../models/quotation.model';
import { logger } from '@/clean/lib/logger';
import StockVehicle from '@/models/StockVehicleSchema';
import Associate from '@/models/associateSchema';
import { transporter } from '@/modules/platform_connections/emailFunc';
import { slackChannelNotifier } from '@/services/slackBotNotifier/slackBot';

export interface NotificationData {
  customerName?: string;
  customerEmail?: string;
  vehicleInfo?: string;
  workshopName?: string;
  fleetEmail?: string;
}

export class CorrectiveMaintenanceNotificationService {
  /**
   * Send notification when corrective maintenance is requested
   */
  async sendMaintenanceRequestNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const subject = `🔧 Nueva Solicitud de Mantenimiento Correctivo - ${vehicleInfo}`;

      const html = this.generateMaintenanceRequestTemplate({
        customerName: associateInfo?.name || 'Cliente',
        vehicleInfo,
        orderType:
          order.type === 'customer-initiated'
            ? 'Iniciado por Cliente'
            : 'Detectado en Mantenimiento Preventivo',
        failureType: order.failureType === 'known' ? 'Falla Conocida' : 'Requiere Diagnóstico',
        arrivalMethod: order.arrivalMethod === 'driving' ? 'Llega Rodando' : 'Requiere Grúa',
        customerDescription: order.customerDescription || 'No especificada',
        workshopName: notificationData.workshopName || 'Taller',
        orderId: order._id.toString(),
      });

      // Send to workshop
      if (notificationData.workshopName) {
        await transporter.sendMail({
          from: emailSender,
          to: '<EMAIL>', // Workshop email
          subject,
          html,
        });
      }

      // Send to fleet if required
      if (order.approvalType === 'fleet' && notificationData.fleetEmail) {
        await transporter.sendMail({
          from: emailSender,
          to: notificationData.fleetEmail,
          subject,
          html,
        });
      }

      // Send Slack notification
      await this.sendSlackMaintenanceRequestNotification(order, notificationData);

      logger.info('Maintenance request notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending maintenance request notification', { error, orderId: order._id });
    }
  }

  /**
   * Send notification when diagnosis is completed
   */
  async sendDiagnosisCompletedNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const subject = `🔍 Diagnóstico Completado - ${vehicleInfo}`;

      const html = this.generateDiagnosisCompletedTemplate({
        customerName: associateInfo?.name || 'Cliente',
        vehicleInfo,
        diagnosisNotes: order.diagnosisNotes || 'Sin notas adicionales',
        totalEstimatedCost: order.totalEstimatedCost,
        totalEstimatedDuration: order.totalEstimatedDuration,
        servicesCount: order.services.length,
        orderId: order._id.toString(),
      });

      // Send to customer
      if (associateInfo?.email) {
        await transporter.sendMail({
          from: emailSender,
          to: associateInfo.email,
          subject,
          html,
        });
      }

      // Send to fleet if required
      if (order.approvalType === 'fleet' && notificationData.fleetEmail) {
        await transporter.sendMail({
          from: emailSender,
          to: notificationData.fleetEmail,
          subject,
          html,
        });
      }

      // Send Slack notification
      await this.sendSlackDiagnosisCompletedNotification(order);

      logger.info('Diagnosis completed notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending diagnosis completed notification', { error, orderId: order._id });
    }
  }

  /**
   * Send notification when quotation is ready for approval
   */
  async sendQuotationApprovalNotification(
    quotation: IQuotation,
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const subject = `💰 Cotización Lista para Aprobación - ${vehicleInfo}`;

      const html = this.generateQuotationApprovalTemplate({
        customerName: associateInfo?.name || 'Cliente',
        vehicleInfo,
        quotationNumber: quotation.quotationNumber,
        totalCost: quotation.totalEstimatedCost,
        totalDuration: quotation.totalEstimatedDuration,
        // validUntil: quotation.validUntil, // Removed - not needed
        services: quotation.services.map((s) => ({
          name: s.serviceName,
          cost: s.estimatedCost,
          duration: s.estimatedDuration,
        })),
        orderId: order._id.toString(),
        quotationId: quotation._id.toString(),
      });

      // Send to appropriate approver
      const approverEmail =
        quotation.approverEmail ||
        (quotation.approvalType === 'fleet' ? notificationData.fleetEmail : associateInfo?.email);

      if (approverEmail) {
        await transporter.sendMail({
          from: emailSender,
          to: approverEmail,
          subject,
          html,
        });
      }

      // Send Slack notification
      await this.sendSlackQuotationApprovalNotification(quotation, order);

      logger.info('Quotation approval notification sent', {
        quotationId: quotation._id,
        approverEmail,
      });
    } catch (error) {
      logger.error('Error sending quotation approval notification', {
        error,
        quotationId: quotation._id,
      });
    }
  }

  /**
   * Send notification when services are approved and work begins
   */
  async sendWorkStartedNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const subject = `🔧 Trabajo Iniciado - ${vehicleInfo}`;

      const html = this.generateWorkStartedTemplate({
        customerName: associateInfo?.name || 'Cliente',
        vehicleInfo,
        estimatedCompletion: order.slaTarget,
        workshopName: notificationData.workshopName || 'Taller',
        orderId: order._id.toString(),
      });

      // Send to customer
      if (associateInfo?.email) {
        await transporter.sendMail({
          from: emailSender,
          to: associateInfo.email,
          subject,
          html,
        });
      }

      // Send Slack notification
      await this.sendSlackWorkStartedNotification(order, notificationData);

      logger.info('Work started notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending work started notification', { error, orderId: order._id });
    }
  }

  /**
   * Send notification when work is completed
   */
  async sendWorkCompletedNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const subject = `✅ Mantenimiento Completado - ${vehicleInfo}`;

      const html = this.generateWorkCompletedTemplate({
        customerName: associateInfo?.name || 'Cliente',
        vehicleInfo,
        totalCost: order.totalActualCost || order.totalEstimatedCost,
        totalDuration: order.totalActualDuration || order.totalEstimatedDuration,
        workshopName: notificationData.workshopName || 'Taller',
        orderId: order._id.toString(),
      });

      // Send to customer
      if (associateInfo?.email) {
        await transporter.sendMail({
          from: emailSender,
          to: associateInfo.email,
          subject,
          html,
        });
      }

      // Send to fleet
      if (notificationData.fleetEmail) {
        await transporter.sendMail({
          from: emailSender,
          to: notificationData.fleetEmail,
          subject,
          html,
        });
      }

      // Send Slack notification
      await this.sendSlackWorkCompletedNotification(order, notificationData);

      logger.info('Work completed notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending work completed notification', { error, orderId: order._id });
    }
  }

  /**
   * Get vehicle information for notifications
   */
  private async getVehicleInfo(stockId: string): Promise<string> {
    try {
      const vehicle = await StockVehicle.findById(stockId);
      if (!vehicle) return 'Vehículo no encontrado';

      return `${vehicle.brand} ${vehicle.model} ${vehicle.year} - ${vehicle.carPlates?.plates || 'Sin placas'}`;
    } catch (error) {
      logger.error('Error getting vehicle info', { error, stockId });
      return 'Información del vehículo no disponible';
    }
  }

  /**
   * Get associate information for notifications
   */
  private async getAssociateInfo(associateId: string): Promise<any> {
    try {
      const associate = await Associate.findById(associateId);
      return associate;
    } catch (error) {
      logger.error('Error getting associate info', { error, associateId });
      return null;
    }
  }

  /**
   * Generate maintenance request email template
   */
  private generateMaintenanceRequestTemplate(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Nueva Solicitud de Mantenimiento Correctivo</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">🔧 Nueva Solicitud de Mantenimiento Correctivo</h1>

          <p>Hola,</p>

          <p>Se ha recibido una nueva solicitud de mantenimiento correctivo:</p>

          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Detalles del Vehículo:</h3>
            <p><strong>Vehículo:</strong> ${data.vehicleInfo}</p>
            <p><strong>Cliente:</strong> ${data.customerName}</p>
            <p><strong>Tipo de Solicitud:</strong> ${data.orderType}</p>
            <p><strong>Tipo de Falla:</strong> ${data.failureType}</p>
            <p><strong>Método de Llegada:</strong> ${data.arrivalMethod}</p>
            <p><strong>Descripción:</strong> ${data.customerDescription}</p>
            <p><strong>Taller Asignado:</strong> ${data.workshopName}</p>
          </div>

          <p><strong>ID de Orden:</strong> ${data.orderId}</p>

          <p>Por favor, procede con el diagnóstico correspondiente.</p>

          <p>Saludos,<br>Equipo OneCarNow</p>
        </div>
      </body>
      </html>
    `;
  }

  // Additional template methods implementation
  private generateDiagnosisCompletedTemplate(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Diagnóstico Completado</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">🔍 Diagnóstico Completado</h1>

          <p>Hola ${data.customerName},</p>

          <p>El diagnóstico de tu vehículo ha sido completado:</p>

          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Detalles del Diagnóstico:</h3>
            <p><strong>Vehículo:</strong> ${data.vehicleInfo}</p>
            <p><strong>Servicios Identificados:</strong> ${data.servicesCount}</p>
            <p><strong>Costo Total Estimado:</strong> $${data.totalEstimatedCost.toLocaleString()}</p>
            <p><strong>Tiempo Estimado:</strong> ${data.totalEstimatedDuration} horas</p>
            <p><strong>Notas del Diagnóstico:</strong> ${data.diagnosisNotes}</p>
          </div>

          <p>En breve recibirás la cotización detallada para tu aprobación.</p>

          <p><strong>ID de Orden:</strong> ${data.orderId}</p>

          <p>Saludos,<br>Equipo OneCarNow</p>
        </div>
      </body>
      </html>
    `;
  }

  private generateQuotationApprovalTemplate(data: any): string {
    const servicesHtml = data.services
      .map(
        (service: any) => `
      <tr>
        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${service.name}</td>
        <td style="padding: 8px; border-bottom: 1px solid #ddd;">$${service.cost.toLocaleString()}</td>
        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${service.duration}h</td>
      </tr>
    `
      )
      .join('');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Cotización Lista para Aprobación</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">💰 Cotización Lista para Aprobación</h1>

          <p>Hola ${data.customerName},</p>

          <p>Tu cotización está lista para revisión y aprobación:</p>

          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Detalles de la Cotización:</h3>
            <p><strong>Vehículo:</strong> ${data.vehicleInfo}</p>
            <p><strong>Número de Cotización:</strong> ${data.quotationNumber}</p>
            <p><strong>Costo Total:</strong> $${data.totalCost.toLocaleString()}</p>
            <p><strong>Tiempo Total:</strong> ${data.totalDuration} horas</p>
            <p><strong>Válida hasta:</strong> ${new Date(data.validUntil).toLocaleDateString()}</p>
          </div>

          <h3>Servicios Incluidos:</h3>
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
              <tr style="background: #2c5aa0; color: white;">
                <th style="padding: 10px; text-align: left;">Servicio</th>
                <th style="padding: 10px; text-align: left;">Costo</th>
                <th style="padding: 10px; text-align: left;">Tiempo</th>
              </tr>
            </thead>
            <tbody>
              ${servicesHtml}
            </tbody>
          </table>

          <p>Por favor, revisa y aprueba los servicios que deseas realizar.</p>

          <p>Saludos,<br>Equipo OneCarNow</p>
        </div>
      </body>
      </html>
    `;
  }

  private generateWorkStartedTemplate(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Trabajo Iniciado</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">🔧 Trabajo Iniciado</h1>

          <p>Hola ${data.customerName},</p>

          <p>Hemos iniciado el trabajo en tu vehículo:</p>

          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Detalles del Trabajo:</h3>
            <p><strong>Vehículo:</strong> ${data.vehicleInfo}</p>
            <p><strong>Taller:</strong> ${data.workshopName}</p>
            <p><strong>Finalización Estimada:</strong> ${new Date(data.estimatedCompletion).toLocaleDateString()}</p>
          </div>

          <p>Te mantendremos informado del progreso.</p>

          <p>Saludos,<br>Equipo OneCarNow</p>
        </div>
      </body>
      </html>
    `;
  }

  private generateWorkCompletedTemplate(data: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Mantenimiento Completado</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #2c5aa0;">✅ Mantenimiento Completado</h1>

          <p>Hola ${data.customerName},</p>

          <p>¡Excelentes noticias! El mantenimiento correctivo de tu vehículo ha sido completado exitosamente.</p>

          <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Resumen del Trabajo:</h3>
            <p><strong>Vehículo:</strong> ${data.vehicleInfo}</p>
            <p><strong>Taller:</strong> ${data.workshopName}</p>
            <p><strong>Costo Total:</strong> $${data.totalCost.toLocaleString()}</p>
            <p><strong>Tiempo Total:</strong> ${data.totalDuration} horas</p>
          </div>

          <p>Tu vehículo está listo para ser retirado. Por favor, contacta al taller para coordinar la entrega.</p>

          <p>Gracias por confiar en OneCarNow para el cuidado de tu vehículo.</p>

          <p>Saludos,<br>Equipo OneCarNow</p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Send Slack notification for maintenance request
   */
  private async sendSlackMaintenanceRequestNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🔧 Nueva Solicitud de Mantenimiento Correctivo',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Vehículo:* ${vehicleInfo}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cliente:* ${associateInfo?.name || 'No disponible'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Tipo:* ${order.type === 'customer-initiated' ? 'Iniciado por Cliente' : 'Detectado en Preventivo'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Falla:* ${order.failureType === 'known' ? 'Conocida' : 'Requiere Diagnóstico'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Llegada:* ${order.arrivalMethod === 'driving' ? 'Rodando' : 'Grúa'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Taller:* ${notificationData.workshopName || 'No asignado'}`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Descripción:* ${order.customerDescription || 'Sin descripción'}`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🆔 ID: ${order._id} | 📅 ${new Date(order.createdAt).toLocaleString('es-MX')}`,
            },
          ],
        },
      ];

      await slackChannelNotifier({
        message: {
          text: `Nueva solicitud de mantenimiento correctivo: ${vehicleInfo}`,
          blocks,
        },
        BotToken: process.env.SLACK_NOTIFIER_BOT_TOKEN!,
        ChannelId: SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID,
      });

      logger.info('Slack maintenance request notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending Slack maintenance request notification', { error, orderId: order._id });
    }
  }

  /**
   * Send Slack notification for diagnosis completed
   */
  private async sendSlackDiagnosisCompletedNotification(order: ICorrectiveMaintenanceOrder): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🔍 Diagnóstico Completado',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Vehículo:* ${vehicleInfo}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cliente:* ${associateInfo?.name || 'No disponible'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Servicios:* ${order.services.length}`,
            },
            {
              type: 'mrkdwn',
              text: `*Costo Est.:* $${order.totalEstimatedCost?.toLocaleString('es-MX') || 'Por definir'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Tiempo Est.:* ${order.totalEstimatedDuration || 'Por definir'}h`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Notas del Diagnóstico:*\n${order.diagnosisNotes || 'Sin notas adicionales'}`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🆔 ID: ${order._id} | 📅 ${new Date().toLocaleString('es-MX')}`,
            },
          ],
        },
      ];

      await slackChannelNotifier({
        message: {
          text: `Diagnóstico completado: ${vehicleInfo}`,
          blocks,
        },
        BotToken: process.env.SLACK_NOTIFIER_BOT_TOKEN!,
        ChannelId: SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID,
      });

      logger.info('Slack diagnosis completed notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending Slack diagnosis completed notification', { error, orderId: order._id });
    }
  }

  /**
   * Send Slack notification for quotation approval
   */
  private async sendSlackQuotationApprovalNotification(
    quotation: IQuotation,
    order: ICorrectiveMaintenanceOrder
    // notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '💰 Cotización Lista para Aprobación',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Cotización:* ${quotation.quotationNumber}`,
            },
            {
              type: 'mrkdwn',
              text: `*Vehículo:* ${vehicleInfo}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cliente:* ${associateInfo?.name || 'No disponible'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Monto:* $${quotation.totalEstimatedCost.toLocaleString('es-MX')}`,
            },
            {
              type: 'mrkdwn',
              text: `*Tiempo:* ${quotation.totalEstimatedDuration}h`,
            },
            {
              type: 'mrkdwn',
              text: `*Tipo:* ${quotation.approvalType === 'fleet' ? 'Fleet' : 'Cliente'}`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Servicios (${quotation.services?.length || 0}):*\n${
              quotation.services && Array.isArray(quotation.services)
                ? quotation.services
                    .filter((service: any) => service && typeof service === 'object')
                    .map(
                      (service: any) =>
                        `• ${service.serviceName || 'Servicio sin nombre'} - $${(service.estimatedCost || 0).toLocaleString('es-MX')}`
                    )
                    .join('\n')
                : 'No hay servicios disponibles'
            }`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🆔 Cotización: ${quotation._id} | 📅 ${new Date(quotation.createdAt).toLocaleString('es-MX')}`,
            },
          ],
        },
      ];

      await slackChannelNotifier({
        message: {
          text: `Cotización lista para aprobación: ${quotation.quotationNumber} - $${quotation.totalEstimatedCost.toLocaleString('es-MX')}`,
          blocks,
        },
        BotToken: process.env.SLACK_NOTIFIER_BOT_TOKEN!,
        ChannelId: SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID,
      });

      logger.info('Slack quotation approval notification sent', { quotationId: quotation._id });
    } catch (error) {
      logger.error('Error sending Slack quotation approval notification', {
        error,
        quotationId: quotation._id,
      });
    }
  }

  /**
   * Send Slack notification for work started
   */
  private async sendSlackWorkStartedNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '🔧 Trabajo Iniciado',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Vehículo:* ${vehicleInfo}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cliente:* ${associateInfo?.name || 'No disponible'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Taller:* ${notificationData.workshopName || 'No especificado'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Servicios:* ${Array.isArray(order.services) ? order.services.length : 0}`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Servicios:* ${Array.isArray(order.services) ? order.services.length : 0} servicio(s) identificado(s)`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🆔 ID: ${order._id} | 📅 ${new Date().toLocaleString('es-MX')}`,
            },
          ],
        },
      ];

      await slackChannelNotifier({
        ChannelId: SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID,
        message: {
          text: `Trabajo iniciado: ${vehicleInfo}`,
          blocks,
        },
        BotToken: process.env.SLACK_NOTIFIER_BOT_TOKEN!,
      });

      logger.info('Slack work started notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending Slack work started notification', { error, orderId: order._id });
    }
  }

  /**
   * Send Slack notification for work completed
   */
  private async sendSlackWorkCompletedNotification(
    order: ICorrectiveMaintenanceOrder,
    notificationData: NotificationData
  ): Promise<void> {
    try {
      const vehicleInfo = await this.getVehicleInfo(order.stockId.toString());
      const associateInfo = await this.getAssociateInfo(order.associateId.toString());

      const blocks = [
        {
          type: 'header',
          text: {
            type: 'plain_text',
            text: '✅ Mantenimiento Completado',
            emoji: true,
          },
        },
        {
          type: 'section',
          fields: [
            {
              type: 'mrkdwn',
              text: `*Vehículo:* ${vehicleInfo}`,
            },
            {
              type: 'mrkdwn',
              text: `*Cliente:* ${associateInfo?.name || 'No disponible'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Taller:* ${notificationData.workshopName || 'No especificado'}`,
            },
            {
              type: 'mrkdwn',
              text: `*Costo Final:* $${(order.totalActualCost || order.totalEstimatedCost || 0).toLocaleString('es-MX')}`,
            },
            {
              type: 'mrkdwn',
              text: `*Tiempo:* ${order.totalActualDuration || order.totalEstimatedDuration || 0}h`,
            },
          ],
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Servicios:* ${Array.isArray(order.services) ? order.services.length : 0} servicio(s) completado(s)`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🆔 ID: ${order._id} | 📅 ${new Date().toLocaleString('es-MX')}`,
            },
          ],
        },
      ];

      await slackChannelNotifier({
        message: {
          text: `Mantenimiento completado: ${vehicleInfo}`,
          blocks,
        },
        BotToken: process.env.SLACK_NOTIFIER_BOT_TOKEN!,
        ChannelId: SLACK_CORRECTIVE_MAINTENANCE_CHANNEL_ID,
      });

      logger.info('Slack work completed notification sent', { orderId: order._id });
    } catch (error) {
      logger.error('Error sending Slack work completed notification', { error, orderId: order._id });
    }
  }
}

export const correctiveMaintenanceNotificationService = new CorrectiveMaintenanceNotificationService();
