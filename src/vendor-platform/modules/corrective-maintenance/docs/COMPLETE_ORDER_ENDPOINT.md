# Complete Order Endpoint Documentation

## Overview
The `completeOrder` endpoint allows workshops to mark a corrective maintenance order as completed. This endpoint validates that all services are finished, calculates final costs and metrics, uploads completion photos, and sends notifications.

## Endpoint Details

**URL**: `POST /vendor-platform/corrective-maintenance/orders/:orderId/complete`

**Authentication**: Required (Vendor Platform Token)

**Parameters**:
- `orderId` (path parameter): The ID of the corrective maintenance order to complete

## Request

### Headers
```
Authorization: Bearer <vendor_platform_token>
Content-Type: multipart/form-data
```

### Body (Form Data)
```json
{
  "completionNotes": "All services completed successfully. Vehicle tested and ready for delivery.",
  "finalInspectionPassed": true,
  "customerSatisfactionRating": 5,
  "workQualityRating": 5,
  "totalActualCost": 12500.00,
  "recommendationsForFuture": [
    "Schedule brake inspection in 6 months",
    "Monitor tire wear patterns"
  ]
}
```

### File Uploads
- `afterPhotos[]`: Multiple image files showing completed work (max 10)

## Response

### Success Response (200)
```json
{
  "message": "Order completed successfully",
  "data": {
    "_id": "order_id_123",
    "status": "completed",
    "completionDate": "2024-01-15T16:30:00.000Z",
    "startDate": "2024-01-15T10:30:00.000Z",
    "totalDurationHours": 6,
    "totalEstimatedCost": 12000,
    "totalActualCost": 12500,
    "slaTarget": "2024-01-17T10:30:00.000Z",
    "slaActual": "2024-01-15T16:30:00.000Z",
    "slaCompliance": true,
    "completionNotes": "All services completed successfully. Vehicle tested and ready for delivery.",
    "finalInspectionPassed": true,
    "customerSatisfactionRating": 5,
    "workQualityRating": 5,
    "recommendationsForFuture": [
      "Schedule brake inspection in 6 months",
      "Monitor tire wear patterns"
    ],
    "afterPhotos": [
      "https://s3.amazonaws.com/bucket/completion1.jpg",
      "https://s3.amazonaws.com/bucket/completion2.jpg"
    ],
    "services": [
      {
        "_id": "service_id_1",
        "serviceName": "Frenos",
        "status": "completed",
        "estimatedCost": 2000,
        "actualCost": 2200,
        "actualStartTime": "2024-01-15T10:30:00.000Z",
        "actualEndTime": "2024-01-15T14:00:00.000Z",
        "slaCompliance": true,
        "qualityCheckPassed": true
      },
      {
        "_id": "service_id_2",
        "serviceName": "Llantas",
        "status": "completed",
        "estimatedCost": 10000,
        "actualCost": 10300,
        "actualStartTime": "2024-01-15T14:00:00.000Z",
        "actualEndTime": "2024-01-15T16:30:00.000Z",
        "slaCompliance": true,
        "qualityCheckPassed": true
      }
    ],
    "workshop": {
      "name": "Taller Central",
      "location": "...",
      "contact": "..."
    },
    "stockVehicle": {
      "brand": "Toyota",
      "model": "Corolla",
      "year": 2020,
      "plate": "ABC-123"
    },
    "associate": {
      "name": "Juan Pérez",
      "email": "<EMAIL>",
      "phone": "+521234567890"
    }
  }
}
```

### Error Responses

#### 400 - Bad Request (Order Already Completed)
```json
{
  "message": "Order is already completed",
  "error": "..."
}
```

#### 400 - Bad Request (Services Not Completed)
```json
{
  "message": "Cannot complete order. 2 service(s) are still pending completion. Completed: 1, In Progress: 1, Waiting for Parts: 1",
  "error": "..."
}
```

#### 400 - Bad Request (Invalid Rating)
```json
{
  "message": "Customer satisfaction rating must be between 1 and 5",
  "error": "..."
}
```

#### 404 - Not Found
```json
{
  "message": "Corrective maintenance order not found",
  "error": "..."
}
```

#### 401 - Unauthorized
```json
{
  "message": "No autorizado",
  "data": "v"
}
```

## Business Logic

### Prerequisites
1. Order must exist and belong to the requesting organization
2. Order cannot already be completed or cancelled
3. All active services must be in `completed` status
4. Services in `cancelled` status are ignored for completion validation

### Process Flow
1. **Validation**: Verify order exists, belongs to organization, and can be completed
2. **Service Check**: Ensure all active services are completed
3. **File Upload**: Process and upload completion photos to S3
4. **Cost Calculation**: Calculate final actual cost from all services
5. **Metrics Calculation**: Calculate duration, SLA compliance, and ratings
6. **Data Update**: Update order with completion information
7. **History Update**: Update vehicle maintenance history
8. **Notifications**: Send completion notifications to stakeholders
9. **Logging**: Record completion details for audit trail

### Automatic Calculations

#### Total Duration
- Calculated from `startDate` to completion time
- Stored in hours for easy reporting

#### SLA Compliance
- Compares completion time against quotation SLA target
- Considers overall order SLA from quotation

#### Total Actual Cost
- Sums actual costs from all services
- Falls back to estimated costs if actual not provided
- Can be overridden with `totalActualCost` parameter

### Quality Metrics

#### Ratings (1-5 scale)
- **Customer Satisfaction Rating**: Overall customer satisfaction
- **Work Quality Rating**: Technical quality of work performed

#### Inspection Results
- **Final Inspection Passed**: Boolean indicating if final inspection passed
- **Recommendations for Future**: Array of maintenance recommendations

### Special Features

#### Photo Management
- Automatically uploads completion photos to S3
- Maintains organized folder structure
- Supports multiple file formats

#### Vehicle History Integration
- Updates comprehensive vehicle maintenance history
- Records completion details and costs
- Tracks service performance metrics

#### Maintenance Summary
- Creates detailed summary for vehicle records
- Includes all services performed and costs
- Records quality ratings and SLA compliance

## Example Usage

### cURL - Basic Completion
```bash
curl -X POST \
  http://localhost:3000/vendor-platform/corrective-maintenance/orders/order_id_123/complete \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "completionNotes=All work completed successfully" \
  -F "finalInspectionPassed=true" \
  -F "workQualityRating=5"
```

### cURL - Complete with Photos and Ratings
```bash
curl -X POST \
  http://localhost:3000/vendor-platform/corrective-maintenance/orders/order_id_123/complete \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "completionNotes=Excellent work completed on time" \
  -F "finalInspectionPassed=true" \
  -F "customerSatisfactionRating=5" \
  -F "workQualityRating=5" \
  -F "totalActualCost=12500" \
  -F "recommendationsForFuture=[\"Schedule brake check in 6 months\"]" \
  -F "afterPhotos=@completion1.jpg" \
  -F "afterPhotos=@completion2.jpg"
```

### JavaScript
```javascript
const formData = new FormData();
formData.append('completionNotes', 'All services completed successfully');
formData.append('finalInspectionPassed', 'true');
formData.append('customerSatisfactionRating', '5');
formData.append('workQualityRating', '5');
formData.append('totalActualCost', '12500');

// Add photos
formData.append('afterPhotos', completionPhoto1);
formData.append('afterPhotos', completionPhoto2);

const response = await fetch('/vendor-platform/corrective-maintenance/orders/order_id_123/complete', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: formData
});

const result = await response.json();
console.log(result);
```

## Related Endpoints

- `POST /vendor-platform/corrective-maintenance/orders/:orderId/start` - Start work on order
- `PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress` - Update service progress
- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get order details

## Notes

- All active services must be completed before order can be completed
- Cancelled services are ignored for completion validation
- Completion photos are automatically uploaded to S3
- Vehicle maintenance history is automatically updated
- Notifications are sent to fleet and customer
- SLA compliance is automatically calculated
- All completion data is logged for audit purposes
- Ratings must be between 1 and 5 if provided
