# Update Service Progress Endpoint Documentation

## Overview
The `updateServiceProgress` endpoint allows workshops to update the progress of individual corrective maintenance services. This includes status updates, progress photos, notes, cost updates, and parts management.

## Endpoint Details

**URL**: `PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress`

**Authentication**: Required (Vendor Platform Token)

**Parameters**:
- `serviceId` (path parameter): The ID of the corrective service to update

## Request

### Headers
```
Authorization: Bearer <vendor_platform_token>
Content-Type: multipart/form-data
```

### Body (Form Data)
```json
{
  "status": "in-progress | completed | waiting-for-parts | cancelled",
  "notes": "Progress notes or comments",
  "actualCost": 2500.00,
  "qualityCheckPassed": true,
  "technicalNotes": ["Replaced brake pads", "Checked brake fluid"],
  "progressPhotos": ["url1", "url2"],
  "partsUsed": [
    {
      "name": "Brake Pads",
      "quantity": 4,
      "unitCost": 150.00,
      "supplier": "AutoParts Inc"
    }
  ]
}
```

### File Uploads
- `progressPhotos[]`: Multiple image files (max 5)

## Response

### Success Response (200)
```json
{
  "message": "Service progress updated successfully",
  "data": {
    "_id": "service_id_123",
    "serviceName": "Frenos",
    "status": "completed",
    "estimatedCost": 2000,
    "actualCost": 2500,
    "estimatedDuration": 48,
    "actualStartTime": "2024-01-15T10:30:00.000Z",
    "actualEndTime": "2024-01-15T14:30:00.000Z",
    "slaTarget": "2024-01-17T10:30:00.000Z",
    "slaActual": "2024-01-15T14:30:00.000Z",
    "slaCompliance": true,
    "qualityCheckPassed": true,
    "progressPhotos": [
      "https://s3.amazonaws.com/bucket/progress1.jpg",
      "https://s3.amazonaws.com/bucket/progress2.jpg"
    ],
    "technicalNotes": [
      "2024-01-15T10:30:00.000Z: Started brake service",
      "2024-01-15T14:30:00.000Z: Progress notes or comments",
      "Replaced brake pads",
      "Checked brake fluid"
    ],
    "parts": [
      {
        "name": "Brake Pads",
        "quantity": 4,
        "unitCost": 150.00,
        "totalCost": 600.00,
        "supplier": "AutoParts Inc",
        "isAvailable": true
      }
    ],
    "totalPartsCost": 600.00,
    "orderId": {
      "_id": "order_id_123",
      "status": "completed",
      "workshop": {
        "name": "Taller Central",
        "location": "..."
      },
      "stockVehicle": {
        "brand": "Toyota",
        "model": "Corolla",
        "year": 2020,
        "plate": "ABC-123"
      },
      "associate": {
        "name": "Juan Pérez",
        "email": "<EMAIL>",
        "phone": "+************"
      }
    }
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "message": "Cannot update progress for service with status: completed",
  "error": "..."
}
```

#### 404 - Not Found
```json
{
  "message": "Corrective service not found",
  "error": "..."
}
```

#### 401 - Unauthorized
```json
{
  "message": "No autorizado",
  "data": "v"
}
```

## Business Logic

### Prerequisites
1. Service must exist and belong to the requesting organization
2. Service cannot be in `completed` or `cancelled` status (unless updating to those statuses)

### Process Flow
1. **Validation**: Verify service exists and belongs to organization
2. **Status Check**: Ensure service can be updated
3. **File Upload**: Process and upload progress photos to S3
4. **Data Update**: Update service with provided information
5. **Status Management**: Handle status transitions and timestamps
6. **Parts Management**: Update parts information and costs
7. **Order Status**: Automatically update order status based on all services
8. **Notifications**: Send notifications for completed services
9. **Logging**: Record all changes for audit trail

### Status Transitions

#### Service Status
- `not-started` → `in-progress`
- `in-progress` → `completed`
- `in-progress` → `waiting-for-parts`
- `waiting-for-parts` → `in-progress`
- Any status → `cancelled`

#### Automatic Order Status Updates
- All services `completed` → Order `COMPLETED`
- All services `cancelled` → Order `CANCELLED`
- All remaining services `waiting-for-parts` → Order `WAITING_FOR_PARTS`
- Any service `in-progress` → Order `IN_PROGRESS`

### Special Features

#### Photo Management
- Automatically uploads photos to S3
- Maintains history of all progress photos
- Supports multiple file formats (JPEG, PNG, etc.)

#### Parts Tracking
- Updates existing parts or adds new ones
- Automatically calculates total parts cost
- Tracks supplier information

#### Notes and Documentation
- Timestamps all notes automatically
- Maintains technical notes history
- Supports both general notes and technical notes

#### SLA Tracking
- Automatically sets completion time when status = 'completed'
- Calculates SLA compliance
- Tracks actual vs estimated duration

## Example Usage

### cURL - Basic Status Update
```bash
curl -X PATCH \
  http://localhost:3000/vendor-platform/corrective-maintenance/services/service_id_123/progress \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "status=completed" \
  -F "notes=Service completed successfully" \
  -F "qualityCheckPassed=true"
```

### cURL - With Photos and Parts
```bash
curl -X PATCH \
  http://localhost:3000/vendor-platform/corrective-maintenance/services/service_id_123/progress \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "status=in-progress" \
  -F "notes=Brake pads replaced" \
  -F "actualCost=2500" \
  -F "partsUsed=[{\"name\":\"Brake Pads\",\"quantity\":4,\"unitCost\":150}]" \
  -F "progressPhotos=@photo1.jpg" \
  -F "progressPhotos=@photo2.jpg"
```

### JavaScript
```javascript
const formData = new FormData();
formData.append('status', 'completed');
formData.append('notes', 'Service completed successfully');
formData.append('actualCost', '2500');
formData.append('qualityCheckPassed', 'true');

// Add photos
formData.append('progressPhotos', photoFile1);
formData.append('progressPhotos', photoFile2);

const response = await fetch('/vendor-platform/corrective-maintenance/services/service_id_123/progress', {
  method: 'PATCH',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: formData
});

const result = await response.json();
console.log(result);
```

## Related Endpoints

- `POST /vendor-platform/corrective-maintenance/orders/:orderId/start` - Start work on order
- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get order with services
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/complete` - Complete entire order

## Notes

- Progress photos are automatically uploaded to S3
- Order status is automatically updated based on all services
- Notifications are sent when services are completed
- All changes are logged for audit purposes
- SLA compliance is automatically calculated
- Parts costs are automatically recalculated when parts are updated
