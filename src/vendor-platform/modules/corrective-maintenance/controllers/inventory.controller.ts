import { AsyncController } from '@/types&interfaces/types';
import { InventoryService, VehicleCompatibility } from '../services/inventory.service';
import { IPart } from '../models/corrective-service.model';
import { PartCategory } from '../models/inventory.model';

/**
 * Check parts availability for a service
 */
export const checkPartsAvailability: AsyncController = async (req, res) => {
  try {
    const { parts, vehicleInfo } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!parts || !Array.isArray(parts)) {
      return res.status(400).json({
        message: 'Parts array is required',
      });
    }

    // Validate parts structure
    for (const part of parts) {
      if (!part.name || !part.quantity || part.quantity <= 0) {
        return res.status(400).json({
          message: 'Each part must have a name and positive quantity',
        });
      }
    }

    const availability = await InventoryService.checkPartsAvailability(
      parts as IPart[],
      organizationId,
      vehicleInfo as VehicleCompatibility
    );

    const allPartsAvailable = availability.every((part) => part.isAvailable);
    const partsNeedingOrder = availability.filter((part) => part.needsOrdering);

    return res.status(200).json({
      message: 'Parts availability checked successfully',
      data: {
        allPartsAvailable,
        partsAvailability: availability,
        partsNeedingOrder: partsNeedingOrder.length,
        summary: {
          totalParts: parts.length,
          availableParts: availability.filter((p) => p.isAvailable).length,
          unavailableParts: availability.filter((p) => !p.isAvailable).length,
        },
      },
    });
  } catch (error: any) {
    console.error('Error checking parts availability:', error);
    return res.status(500).json({
      message: 'Error checking parts availability',
      error: error.message,
    });
  }
};

/**
 * Search parts in inventory
 */
export const searchInventoryParts: AsyncController = async (req, res) => {
  try {
    const { search, category, vehicleBrand, vehicleModel, vehicleYear } = req.query;
    const organizationId = req.userVendor.organizationId;

    if (!search) {
      return res.status(400).json({
        message: 'Search term is required',
      });
    }

    let vehicleInfo: VehicleCompatibility | undefined;
    if (vehicleBrand && vehicleModel && vehicleYear) {
      vehicleInfo = {
        brand: vehicleBrand as string,
        model: vehicleModel as string,
        year: parseInt(vehicleYear as string),
      };
    }

    const parts = await InventoryService.searchParts(
      organizationId,
      search as string,
      category as PartCategory,
      vehicleInfo
    );

    return res.status(200).json({
      message: 'Parts search completed successfully',
      data: parts,
    });
  } catch (error: any) {
    console.error('Error searching inventory parts:', error);
    return res.status(500).json({
      message: 'Error searching inventory parts',
      error: error.message,
    });
  }
};

/**
 * Get inventory summary
 */
export const getInventorySummary: AsyncController = async (req, res) => {
  try {
    const organizationId = req.userVendor.organizationId;

    const summary = await InventoryService.getInventorySummary(organizationId);

    return res.status(200).json({
      message: 'Inventory summary retrieved successfully',
      data: summary,
    });
  } catch (error: any) {
    console.error('Error getting inventory summary:', error);
    return res.status(500).json({
      message: 'Error getting inventory summary',
      error: error.message,
    });
  }
};

/**
 * Reserve parts for a service
 */
export const reserveParts: AsyncController = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { parts } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!parts || !Array.isArray(parts)) {
      return res.status(400).json({
        message: 'Parts array is required',
      });
    }

    await InventoryService.reserveParts(parts as IPart[], organizationId, orderId);

    return res.status(200).json({
      message: 'Parts reserved successfully',
    });
  } catch (error: any) {
    console.error('Error reserving parts:', error);
    return res.status(500).json({
      message: 'Error reserving parts',
      error: error.message,
    });
  }
};

/**
 * Release reserved parts
 */
export const releaseReservedParts: AsyncController = async (req, res) => {
  try {
    const { parts } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!parts || !Array.isArray(parts)) {
      return res.status(400).json({
        message: 'Parts array is required',
      });
    }

    await InventoryService.releaseReservedParts(parts as IPart[], organizationId);

    return res.status(200).json({
      message: 'Reserved parts released successfully',
    });
  } catch (error: any) {
    console.error('Error releasing reserved parts:', error);
    return res.status(500).json({
      message: 'Error releasing reserved parts',
      error: error.message,
    });
  }
};

/**
 * Consume parts (when service is completed)
 */
export const consumeParts: AsyncController = async (req, res) => {
  try {
    const { parts } = req.body;
    const organizationId = req.userVendor.organizationId;

    if (!parts || !Array.isArray(parts)) {
      return res.status(400).json({
        message: 'Parts array is required',
      });
    }

    await InventoryService.consumeParts(parts as IPart[], organizationId);

    return res.status(200).json({
      message: 'Parts consumed successfully',
    });
  } catch (error: any) {
    console.error('Error consuming parts:', error);
    return res.status(500).json({
      message: 'Error consuming parts',
      error: error.message,
    });
  }
};

/**
 * Check if service can start based on parts availability
 */
export const canStartService: AsyncController = async (req, res) => {
  try {
    // const { serviceId } = req.params;
    const { ignorePartsAvailability = false } = req.query;
    // const organizationId = req.userVendor.organizationId;

    // This would typically fetch the service and check its parts
    // For now, we'll return a basic response
    // TODO: Implement actual service lookup and parts checking

    return res.status(200).json({
      message: 'Service start check completed',
      data: {
        canStart: true, // This should be calculated based on actual parts availability
        reason: ignorePartsAvailability ? 'Parts availability check bypassed' : 'All parts available',
        missingParts: [],
      },
    });
  } catch (error: any) {
    console.error('Error checking if service can start:', error);
    return res.status(500).json({
      message: 'Error checking if service can start',
      error: error.message,
    });
  }
};
