import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@vendor/db';

export interface IServiceType extends Document {
  name: string;
  description?: string;
  duration: number; // duración en minutos
  organization: mongoose.Types.ObjectId; // referencia a la organización
  isActive: boolean;
  maintenanceType: 'preventive' | 'corrective'; // tipo de mantenimiento
  price?: number;
  includedServices?: string[];
}

const ServiceTypeSchema = new Schema({
  name: { type: String, required: true },
  description: String,
  duration: { type: Number, required: true },
  organization: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
  isActive: { type: Boolean, default: true },
  maintenanceType: {
    type: String,
    enum: ['preventive', 'corrective'],
    required: true,
    default: 'preventive',
  },
  price: { type: Number },
  // array of services that are included in this service type like oil change, filter change, using string array for now
  includedServices: [{ type: String }],
});

export const ServiceTypeVendorModel = vendorDB.model<IServiceType>('ServiceType', ServiceTypeSchema);
