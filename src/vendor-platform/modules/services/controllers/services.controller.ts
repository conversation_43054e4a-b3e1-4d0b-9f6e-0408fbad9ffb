import { AsyncController } from '@/types&interfaces/types';
import { serviceClassInstance } from '../services/service.service';
import { organizationsService } from '../../organizations/services/organizations.service';
import { stockVehicleService } from '../../stockVehicles/services/vehicles.service';
import Associate from '@/models/associateSchema';
import StockVehicle from '@/models/StockVehicleSchema';
import { logger } from '@/clean/lib/logger';
import { errorCodeUtils } from '@/utils/error.utils';

export const createService: AsyncController = async (req, res) => {
  try {
    const workshopId = req.body.workshopId;

    if (!workshopId) {
      return res.status(400).send({ message: 'Workshop ID is required' });
    }

    const organizationId = req.userVendor.organizationId;
    const serviceData = {
      ...req.body,
      workshopId,
      organizationId,
    };

    const result = await serviceClassInstance.createService(serviceData, req.files);

    return res.status(201).send({ message: 'Service registered successfully', data: result });
  } catch (error) {
    return res.status(500).send({ message: 'Error registering service', error });
  }
};

export const getServicesByStockId: AsyncController = async (req, res) => {
  const { stockId, associateId } = req.params;
  try {
    const organizationId = req.userVendor.organizationId;

    const services = await serviceClassInstance.findServicesByStockId({
      stockId,
      associateId,
      organizationId,
      options: { includeWorkshop: true },
    });

    return res.status(200).send({ message: 'Services found', data: services });
  } catch (error: any) {
    const message = error.message || 'Error fetching services';
    return res.status(500).send({ message, error });
  }
};

export const completeService: AsyncController = async (req, res) => {
  const { serviceId } = req.params;
  try {
    const data = req.body;
    const files = req.files as Express.Multer.File[];
    data.organizationId = req.userVendor.organizationId;

    const service = await serviceClassInstance.completeService(serviceId, data, files);

    return res.status(200).send({ message: 'Service completed', data: service });
  } catch (error: any) {
    const message = error.message || 'Error completing service';

    return res.status(500).send({ message, error });
  }
};

export const getServicesByAssociateId: AsyncController = async (req, res) => {
  const { associateId } = req.params;
  try {
    const organizationId = req.userVendor.organizationId;
    const services = await serviceClassInstance.findServicesByAssociateId(associateId, organizationId);
    return res.status(200).send({ message: 'Services found', data: services });
  } catch (error: any) {
    const message = error.message || 'Error fetching services';
    return res.status(500).send({ message, error });
  }
};

export const getServicesAndVendorDetailsByAssociateId: AsyncController = async (req, res) => {
  const { associateId } = req.params;
  try {
    const services = await serviceClassInstance.findServicesByAssociateId(associateId);
    if (!services) {
      logger.error(
        `[getServicesAndVendorDetailsByAssociateId] - No services found for associateId: ${associateId}`
      );
      return res.status(404).send(errorCodeUtils.SERVICE_NOT_FOUND);
    }
    // Fetch the associate details by associateId
    const associate = await Associate.findById(associateId);
    if (!associate) {
      logger.error(
        `[getServicesAndVendorDetailsByAssociateId] - Associate not found for associateId: ${associateId}`
      );
      return res.status(404).send(errorCodeUtils.ASSOCIATE_NOT_FOUND);
    }
    // Pick the most recent vehicle ID from the associate's vehicles
    const vehicleId = associate.vehiclesId[associate.vehiclesId.length - 1];
    // Get Vehicle details to include vehicle brand and model in the response
    const vehicleDetails = await StockVehicle.findById(vehicleId);
    if (!vehicleDetails) {
      logger.error(
        `[getServicesAndVendorDetailsByAssociateId] - Vehicle details not found for vehicleId: ${vehicleId}`
      );
      return res.status(404).send(errorCodeUtils.VEHICLE_DETAILS_NOT_FOUND);
    }
    // If vehicle details are found, include them in the response
    const vehicleInfo = {
      brand: vehicleDetails.brand,
      model: vehicleDetails.model,
    };
    logger.info(
      `[getServicesAndVendorDetailsByAssociateId] - Vehicle details found for vehicleId: ${vehicleId}`
    );
    // Fetch the vehicle delivery date using the stockVehicleService
    const vehicleDeliveryDate: Date = await stockVehicleService.getVehicleDeliveryDate(vehicleId.toString());
    logger.info(
      `[getServicesAndVendorDetailsByAssociateId] - Vehicle delivery date found for vehicleId: ${vehicleId}`
    );
    // If no services are found, return an empty array and organization name because the associate
    // might not have any services registered due to new vehicle.
    if (services.length === 0) {
      logger.info(
        `[getServicesAndVendorDetailsByAssociateId] - No services found for associateId: ${associateId}`
      );
      return res.status(200).send({
        services: [],
        vehicleDeliveryDate: vehicleDeliveryDate.toISOString(),
        vehicleInfo: {
          brand: vehicleInfo.brand,
          model: vehicleInfo.model,
        },
      });
    }
    const organizationIds = new Set<string>();
    // Collect unique organization IDs from the services
    for (const service of services) {
      organizationIds.add(service.organizationId.toString());
    }
    const organizationIdsArray = Array.from(organizationIds);
    // Fetch organizations by the collected IDs
    const organizations = await organizationsService.getMultipleOrganizationsByIds(organizationIdsArray);
    if (!organizations || organizations.length === 0) {
      logger.warn(
        `[getServicesAndVendorDetailsByAssociateId] - No organizations found for associateId: ${associateId}`
      );
      return res.status(404).send(errorCodeUtils.ORGANIZATION_NOT_FOUND);
    }
    // Map services to include organization names and handle cases where organization is not found
    const servicesWithOrganizationName = [];
    for (const service of services) {
      const organizationId = service.organizationId.toString();
      const organization = organizations.find((org) => org._id.toString() === organizationId);
      servicesWithOrganizationName.push({
        ...service.toObject(),
        organizationName: organization ? organization.name : 'Unknown Organization',
      });
    }
    return res.status(200).send({
      services: servicesWithOrganizationName,
      vehicleDeliveryDate: vehicleDeliveryDate.toISOString(),
      vehicleInfo: {
        brand: vehicleInfo.brand,
        model: vehicleInfo.model,
      },
    });
  } catch (error: any) {
    logger.error(
      `[getServicesAndVendorDetailsByAssociateId] - Error fetching services and organization details: ${error.message}`
    );
    if (error.code === errorCodeUtils.VEHICLE_DELIVERY_DATE_NOT_FOUND.code) {
      return res.status(404).send(errorCodeUtils.VEHICLE_DELIVERY_DATE_NOT_FOUND);
    }
    const message = error.message || 'Error fetching services and organization details';
    return res.status(500).send({ code: 'INTERNAL_SERVER_ERROR', status: 500, message });
  }
};
