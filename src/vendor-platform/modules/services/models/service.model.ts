import vendorDB from '@vendor/db';
import { Schema, Types } from 'mongoose';
import { Workshop, IWorkshop } from '../../workshop/models/workshops.model';
import { BucketNameEnum, deleteFileFromS3, getUrlSingleFile } from '@/aws/s3';

interface IService {
  stockId: string;
  associateId: string;
  // object id or string
  organizationId: Types.ObjectId | string;
  workshopId: Types.ObjectId | string;
  workshop: IWorkshop;
  completedAt: Date;
  status: string;
  arrivalKm: number;
  firstStep: {
    bodyWork: string;
    bodyWorkDetail: {
      reviewScratches: {
        dents: boolean;
        scratches: boolean;
        corrosion: boolean;
      };
      paintCondition: string;
      paintConditionImages: string[];
      blows: string;
      blowsImages: string[];
    };
    crystalsAndMirrors: string;
    tires: string;
    lights: string;
    seats: string;
    seatsImages: string[];
    dashboard: string;
    dashboardImages: string[];
    controlSystem: string;
    electronicSystems: string;
    engine: string;
    engineImages: string[];
    transmission: string;
    battery: string;
    batteryImages: string[];
    batteryKW: string;
    brakes: string;
    suspension: string;
    electricalSystem: string;
    exhaustSystem: string;
    refrigerant: string;
    brakeFluid: string;
    powerSteeringFluid: string;
    windshieldWasherFluid: string;
    emergencyTools: string;
    emergencyToolsImages: string[];
    spareWheel: string;
    spareWheelImages: string[];
  };
  secondStep: {
    serviceDetail: {
      serviceType: string;
      specifications: {
        oilChange: boolean;
        oilChangeType: {
          synthetic: boolean;
          semiSynthetic: boolean;
          mineral: boolean;
        };
        oilImages: string[];
        filterChange: boolean;
        // filterType: string;
        filterType: {
          air: boolean;
          oil: boolean;
          // fuel: boolean;
          // cabin: boolean;
        };
        filterImages: string[];
        tuneUp: boolean;
        tuneUpImages: string[];
        batteryChange: boolean;
        brakes: boolean;
        brakesDetail: {
          padChange: boolean;
          discService: boolean;
          fluidChange: boolean;
          fluidType: string;
        };
        suspension: boolean;
        suspensionDetail: {
          shockAbsorbers: boolean;
          springsAndBushings: boolean;
          boltsTightening: boolean;
        };
      };
    };
    pendingServices: {
      nextServiceDate: string;
      nextServiceKm: number;
      criticalComponents: string[];
      recommendations: string[];
    };
    costsAndTimes: {
      totalCost: number;
    };
    signature: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const serviceSchema = new Schema<IService>(
  {
    stockId: { type: String, required: true },
    associateId: { type: String },
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    status: { type: String, default: 'pending' },
    workshopId: { type: Schema.Types.ObjectId, ref: Workshop.modelName, required: true },
    completedAt: { type: Date },
    arrivalKm: { type: Number },
    firstStep: {
      type: {
        bodyWork: String,
        bodyWorkDetail: {
          reviewScratches: {
            dents: Boolean,
            scratches: Boolean,
            corrosion: Boolean,
          },
          paintCondition: String,
          paintConditionImages: [String],
          blows: String,
          blowsImages: [String],
        },
        crystalsAndMirrors: String,
        tires: String,
        lights: String,
        seats: String,
        seatsImages: [String],
        dashboard: String,
        dashboardImages: [String],
        controlSystem: String,
        electronicSystems: String,
        engine: String,
        engineImages: [String],
        transmission: String,
        brakes: String,
        suspension: String,
        electricalSystem: String,
        exhaustSystem: String,
        refrigerant: String,
        brakeFluid: String,
        powerSteeringFluid: String,
        windshieldWasherFluid: String,
        emergencyTools: String,
        emergencyToolsImages: [String],
      },
    },
    secondStep: {
      type: {
        serviceDetail: {
          serviceType: String,
          specifications: {
            oilChange: Boolean,
            oilChangeType: {
              synthetic: Boolean,
              semiSynthetic: Boolean,
              mineral: Boolean,
            },
            oilImages: [String],
            filterChange: Boolean,
            // filterType: String,
            filterType: {
              air: Boolean,
              oil: Boolean,
            },
            filterImages: [String],
            tuneUp: Boolean,
            tuneUpImages: [String],
            batteryChange: Boolean,
            brakes: Boolean,
            brakesDetail: {
              padChange: Boolean,
              discService: Boolean,
              fluidChange: Boolean,
              fluidType: String,
            },
            suspension: Boolean,
            suspensionDetail: {
              shockAbsorbers: Boolean,
              springsAndBushings: Boolean,
              boltsTightening: Boolean,
            },
          },
        },
        pendingServices: {
          nextServiceDate: String,
          nextServiceKm: Number,
          criticalComponents: [String],
          recommendations: [String],
        },
        costsAndTimes: {
          totalCost: Number,
        },
        signature: { type: String },
      },
      default: undefined,
    },
  },
  { timestamps: true }
);

serviceSchema.index({ stockId: 1 });

// Add virtuals to the schema to get the workshop and user data using the references and populate

serviceSchema.virtual('workshop', {
  ref: Workshop.modelName,
  localField: 'workshopId',
  foreignField: '_id',
  justOne: true,
});

serviceSchema.set('toObject', { virtuals: true });
serviceSchema.set('toJSON', { virtuals: true });

// Schema hook on get record or records, to transform the signature field, from S3 Key to URL
serviceSchema.post('find', async (docs: IService[]) => {
  if (docs.length) {
    docs = await Promise.all(
      docs.map(async (doc) => {
        if (doc.secondStep?.signature) {
          doc.secondStep.signature = await getUrlSingleFile(
            doc.secondStep.signature,
            BucketNameEnum.VENDOR_PLATFORM
          );
        }
        return doc;
      })
    );
  }
});

serviceSchema.post('findOne', async (doc: IService) => {
  if (doc?.secondStep?.signature) {
    doc.secondStep.signature = await getUrlSingleFile(
      doc.secondStep.signature,
      BucketNameEnum.VENDOR_PLATFORM
    );
  }
});

// Delete file from S3 when a service is deleted

serviceSchema.post('findOneAndDelete', async (doc: IService) => {
  if (doc?.secondStep?.signature) {
    await deleteFileFromS3(doc.secondStep.signature, BucketNameEnum.VENDOR_PLATFORM);
  }
});

serviceSchema.post('deleteMany', async (docs: IService[]) => {
  if (docs.length) {
    await Promise.all(
      docs.map(async (doc) => {
        if (doc?.secondStep?.signature) {
          await deleteFileFromS3(doc.secondStep.signature, BucketNameEnum.VENDOR_PLATFORM);
        }
      })
    );
  }
});
/* CHANGE COMMENTS TO ENGLISH: */
// Middleware to process nextServiceKm before saving
serviceSchema.pre('save', function (next) {
  if (this.isModified('secondStep.pendingServices.nextServiceKm')) {
    const nextServiceKm = this.secondStep?.pendingServices?.nextServiceKm;

    if (nextServiceKm !== undefined && nextServiceKm !== null) {
      // Convert to string to manipulate
      let kmValue = nextServiceKm.toString();

      // Verify if it's already in thousands (has 4 or more digits)
      if (kmValue.length < 4) {
        // If it's less than 4 digits, add the necessary zeros
        this.secondStep.pendingServices.nextServiceKm = parseInt(kmValue + '000');
      } else {
        // If it's already in thousands, just parse it to int
        this.secondStep.pendingServices.nextServiceKm = parseInt(kmValue);
      }
    }
  }
  next();
});

// Middleware to process nextServiceKm before updating
serviceSchema.pre(['updateOne', 'findOneAndUpdate'], function (next) {
  const update = this.getUpdate() as any;

  if (update && update.$set && update.$set['secondStep.pendingServices.nextServiceKm'] !== undefined) {
    let nextServiceKm = update.$set['secondStep.pendingServices.nextServiceKm'];

    if (nextServiceKm !== undefined && nextServiceKm !== null) {
      // Convert to string to manipulate
      let kmValue = nextServiceKm.toString();

      // If it's less than 4 digits, add the necessary zeros
      if (kmValue.length < 4) {
        // If it's less than 4 digits, add the necessary zeros
        update.$set['secondStep.pendingServices.nextServiceKm'] = parseInt(kmValue + '000');
      } else {
        // If it's already in thousands, just parse it to int
        update.$set['secondStep.pendingServices.nextServiceKm'] = parseInt(kmValue);
      }
    }
  }

  // Also handle the case where secondStep.pendingServices is updated as a complete object
  if (update && update.$set && update.$set['secondStep.pendingServices'] !== undefined) {
    const pendingServices = update.$set['secondStep.pendingServices'];

    if (pendingServices && pendingServices.nextServiceKm !== undefined) {
      let nextServiceKm = pendingServices.nextServiceKm;

      if (nextServiceKm !== undefined && nextServiceKm !== null) {
        // Convert to string to manipulate
        let kmValue = nextServiceKm.toString();

        // If it's less than 4 digits, add the necessary zeros
        if (kmValue.length < 4) {
          // If it's less than 4 digits, add the necessary zeros
          pendingServices.nextServiceKm = parseInt(kmValue + '000');
        } else {
          // If it's already in thousands, just parse it to int
          pendingServices.nextServiceKm = parseInt(kmValue);
        }
      }
    }
  }

  // Handle the case where the whole secondStep is updated
  if (update && update.$set && update.$set.secondStep !== undefined) {
    const secondStep = update.$set.secondStep;

    if (secondStep && secondStep.pendingServices && secondStep.pendingServices.nextServiceKm !== undefined) {
      let nextServiceKm = secondStep.pendingServices.nextServiceKm;

      if (nextServiceKm !== undefined && nextServiceKm !== null) {
        // Convert to string to manipulate
        let kmValue = nextServiceKm.toString();

        // Verify if it's already in thousands (has 4 or more digits)
        if (kmValue.length < 4) {
          // If it's less than 4 digits, add the necessary zeros
          secondStep.pendingServices.nextServiceKm = parseInt(kmValue + '000');
        } else {
          // If it's already in thousands, just parse it to int
          secondStep.pendingServices.nextServiceKm = parseInt(kmValue);
        }
      }
    }
  }

  next();
});

// Middleware to process nextServiceKm before inserting many
serviceSchema.pre('insertMany', function (next, docs) {
  if (Array.isArray(docs)) {
    docs.forEach((doc) => {
      if (
        doc.secondStep &&
        doc.secondStep.pendingServices &&
        doc.secondStep.pendingServices.nextServiceKm !== undefined
      ) {
        let nextServiceKm = doc.secondStep.pendingServices.nextServiceKm;

        if (nextServiceKm !== undefined && nextServiceKm !== null) {
          // Convert to string to manipulate
          let kmValue = nextServiceKm.toString();

          // Verify if it's already in thousands (has 4 or more digits)
          if (kmValue.length < 4) {
            // If it's less than 4 digits, add the necessary zeros
            doc.secondStep.pendingServices.nextServiceKm = parseInt(kmValue + '000');
          } else {
            // If it's already in thousands, just parse it to int
            doc.secondStep.pendingServices.nextServiceKm = parseInt(kmValue);
          }
        }
      }
    });
  }
  next();
});

const ServiceVendorModel = vendorDB.model<IService>('Service', serviceSchema);

export default ServiceVendorModel;
