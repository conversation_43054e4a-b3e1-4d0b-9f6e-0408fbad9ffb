/* eslint-disable prettier/prettier */
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { CreateOrgDto } from '../dtos/create-org.dto';
import OrganizationModel from '../models/organization.model';
import { Workshop } from '../../workshop/models/workshops.model';
import { sendInvitationEmail } from '@/middlewares/email';
import { genericMessages, invitationSecret } from '@/constants';
import jwt from 'jsonwebtoken';
import UserVendorModel, { UserType } from '@/vendor-platform/modules/users/models/user.model';
import { Types } from 'mongoose';
import ExpiredToken from '@/models/expiredTokens';
import { addAndInvalidateTokenDB } from '@/conf/blackList';
import { removeEmptySpacesNameFile } from '@/services/removeEmptySpaces';
import Document from '@/models/documentSchema';
import bcrypt from 'bcrypt';
import { isAdminOrSuper } from '@/vendor-platform/utils/roles-validation';
import CompanyUserPermissions, { CompanyUserRole } from '../../company/models/company-user-permissions.model';
import { CompanyVendorModel } from '../../company/models/company.model';
import { CompanyPermissionsService } from '../../company/services/company-permissions.service';

type InviteUserToVendor = {
  email: string;
  organizationId: string | undefined;
  name: string;
  resend: boolean;
  originUrl: string;
  role: string;
  userType?: UserType;
};

export class OrganizationsService {
  async createOrganization(data: CreateOrgDto, role: string) {
    if (!isAdminOrSuper(role)) {
      throw HttpException.Unauthorized("You don't have permission to create an organization");
    }

    const newOrganization = new OrganizationModel({
      ...data,
    });
    return newOrganization.save();
  }

  // Invites a user to a vendor
  async inviteUserToVendor({
    email,
    organizationId,
    name,
    resend,
    originUrl,
    role,
    userType = 'workshop',
  }: InviteUserToVendor) {
    this.validateAdminRole(role);

    if (!email) throw new Error('Email is required');
    if (userType !== 'company' && !organizationId) throw new Error('organizationId is required');

    const invitationToken = this.generateInvitationToken(email);

    if (resend) {
      const user = await this.reinviteExistingUser(email, originUrl, invitationToken);
      return { message: 'User invited successfully', data: user };
    }

    await this.createAndInviteNewUser({
      email,
      organizationId,
      name,
      originUrl,
      invitationToken,
      userType,
    });
    return { message: 'User invited successfully' };
  }

  getTextsOfInvitation(language: string, name: string, userType: UserType = 'workshop') {
    const texts: { [key: string]: { title: string; subject: string; message: string } } = {
      mx: {
        title: 'Invitación a la Plataforma de Taller de OCN',
        subject: 'Has sido invitado a unirte a la Plataforma de Taller de OCN',
        message: `Hola ${name}, has sido invitado a unirte a la Plataforma de Taller de OCN! Tiene 48 horas para aceptar la invitación.`,
      },
      us: {
        title: 'Invitation to the OCN Workshop Platform',
        subject: 'You have been invited to join the OCN Workshop Platform',
        message: `Hello ${name}, you have been invited to join the OCN Workshop Platform! You have 48 hours to accept the invitation.`,
      },
    };

    if (userType === 'company') {
      // Override the texts for company invitations
      texts.mx.title = 'Invitación a la Plataforma de Compañía de OCN';
      texts.mx.subject = 'Has sido invitado a unirte a la Plataforma de Compañía de OCN';
      texts.mx.message = `Hola ${name}, has sido invitado a unirte a la Plataforma de Compañía de OCN! Tiene 48 horas para aceptar la invitación.`;

      texts.us.title = 'Invitation to the OCN Company Platform';
      texts.us.subject = 'You have been invited to join the OCN Company Platform';
      texts.us.message = `Hello ${name}, you have been invited to join the OCN Company Platform! You have 48 hours to accept the invitation.`;
    }

    return texts[language] || texts.mx;
  }

  // Accepts a vendor invitation
  async acceptVendorInvitation({
    code,
    password,
    image,
  }: {
    code: string;
    password: string;
    image?: Express.Multer.File;
  }): Promise<any> {
    if (!code) throw new Error('Token not found');
    jwt.verify(code, invitationSecret);

    await this.checkIfTokenIsExpired(code);

    const decodedToken = jwt.decode(code) as jwt.JwtPayload;
    if (!password) throw new Error(genericMessages.errors.users.missingPassword);

    const user = await this.findUserByEmail(decodedToken.email);
    await this.updateUserWithPasswordAndImage(user, password, image);

    await CompanyPermissionsService.updateUserPermissionStatus(
      user._id.toString(),
      'active'
    );

    return { message: genericMessages.success.users.created };
  }

  // Private helper methods

  private validateAdminRole(role: string): void {
    if (!isAdminOrSuper(role)) {
      throw new Error('You are not authorized to perform this action');
    }
  }

  generateInvitationToken(email: string): string {
    return jwt.sign({ email }, invitationSecret, { expiresIn: '48h' });
  }

  private async reinviteExistingUser(email: string, originUrl: string, token: string): Promise<any> {
    const user = await UserVendorModel.findOne({ email });
    if (!user) throw new Error('User not found');

    user.status = 'invited';

    const text = this.getTextsOfInvitation(user.language, user.name!, user.userType);

    const fullUrl = `${originUrl}/${user.language}/accept-invitation?code=${token}`;
    await sendInvitationEmail({
      title: text.title,
      subject: text.subject,
      message: text.message,
      email,
      token,
      baseUrl: originUrl + '/' + user.language,
      fullUrl,
    });

    await user.save();
    return user;
  }

  private async createAndInviteNewUser({
    email,
    organizationId,
    name,
    originUrl,
    invitationToken,
    userType,
  }: {
    email: string;
      organizationId: string | undefined;
    name: string;
    originUrl: string;
    invitationToken: string;
      userType: UserType;
  }): Promise<void> {
    // const validId = Types.ObjectId.isValid(organizationId);
    // if (!validId) throw new Error('Invalid organizationId');

    // const vendorExists = await OrganizationModel.findById(organizationId);
    // if (!vendorExists) throw new Error('Vendor not found');

    // Check the user type to validate if we need to validate to find an organization
    if (userType === 'workshop') {

      const validId = Types.ObjectId.isValid(organizationId!);
      if (!validId) throw new Error('Invalid organizationId');

      const vendorExists = await OrganizationModel.findById(organizationId);
      if (!vendorExists) throw new Error('Vendor not found');
    }


    const newUser = new UserVendorModel({
      email,
      organizationId,
      name,
      status: 'invited',
      userType, // Agregando el tipo de usuario
    });
    await newUser.save();

    // Si es usuario de tipo company, crear permisos de owner
    if (userType === 'company' || userType === 'company-gestor' || userType === 'superAdmin') {
      const company = new CompanyVendorModel({
        name: `${name}'s Company (Temporary Name)`, // Nombre temporal
        // email,
        phone: '', // Campos requeridos con valores temporales
        address: '',
      });
      await company.save();

      // Crear permisos de owner
      const permissions = new CompanyUserPermissions({
        userId: newUser._id,
        companyId: company._id,
        role: CompanyUserRole.OWNER,
        status: 'invited',
      });
      await permissions.save();
    }

    const fullUrl = `${originUrl}/${newUser.language}/accept-invitation?code=${invitationToken}&uT=${userType}`;

    const text = this.getTextsOfInvitation(newUser.language, newUser.name!, userType);

    await sendInvitationEmail({
      title: text.title,
      subject: text.subject,
      message: text.message,
      email,
      token: invitationToken,
      baseUrl: originUrl + '/' + newUser.language,
      fullUrl,
    });

  }

  private async checkIfTokenIsExpired(token: string): Promise<void> {
    const tokenDB = await ExpiredToken.findOne({ token });
    if (tokenDB) {
      throw new Error('El token ya ha sido invalidado anteriormente');
    }
    addAndInvalidateTokenDB(token);
  }

  private async findUserByEmail(email: string): Promise<any> {
    const user = await UserVendorModel.findOne({ email });
    if (!user) throw new Error(genericMessages.errors.users.notFound);
    return user;
  }

  private async updateUserWithPasswordAndImage(
    user: any,
    password: string,
    image?: Express.Multer.File
  ): Promise<void> {
    const hashedPassword = await bcrypt.hash(password, 12);
    user.password = hashedPassword;
    user.isVerified = true;

    if (image) {
      const removeSpace = removeEmptySpacesNameFile(image);
      const imageDoc = new Document({
        path: `users/${user._id}/profile/${removeSpace}`,
        userId: user._id,
        originalName: removeSpace,
      });
      await imageDoc.save();
      user.image = imageDoc._id;
    }
    await user.save();
  }

  async getOrganizations() {
    return OrganizationModel.find();
  }

  async getOrganizationById(id: string) {
    return OrganizationModel.findById(id);
  }

  async getMultipleOrganizationsByIds(ids: string[]) {
    return OrganizationModel.find({ _id: { $in: ids } });
  }

  async getOrganizationWorkshops(id: string) {
    return Workshop.find({ organizationId: id });
  }
}
export const organizationsService = new OrganizationsService();
