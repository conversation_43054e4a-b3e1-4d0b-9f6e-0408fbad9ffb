import { logger } from '@/clean/lib/logger';
import { fcmNotificationService } from '@/modules/FirebaseCloudMessaging/services/fcmNotification.service';
import { AppointmentVendorStatus } from '../models/appointment.model';

/**
 * Icon to UTF-16 encoding (emoji)
 * :spanner: -> \uD83D\uDD27
 * :date: -> \uD83D\uDCC5
 * :alarm_clock: -> \u23F0
 * :point_right: -> \uD83D\uDC49
 * :white_tick: -> \u2705
 * :sunny: -> \u2600
 * :gear: -> \u2699\uFE0F
 * :warning: -> \u26A0\uFE0F
 * :no_entry_sign: -> \uD83D\uDEAB
 * :sparkles: -> \u2728
 * :smile: -> \uD83D\uDE00
 */

export interface VehicleMaintenanceScheduledOrRescheduledNotification {
  userId: string;
  date: string;
  time: string;
  rescheduleAppointmentLink: string;
  workshopName: string;
  workshopLocation: string;
  action: AppointmentVendorStatus.scheduled | AppointmentVendorStatus.rescheduled;
}

const dataTypeOperation = 'vehicle_maintenance';

// Send notification on OCN Mobile application about vehicle maintenance appointment scheduled
export async function sendVehicleMaintenanceAppointmentScheduledFCMNotification({
  userId,
  date,
  time,
  rescheduleAppointmentLink,
  workshopName,
  workshopLocation,
  action,
}: VehicleMaintenanceScheduledOrRescheduledNotification): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Mantenimiento de Vehículo',
      body: `¡Tu cita de servicio de mantenimiento ha sido confirmada! \uD83D\uDD27\nNos complace informarte que tu cita de mantenimiento ha sido agendada con éxito.\n\uD83D\uDCC5 Fecha: ${date}\n\u23F0 Hora: ${time}\n\u2699\uFE0F Taller: ${workshopName}\n\u26A0\uFE0F Importante: Si necesitas reprogramar o cancelar tu cita debes realizalo mediante el siguiente enlace\n¡Gracias por confiar en nosotros! Nos aseguraremos de brindarte el mejor servicio.`,
      data: {
        type: JSON.stringify({
          operation: dataTypeOperation,
          action,
        }),
        workshopLocation,
        rescheduleAppointmentLink,
      },
    });
    logger.info(
      `[sendFCMVehicleMaintenanceAppointmentScheduledNotification] - Notification sent about vehicle maintenance appointment creation to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendFCMVehicleMaintenanceAppointmentScheduledNotification] - Error sending notification about vehicle maintenance appointment to userId ${userId}`,
      error
    );
  }
}

// Send notification on OCN Mobile application about vehicle maintenance appointment cancellation
export async function sendVehicleMaintenanceAppointmentCancelledFCMNotification({
  userId,
}: {
  userId: string;
}): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Mantenimiento de Vehículo',
      body: `¡Hola! Notamos que cancelaste el mantenimiento que habías programado. Esperamos que te encuentres bien.\n\uD83D\uDD27 Recuerda que darle mantenimiento a tu auto es muy importante para mantenerlo en excelentes condiciones.\n¡Estamos aquí para ayudarte!`,
      data: {
        type: JSON.stringify({
          operation: dataTypeOperation,
          action: AppointmentVendorStatus.canceled,
        }),
      },
    });
    logger.info(
      `[sendVehicleMaintenanceAppointmentCancelledFCMNotification] - Notification sent to userId ${userId} about vehicle maintenance appointment cancellation`
    );
  } catch (error) {
    logger.error(
      `[sendVehicleMaintenanceAppointmentCancelledFCMNotification] - Error sending notification to userId ${userId} about vehicle maintenance appointment cancellation`,
      error
    );
  }
}

// Send notification on OCN Mobile application about vehicle maintenance appointment missed
export async function sendVehicleMaintenanceAppointmentMissedFCMNotification({
  userId,
  scheduleAppointmentLink,
}: {
  userId: string;
  scheduleAppointmentLink: string;
}): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Mantenimiento de Vehículo',
      body: `\uD83D\uDEAB Cita cancelada\nLamentablemente tu cita fue cancelada porque no te presentaste en la fecha y hora programadas.\n\n\uD83D\uDCC5 Puedes reagendar fàcilmente a través del siguiente enlace\n\n\u2728 Además, puedes usar este mismo enlace en cualquier momento para programar tu cita y evitar futuras inasistencias.\n\n\u26A0\uFE0F Recuerda que si no asistes nuevamente, se aplicará una penalización según nuestras políticas.\n\n¡Gracias por tu comprensión!\uD83D\uDE00`,
      data: {
        type: JSON.stringify({
          operation: dataTypeOperation,
          action: AppointmentVendorStatus['not-attended'],
        }),
        scheduleAppointmentLink,
      },
    });
    logger.info(
      `[sendVehicleMaintenanceAppointmentMissedFCMNotification] - Notification sent of vehicle maintenance appointment missed to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendVehicleMaintenanceAppointmentMissedFCMNotification] - Error sending notification about vehicle maintenance appointment missed to userId ${userId}`,
      error
    );
  }
}

// Send notification on OCN Mobile application about vehicle maintenance appointment confirmation
export async function sendVehicleMaintenanceAppointmentCompletedFCMNotification({
  userId,
}: {
  userId: string;
}): Promise<void> {
  try {
    await fcmNotificationService.sendNotificationToAssociateById(userId, {
      title: 'Mantenimiento exitoso',
      body: '¡Hola!\nEl mantenimiento de tu auto se ha realizado con éxito. \u2705\n¡Gracias por tu tiempo!\nTe deseamos un excelente día \u2600',
      data: {
        type: JSON.stringify({
          operation: dataTypeOperation,
          action: AppointmentVendorStatus.completed,
        }),
      },
    });
    logger.info(
      `[sendVehicleMaintenanceAppointmentCompletedFCMNotification] - Notification sent of vehicle maintenance appointment confirmation to userId ${userId}`
    );
  } catch (error) {
    logger.error(
      `[sendVehicleMaintenanceAppointmentCompletedFCMNotification] - Error sending notification of vehicle maintenance appointment confirmation to userId ${userId}`,
      error
    );
  }
}
