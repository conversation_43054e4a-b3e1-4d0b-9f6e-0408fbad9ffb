import { logger } from '@/clean/lib/logger';
import { HILOS_API_KEY, HILOS_URL_SEND_TEMPLATE } from '@/constants';
import axios from 'axios';

export async function sendAppointmentNotificationMessage({
  associateName,
  associatePhone,
  date,
  time,
  workshopName,
  mapsLink,
}: {
  associateName: string;
  associatePhone: string;
  date: string;
  time: string;
  workshopName: string;
  mapsLink: string;
}) {
  mapsLink = mapsLink?.replace('https://www.google.com/', '') || '';

  try {
    const response = await axios.post(
      // `${HILOS_URL_SEND_TEMPLATE}/067eed46-42cb-7297-8000-16acb394b031/send`,
      // Old one: 0681bdc9-dc3b-7fdf-8000-cd95e1db9335
      // New one: 06862f65-2729-7266-8000-7dd4fbd2b4b6
      // Template name: cita_mantenimeinto_vendors_reagendar
      `${HILOS_URL_SEND_TEMPLATE}/06862f65-2729-7266-8000-7dd4fbd2b4b6/send`,
      {
        variables: [
          `https://hilos.io/api/file/p/06792926-8487-7d1d-8000-7595527fe1f9`,
          associateName,
          date,
          time,
          workshopName,
          mapsLink,
        ],
        phone: associatePhone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const { data } = response;
    console.log('sendAppointmentNotificationMessage data response: ', data);
    logger.info(`[sendAppointmentNotificationMessage] Message sent to Hilos: ${JSON.stringify(data)}`);
  } catch (error: any) {
    console.error('Error sending appointment notification', error.response?.data, error.message);
    logger.error(
      `[sendAppointmentNotificationMessage] Error sending appointment notification: ${error.message}`
    );
  }
}

export async function sendNotAttendedNotification({
  // text,
  // appointmentId,
  phone,
}: {
  text: string;
  appointmentId: string;
  phone: string;
}) {
  try {
    const { data } = await axios.post(
      // `${HILOS_URL_SEND_TEMPLATE}/067eed4b-7580-726d-8000-ae0a0e8be930/send`,
      // 0681cdc0-c3d1-7e9b-8000-32d420d953dd
      `${HILOS_URL_SEND_TEMPLATE}/0681cdc0-c3d1-7e9b-8000-32d420d953dd/send`,
      {
        // variables: [text, appointmentId],
        phone,
      },
      {
        headers: {
          Authorization: `Token ${HILOS_API_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('[sendNotAssitedNotification] response: ', data);
  } catch (error: any) {
    console.error('Error sending not assited notification', error.response?.data, error.message);
  }
}
