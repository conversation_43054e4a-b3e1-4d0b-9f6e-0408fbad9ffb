import { errorHandlerV2 } from '../../../../clean/errors/errorHandler';
import { getServicesByStockId } from '@/vendor-platform/modules/services/controllers/services.controller';
import { getVehicleDataById, getVehiclesVendor } from '../controllers/vehicles.controller';
import { Router } from 'express';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import {
  confirmQrStatusChange,
  getVehicleQRScanHistory,
  initiateQrScanAction,
  uploadQrScanPhoto,
} from '@/controllers/stockVehicles';
import { upload } from '@/multer/multer';

const vehiclesRouter = Router();

const vehiclesUrl = '/vehicles';

vehiclesRouter.get(vehiclesUrl, verifyTokenVendorPlatform, errorHandlerV2(getVehiclesVendor));
vehiclesRouter.get(`${vehiclesUrl}/:stockId`, verifyTokenVendorPlatform, errorHandlerV2(getVehicleDataById));
vehiclesRouter.get(
  `${vehiclesUrl}/:stockId/services`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServicesByStockId)
);
vehiclesRouter.post(
  `${vehiclesUrl}/:vehicleId/initiate-qr-scan-action`,
  verifyTokenVendorPlatform,
  errorHandlerV2(initiateQrScanAction)
);

vehiclesRouter.post(
  `${vehiclesUrl}/:vehicleId/confirm-qr-status-change`,
  verifyTokenVendorPlatform,
  errorHandlerV2(confirmQrStatusChange)
);

vehiclesRouter.post(
  `${vehiclesUrl}/:vehicleId/upload-qr-photo`,
  verifyTokenVendorPlatform,
  upload.single('file'),
  errorHandlerV2(uploadQrScanPhoto)
);

vehiclesRouter.get(
  `${vehiclesUrl}/:vehicleId/qr-scan-history`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getVehicleQRScanHistory)
);

export default vehiclesRouter;
