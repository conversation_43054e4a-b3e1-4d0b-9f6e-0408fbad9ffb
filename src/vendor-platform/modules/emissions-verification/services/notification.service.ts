import { EmissionsVerificationService } from './emissions-verification.service';
import { VerificationLogicService } from './verification-logic.service';
import { EmissionsVerification } from '../models/emissions-verification.model';

export interface NotificationData {
  vehiclePlate: string;
  nextVerificationDate: Date;
  daysRemaining: number;
  verificationCenter?: string;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
}

export class NotificationService {
  /**
   * Envía recordatorio al customer cuando se acerca la verificación
   */
  static async sendCustomerReminder(verification: any): Promise<boolean> {
    try {
      const daysRemaining = Math.ceil(
        (verification.nextVerificationDate.getTime() - new Date().getTime()) / (1000 * 3600 * 24)
      );

      const notificationData: NotificationData = {
        vehiclePlate: verification.vehiclePlate,
        nextVerificationDate: verification.nextVerificationDate,
        daysRemaining,
        verificationCenter: verification.verificationCenter?.name,
      };

      // Aquí se integraría con el sistema de notificaciones existente
      // Por ejemplo: WhatsApp, Email, SMS, etc.
      console.log(`Sending customer reminder for vehicle ${verification.vehiclePlate}:`, notificationData);

      // Simular envío exitoso
      await EmissionsVerificationService.markReminderSent(verification._id);

      return true;
    } catch (error) {
      console.error('Error sending customer reminder:', error);
      return false;
    }
  }

  /**
   * Envía notificación a la flota para vencimientos del mes siguiente
   */
  static async sendFleetReminder(verifications: any[]): Promise<boolean> {
    try {
      const fleetData = verifications.map((v) => ({
        vehiclePlate: v.vehiclePlate,
        nextVerificationDate: v.nextVerificationDate,
        verificationCenter: v.verificationCenter?.name,
        status: v.status,
      }));

      console.log(`Sending fleet reminder for ${verifications.length} vehicles:`, fleetData);

      // Aquí se integraría con el sistema de notificaciones para fleets
      // Podría ser un reporte mensual, dashboard, etc.

      return true;
    } catch (error) {
      console.error('Error sending fleet reminder:', error);
      return false;
    }
  }

  /**
   * Envía recordatorio diario al customer que no ha subido evidencias
   */
  static async sendDailyCustomerReminder(verification: any): Promise<boolean> {
    try {
      const daysSinceVerification = Math.ceil(
        (new Date().getTime() - verification.verificationDate.getTime()) / (1000 * 3600 * 24)
      );

      console.log(
        `Daily reminder for vehicle ${verification.vehiclePlate} - ${daysSinceVerification} days since verification`
      );

      // Aquí se enviaría el recordatorio diario
      // Incrementar contador de recordatorios
      await EmissionsVerification.findByIdAndUpdate(verification._id, {
        $inc: { remindersSent: 1 },
        lastReminderSent: new Date(),
      });

      return true;
    } catch (error) {
      console.error('Error sending daily customer reminder:', error);
      return false;
    }
  }

  /**
   * Proceso automático para enviar recordatorios
   */
  static async processReminders(): Promise<void> {
    try {
      // 1. Recordatorios para customers que se acerca la verificación (30 días)
      const upcomingVerifications = await EmissionsVerificationService.getVerificationsNeedingReminder(30);

      for (const verification of upcomingVerifications) {
        if (VerificationLogicService.needsVerificationSoon(verification.nextVerificationDate, 30)) {
          await this.sendCustomerReminder(verification);
        }
      }

      // 2. Recordatorios diarios para customers que no han subido evidencias
      const pendingCustomerVerifications = await EmissionsVerification.find({
        status: 'pending_customer',
        createdAt: { $lte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Más de 1 día
      }).populate('verificationCenter');

      for (const verification of pendingCustomerVerifications) {
        // Enviar recordatorio máximo cada 24 horas
        const lastReminder = verification.lastReminderSent;
        if (!lastReminder || new Date().getTime() - lastReminder.getTime() >= 24 * 60 * 60 * 1000) {
          await this.sendDailyCustomerReminder(verification);
        }
      }

      // 3. Recordatorio mensual para fleets (primer día del mes)
      const today = new Date();
      if (today.getDate() === 1) {
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
        const endOfNextMonth = new Date(today.getFullYear(), today.getMonth() + 2, 0);

        const nextMonthVerifications = await EmissionsVerification.find({
          status: 'completed',
          nextVerificationDate: {
            $gte: nextMonth,
            $lte: endOfNextMonth,
          },
        }).populate('verificationCenter');

        if (nextMonthVerifications.length > 0) {
          await this.sendFleetReminder(nextMonthVerifications);
        }
      }

      console.log('Reminder processing completed successfully');
    } catch (error) {
      console.error('Error processing reminders:', error);
    }
  }

  /**
   * Configurar cron job para recordatorios automáticos
   */
  static setupAutomaticReminders(): void {
    // Este método se llamaría desde el main app para configurar el cron job
    // Ejemplo usando node-cron:
    /*
    const cron = require('node-cron');
    
    // Ejecutar todos los días a las 9:00 AM
    cron.schedule('0 9 * * *', () => {
      console.log('Running automatic reminders...');
      this.processReminders();
    });
    */

    console.log('Automatic reminders system configured');
  }

  /**
   * Envía notificación cuando customer completa el proceso
   */
  static async notifyVerificationCompleted(verification: any): Promise<boolean> {
    try {
      console.log(`Verification completed for vehicle ${verification.vehiclePlate}`);
      console.log(`Next verification due: ${verification.nextVerificationDate}`);

      // Aquí se podría enviar confirmación al customer
      // y notificación al centro de verificación

      return true;
    } catch (error) {
      console.error('Error sending completion notification:', error);
      return false;
    }
  }
}
