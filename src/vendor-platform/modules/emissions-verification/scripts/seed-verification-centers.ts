import { VerificationCenter } from '../models/verification-center.model';

interface SeedVerificationCenter {
  name: string;
  code: string;
  state: string;
  organizationId: string; // Se debe reemplazar con un ID real
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
  };
  isActive: boolean;
  authorizedFor: string[];
}

const verificationCenters: SeedVerificationCenter[] = [
  {
    name: 'Verificentro MH01 - Centro',
    code: 'MH01',
    state: 'CDMX',
    organizationId: '507f1f77bcf86cd799439011', // ID temporal - reemplazar con real
    location: {
      address: 'Av. Insurgentes Sur 1234, Col. Roma Norte',
      city: 'Ciudad de México',
      state: 'CDMX',
      country: 'mx',
    },
    isActive: true,
    authorizedFor: ['gasoline', 'diesel', 'hybrid'],
  },
  {
    name: 'Verificentro MH02 - Norte',
    code: 'MH02',
    state: 'CDMX',
    organizationId: '507f1f77bcf86cd799439011',
    location: {
      address: 'Av. <PERSON> 567, Col. Lindavista',
      city: 'Ciudad de México',
      state: 'CDMX',
      country: 'mx',
    },
    isActive: true,
    authorizedFor: ['gasoline', 'diesel'],
  },
  {
    name: 'Verificentro EM01 - Estado de México',
    code: 'EM01',
    state: 'EDOMEX',
    organizationId: '507f1f77bcf86cd799439011',
    location: {
      address: 'Blvd. Adolfo López Mateos 890, Col. Valle Dorado',
      city: 'Tlalnepantla de Baz',
      state: 'EDOMEX',
      country: 'mx',
    },
    isActive: true,
    authorizedFor: ['gasoline', 'diesel', 'hybrid', 'motorcycle'],
  },
  {
    name: 'Verificentro QR01 - Querétaro',
    code: 'QR01',
    state: 'QUERETARO',
    organizationId: '507f1f77bcf86cd799439011',
    location: {
      address: 'Av. 5 de Febrero 456, Col. Centro',
      city: 'Querétaro',
      state: 'QUERETARO',
      country: 'mx',
    },
    isActive: true,
    authorizedFor: ['gasoline', 'diesel'],
  },
  {
    name: 'Verificentro GJ01 - Guadalajara',
    code: 'GJ01',
    state: 'JALISCO',
    organizationId: '507f1f77bcf86cd799439011',
    location: {
      address: 'Av. López Mateos Sur 2345, Col. Chapalita',
      city: 'Guadalajara',
      state: 'JALISCO',
      country: 'mx',
    },
    isActive: true,
    authorizedFor: ['gasoline', 'diesel', 'hybrid'],
  },
];

export async function seedVerificationCenters(): Promise<void> {
  try {
    console.log('🌱 Iniciando seed de centros de verificación...');

    // Verificar si ya existen centros
    const existingCenters = await VerificationCenter.countDocuments();

    if (existingCenters > 0) {
      console.log(`⚠️  Ya existen ${existingCenters} centros de verificación. Saltando seed.`);
      return;
    }

    // Crear centros de verificación
    for (const centerData of verificationCenters) {
      try {
        const center = new VerificationCenter(centerData);
        await center.save();
        console.log(`✅ Centro creado: ${centerData.code} - ${centerData.name}`);
      } catch (error: any) {
        if (error.code === 11000) {
          console.log(`⚠️  Centro ${centerData.code} ya existe, saltando...`);
        } else {
          console.error(`❌ Error creando centro ${centerData.code}:`, error.message);
        }
      }
    }

    console.log('🎉 Seed de centros de verificación completado!');
  } catch (error: any) {
    console.error('❌ Error en seed de centros de verificación:', error.message);
    throw error;
  }
}

// Función para limpiar todos los centros (usar con cuidado)
export async function clearVerificationCenters(): Promise<void> {
  try {
    console.log('🧹 Limpiando centros de verificación...');
    const result = await VerificationCenter.deleteMany({});
    console.log(`🗑️  ${result.deletedCount} centros eliminados`);
  } catch (error: any) {
    console.error('❌ Error limpiando centros:', error.message);
    throw error;
  }
}

// Función para obtener estadísticas de centros
export async function getVerificationCentersStats(): Promise<void> {
  try {
    const total = await VerificationCenter.countDocuments();
    const active = await VerificationCenter.countDocuments({ isActive: true });
    const inactive = total - active;

    const byState = await VerificationCenter.aggregate([
      { $group: { _id: '$state', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    console.log('📊 Estadísticas de Centros de Verificación:');
    console.log(`   Total: ${total}`);
    console.log(`   Activos: ${active}`);
    console.log(`   Inactivos: ${inactive}`);
    console.log('   Por estado:');
    byState.forEach((state) => {
      console.log(`     ${state._id}: ${state.count}`);
    });
  } catch (error: any) {
    console.error('❌ Error obteniendo estadísticas:', error.message);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  console.log('🚀 Ejecutando seed de centros de verificación...');
  seedVerificationCenters()
    .then(() => {
      console.log('✅ Seed completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error en seed:', error);
      process.exit(1);
    });
}
