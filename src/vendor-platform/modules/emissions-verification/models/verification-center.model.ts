import mongoose, { Schema, Document } from 'mongoose';
import vendorDB from '@/vendor-platform/db';
import { VehicleStateType } from '@/models/StockVehicleSchema';

export interface IVerificationCenter extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  code: string; // Código único del verificentro (ej: MH01)
  state: VehicleStateType; // CDMX, EDOMEX, etc.
  organizationId: mongoose.Types.ObjectId; // Empresa que maneja el verificentro
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  isActive: boolean;
  authorizedFor: string[]; // Tipos de verificación autorizados
  createdAt: Date;
  updatedAt: Date;
}

const VerificationCenterSchema = new Schema<IVerificationCenter>(
  {
    name: { type: String, required: true },
    code: { type: String, required: true, unique: true },
    state: { type: String, required: true },
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    location: {
      address: { type: String, required: true },
      city: { type: String, required: true },
      state: { type: String, required: true },
      country: { type: String, default: 'mx' },
      coordinates: {
        lat: { type: Number },
        lng: { type: Number },
      },
    },
    isActive: { type: Boolean, default: true },
    authorizedFor: [{ type: String }], // ['gasoline', 'diesel', 'hybrid', 'motorcycle']
  },
  {
    timestamps: true,
  }
);

VerificationCenterSchema.index({ code: 1 }, { unique: true });
VerificationCenterSchema.index({ state: 1, isActive: 1 });

export const VerificationCenter = vendorDB.model<IVerificationCenter>(
  'VerificationCenter',
  VerificationCenterSchema
);
