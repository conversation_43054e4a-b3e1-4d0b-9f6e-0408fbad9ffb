import { Request, Response } from 'express';
import { EmissionsVerificationService } from '../services/emissions-verification.service';
import '../types/request.types'; // Importar tipos globales
//import { VerificationCenter } from '../models/verification-center.model';

export class VendorVerificationController {
  /**
   * Buscar vehículo por placa
   */
  static async searchVehicle(req: Request, res: Response) {
    try {
      const { plate } = req.params;

      if (!plate) {
        return res.status(400).json({
          success: false,
          message: 'La placa es requerida',
        });
      }

      const vehicle = await EmissionsVerificationService.searchVehicleByPlate(plate);

      if (!vehicle) {
        return res.status(404).json({
          success: false,
          message: 'Vehículo no encontrado en el sistema',
        });
      }

      return res.json({
        success: true,
        data: {
          vehicle,
          exists: true,
        },
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error inesperado',
      });
    }
  }

  /**
   * Obtener centros de verificación por estado
   */
  static async getVerificationCenters(req: Request, res: Response) {
    try {
      const { state } = req.query;

      if (!state) {
        return res.status(400).json({
          success: false,
          message: 'El estado es requerido',
        });
      }

      const centers = await EmissionsVerificationService.getVerificationCentersByState(state as string);

      return res.json({
        success: true,
        data: centers,
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: error instanceof Error ? error.message : 'Ocurrió un error inesperado',
      });
    }
  }

  /**
   * Registrar nueva verificación
   */
  static async createVerification(req: Request, res: Response) {
    try {
      console.log('🔍 createVerification - Headers:', req.headers);
      console.log('🔍 createVerification - Query params:', req.query);
      console.log('🔍 createVerification - Params:', req.params);
      console.log('🔍 createVerification - Body completo:', JSON.stringify(req.body, null, 2));
      console.log('🔍 createVerification - User vendor:', req.userVendor);
      console.log('🔍 createVerification - User Req:', req.userReq);

      const { vehiclePlate, verificationCenterId, verificationDate, vehiclePhoto, circulationCard } =
        req.body;

      console.log('🔍 createVerification - Datos extraídos:', {
        vehiclePlate: vehiclePlate || 'UNDEFINED',
        verificationCenterId: verificationCenterId || 'UNDEFINED',
        verificationDate: verificationDate || 'UNDEFINED',
        vehiclePhoto: vehiclePhoto ? 'PRESENTE' : 'AUSENTE',
        circulationCard: circulationCard ? 'PRESENTE' : 'AUSENTE',
      });

      // Validaciones
      if (!vehiclePlate || !verificationCenterId || !verificationDate || !vehiclePhoto || !circulationCard) {
        console.log('❌ createVerification - Validación fallida', {
          vehiclePlate,
          verificationCenterId,
          verificationDate,
          vehiclePhoto,
          circulationCard,
        });
        return res.status(400).json({
          success: false,
          message: 'Todos los campos son requeridos',
        });
      }

      // Verificar que el usuario esté autenticado
      const userId = req.userVendor?.userId || req.userReq?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no autenticado',
        });
      }

      console.log('🔍 createVerification - Llamando al servicio', {
        vehiclePlate,
        verificationCenterId,
        verificationDate: new Date(verificationDate),
        createdBy: userId,
      });

      const verification = await EmissionsVerificationService.createVerification({
        vehiclePlate,
        verificationCenterId,
        verificationDate: new Date(verificationDate),
        vehiclePhoto,
        circulationCard,
        createdBy: userId, // Usuario autenticado del vendor platform
      });

      console.log('✅ createVerification - Verificación creada', { verification });

      return res.status(201).json({
        success: true,
        message: 'Verificación registrada exitosamente',
        data: {
          verification,
          customerLink: `${process.env.CUSTOMER_PORTAL_URL}/verification/${verification.uniqueLink}`,
        },
      });
    } catch (error: any) {
      console.log('❌ createVerification - Error capturado:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code,
        fullError: error,
      });
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener verificaciones del centro
   */
  static async getCenterVerifications(req: Request, res: Response) {
    try {
      const { centerId } = req.params;
      const { page = 1, limit = 20, status } = req.query;

      const result = await EmissionsVerificationService.getVerificationsByCenter(
        centerId,
        parseInt(page as string),
        parseInt(limit as string),
        status as string
      );

      res.json({
        success: true,
        data: result,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener historial de verificaciones por vehículo
   */
  static async getVehicleHistory(req: Request, res: Response) {
    try {
      const { plate } = req.params;

      if (!plate) {
        return res.status(400).json({
          success: false,
          message: 'La placa es requerida',
        });
      }

      const history = await EmissionsVerificationService.getVehicleVerificationHistory(plate);

      return res.json({
        success: true,
        data: history,
      });
    } catch (error: any) {
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener estadísticas del centro
   */
  static async getCenterStats(req: Request, res: Response) {
    try {
      const { centerId } = req.params;

      // Obtener estadísticas básicas
      const [totalVerifications, pendingCustomer, completed, thisMonth] = await Promise.all([
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1).then((r) => r.pagination.total),
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1, 'pending_customer').then(
          (r) => r.pagination.total
        ),
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1, 'completed').then(
          (r) => r.pagination.total
        ),
        // Verificaciones de este mes
        EmissionsVerificationService.getVerificationsByCenter(centerId, 1, 1000).then((r) => {
          const currentMonth = new Date();
          currentMonth.setDate(1);
          return r.verifications.filter((v) => v.verificationDate >= currentMonth).length;
        }),
      ]);

      res.json({
        success: true,
        data: {
          total: totalVerifications,
          pendingCustomer,
          completed,
          thisMonth,
          completionRate: totalVerifications > 0 ? Math.round((completed / totalVerifications) * 100) : 0,
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
}
