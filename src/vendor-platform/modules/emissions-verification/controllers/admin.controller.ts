import { Request, Response } from 'express';
import { EmissionsVerificationService } from '../services/emissions-verification.service';
import { EmissionsVerification } from '../models/emissions-verification.model';
import { VerificationCenter } from '../models/verification-center.model';
import '../types/request.types'; // Importar tipos globales

export class AdminVerificationController {
  /**
   * Dashboard general con estadísticas
   */
  static async getDashboard(req: Request, res: Response) {
    try {
      const today = new Date();
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sixMonthsFromNow = new Date(today.getTime() + 180 * 24 * 60 * 60 * 1000);

      const [
        totalVerifications,
        completedVerifications,
        pendingCustomer,
        recentVerifications,
        upcomingDue,
        overdueVerifications,
        totalCenters,
        activeCenters,
      ] = await Promise.all([
        EmissionsVerification.countDocuments(),
        EmissionsVerification.countDocuments({ status: 'completed' }),
        EmissionsVerification.countDocuments({ status: 'pending_customer' }),
        EmissionsVerification.countDocuments({
          createdAt: { $gte: thirtyDaysAgo },
        }),
        EmissionsVerification.countDocuments({
          status: 'completed',
          nextVerificationDate: { $gte: today, $lte: sixMonthsFromNow },
        }),
        EmissionsVerification.countDocuments({
          status: 'completed',
          nextVerificationDate: { $lt: today },
        }),
        VerificationCenter.countDocuments(),
        VerificationCenter.countDocuments({ isActive: true }),
      ]);

      const completionRate =
        totalVerifications > 0 ? Math.round((completedVerifications / totalVerifications) * 100) : 0;

      res.json({
        success: true,
        data: {
          summary: {
            totalVerifications,
            completedVerifications,
            pendingCustomer,
            completionRate,
          },
          recent: {
            last30Days: recentVerifications,
          },
          alerts: {
            upcomingDue,
            overdue: overdueVerifications,
          },
          centers: {
            total: totalCenters,
            active: activeCenters,
          },
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener todas las verificaciones con filtros
   */
  static async getAllVerifications(req: Request, res: Response) {
    try {
      const { page = 1, limit = 20, status, centerId, startDate, endDate, search } = req.query;

      const query: any = {};

      if (status) query.status = status;
      if (centerId) query.verificationCenterId = centerId;

      if (startDate || endDate) {
        query.verificationDate = {};
        if (startDate) query.verificationDate.$gte = new Date(startDate as string);
        if (endDate) query.verificationDate.$lte = new Date(endDate as string);
      }

      if (search) {
        query.vehiclePlate = { $regex: new RegExp(search as string, 'i') };
      }

      const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

      const [verifications, total] = await Promise.all([
        EmissionsVerification.find(query)
          .populate('verificationCenter', 'name code location state')
          .populate('createdBy', 'name email')
          .sort({ verificationDate: -1 })
          .skip(skip)
          .limit(parseInt(limit as string)),
        EmissionsVerification.countDocuments(query),
      ]);

      res.json({
        success: true,
        data: {
          verifications,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            pages: Math.ceil(total / parseInt(limit as string)),
          },
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Subir verificación pasada manualmente
   */
  static async uploadPastVerification(req: Request, res: Response) {
    try {
      const {
        vehiclePlate,
        verificationCenterId,
        verificationDate,
        hologramType,
        verificationCertificate,
        hologramPhoto,
        notes,
      } = req.body;

      if (!vehiclePlate || !verificationCenterId || !verificationDate) {
        return res.status(400).json({
          success: false,
          message: 'Placa, centro de verificación y fecha son requeridos',
        });
      }

      // Verificar que el usuario esté autenticado
      const userId = req.userReq?.userId || req.userVendor?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no autenticado',
        });
      }

      const verification = await EmissionsVerificationService.createVerification({
        vehiclePlate,
        verificationCenterId,
        verificationDate: new Date(verificationDate),
        vehiclePhoto: 'admin_upload',
        circulationCard: 'admin_upload',
        createdBy: userId,
      });

      if (hologramType || verificationCertificate || hologramPhoto) {
        await EmissionsVerificationService.updateCustomerEvidence(verification.uniqueLink, {
          verificationCertificate,
          hologramType,
          hologramPhoto,
          customerNotes: notes,
        });
      }

      return res.status(201).json({
        success: true,
        message: 'Verificación pasada registrada exitosamente',
        data: verification,
      });
    } catch (error: any) {
      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Obtener historial completo de un vehículo
   */
  static async getVehicleFullHistory(req: Request, res: Response) {
    try {
      const { plate } = req.params;

      const history = await EmissionsVerificationService.getVehicleVerificationHistory(plate);

      const stats = {
        totalVerifications: history.length,
        completedVerifications: history.filter((v) => v.status === 'completed').length,
        lastVerification: history[0]?.verificationDate,
        nextDueDate: history.find((v) => v.status === 'completed')?.nextVerificationDate,
      };

      res.json({
        success: true,
        data: {
          vehiclePlate: plate.toUpperCase(),
          history,
          stats,
        },
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Crear nuevo centro de verificación
   */
  static async createVerificationCenter(req: Request, res: Response) {
    try {
      // Verificar que el usuario esté autenticado
      if (!req.userReq?.userId && !req.userVendor?.userId) {
        return res.status(401).json({
          success: false,
          message: 'Usuario no autenticado',
        });
      }

      const { name, code, state, organizationId, location, authorizedFor } = req.body;

      if (!name || !code || !state || !organizationId || !location) {
        return res.status(400).json({
          success: false,
          message: 'Todos los campos obligatorios son requeridos',
        });
      }

      const center = new VerificationCenter({
        name,
        code: code.toUpperCase(),
        state: state.toUpperCase(),
        organizationId,
        location,
        authorizedFor: authorizedFor || ['gasoline', 'diesel'],
        isActive: true,
      });

      await center.save();

      return res.status(201).json({
        success: true,
        message: 'Centro de verificación creado exitosamente',
        data: center,
      });
    } catch (error: any) {
      if (error.code === 11000) {
        return res.status(400).json({
          success: false,
          message: 'El código del centro de verificación ya existe',
        });
      }

      return res.status(500).json({
        success: false,
        message: error.message,
      });
    }
  }
}
