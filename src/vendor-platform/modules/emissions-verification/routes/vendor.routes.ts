import { Router } from 'express';
import { VendorVerificationController } from '../controllers/vendor.controller';
import { ValidationMiddleware } from '../middlewares/validation.middleware';

const router = Router();

// Buscar vehículo por placa
router.get('/search-vehicle/:plate', VendorVerificationController.searchVehicle);

// Obtener centros de verificación por estado
router.get('/verification-centers', VendorVerificationController.getVerificationCenters);

// Registrar nueva verificación
router.post(
  '/verifications',
  ValidationMiddleware.validatePlateFormat,
  ValidationMiddleware.validateVerificationDate,
  ValidationMiddleware.validateVerificationCenter,
  ValidationMiddleware.validateVendorEvidence,
  ValidationMiddleware.validateDuplicateVerification,
  ValidationMiddleware.validateVerificationPeriod,
  VendorVerificationController.createVerification
);

// Obtener verificaciones del centro
router.get('/centers/:centerId/verifications', VendorVerificationController.getCenterVerifications);

// Obtener estadísticas del centro
router.get('/centers/:centerId/stats', VendorVerificationController.getCenterStats);

// Obtener historial de verificaciones por vehículo
router.get(
  '/vehicles/:plate/history',
  ValidationMiddleware.validatePlateFormat,
  VendorVerificationController.getVehicleHistory
);

export default router;
