import { Router } from 'express';
import vendorRoutes from './vendor.routes';
import customerRoutes from './customer.routes';
import adminRoutes from './admin.routes';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { isAdminPlatform } from '@/vendor-platform/utils/is-admin-platform';

const router = Router();

// Rutas para vendors (requieren autenticación)
router.use('/vendor', verifyTokenVendorPlatform, vendorRoutes);

// Rutas para customers (públicas, acceso por link único)
router.use('/customer', customerRoutes);

// Rutas para admin (requieren autenticación y ser admin platform)
router.use('/admin', verifyTokenVendorPlatform, isAdminPlatform, adminRoutes);

export default router;
