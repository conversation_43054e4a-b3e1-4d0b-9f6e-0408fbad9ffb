import { Router } from 'express';
import { AdminVerificationController } from '../controllers/admin.controller';

const router = Router();

// Dashboard general con estadísticas
router.get('/dashboard', AdminVerificationController.getDashboard);

// Obtener todas las verificaciones con filtros
router.get('/verifications', AdminVerificationController.getAllVerifications);

// Obtener historial completo de un vehículo
router.get('/vehicles/:plate/full-history', AdminVerificationController.getVehicleFullHistory);

// Subir verificación pasada manualmente
router.post('/verifications/past', AdminVerificationController.uploadPastVerification);

// Crear nuevo centro de verificación
router.post('/verification-centers', AdminVerificationController.createVerificationCenter);

export default router;
