# Sistema de Verificaciones Vehiculares 🚗

Sistema completo para gestionar verificaciones vehiculares con flujos diferenciados para vendors, customers y admins.

## 📋 Características Implementadas

### ✅ 1. Vendor: Verification Registration
- **Búsqueda de vehículo** por placa en base de datos interna
- **Dropdown de verificentros** filtrado por estado 
- **Upload de evidencias**: foto del vehículo y Tarjeta de Circulación (TDC)
- **Auto-validación**: verificación de existencia del vehículo en sistema
- **Trigger automático**: envío de link único al customer

### ✅ 2. Customer: Verification Confirmation  
- **Acceso por link único** sin necesidad de registro
- **Upload de evidencias**: certificado de verificación y foto de holograma
- **Declaración de tipo de holograma**: 00, 0, 1, 2
- **Recordatorios diarios** hasta completar el proceso

### ✅ 3. Admin: Dashboard y Tracking
- **Dashboard con estadísticas** completas del sistema
- **Historial de verificaciones** por vehículo  
- **Acceso a evidencias** subidas (fotos, PDFs)
- **Cálculo automático** de próxima verificación
- **Upload manual** de verificaciones pasadas para vehículos activos

### ✅ 4. Lógica de Verificación Completa
- **Cálculo por último dígito** de placa según normativa mexicana
- **Holograma 00**: exención por 2 años
- **Hologramas 0, 1, 2**: verificación cada 6 meses
- **Vehículos híbridos/eléctricos**: exención por 8 años desde registro
- **Sistema de triggers**: recordatorios al conductor y flota

## 🏗️ Arquitectura del Sistema

```
/emissions-verification/
├── controllers/           # Controladores por tipo de usuario
│   ├── admin.controller.ts      # Dashboard, estadísticas, gestión
│   ├── customer.controller.ts   # Upload evidencias, consultas
│   └── vendor.controller.ts     # Registro verificaciones, búsquedas
├── models/               # Modelos de datos
│   ├── emissions-verification.model.ts  # Esquema principal
│   └── verification-center.model.ts     # Centros de verificación
├── services/             # Lógica de negocio
│   ├── emissions-verification.service.ts # CRUD principal
│   ├── verification-logic.service.ts    # Cálculos de fechas
│   └── notification.service.ts          # Sistema de recordatorios
├── routes/               # Configuración de endpoints
│   ├── admin.routes.ts
│   ├── customer.routes.ts
│   ├── vendor.routes.ts
│   └── index.ts
├── middlewares/          # Validaciones y seguridad
│   └── validation.middleware.ts
├── scripts/              # Utilidades y pruebas
│   ├── seed-verification-centers.ts
│   └── test-verification-flow.ts
└── docs/                 # Documentación
    └── VERIFICATION_SYSTEM_GUIDE.md
```

## 🔄 Flujo Completo del Proceso

### Paso 1: Vendor Registration
```http
POST /vendor-platform/emissions-verification/vendor/verifications
Authorization: Bearer <vendor_token>
Content-Type: application/json

{
  "vehiclePlate": "ABC123",
  "verificationCenterId": "64a1b2c3d4e5f6789012345",
  "verificationDate": "2024-03-15T10:30:00.000Z",
  "vehiclePhoto": "https://storage.example.com/photos/vehicle_abc123.jpg",
  "circulationCard": "https://storage.example.com/docs/tdc_abc123.pdf"
}
```

**Respuesta:**
```json
{
  "success": true,
  "message": "Verificación registrada exitosamente",
  "data": {
    "verification": {
      "_id": "64a1b2c3d4e5f6789012346",
      "vehiclePlate": "ABC123",
      "status": "pending_customer",
      "uniqueLink": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "nextVerificationDate": "2024-09-15T23:59:59.999Z"
    },
    "customerLink": "https://customer.portal.com/verification/a1b2c3d4-e5f6-7890-1234-567890abcdef"
  }
}
```

### Paso 2: Customer Access
```http
GET /vendor-platform/emissions-verification/customer/a1b2c3d4-e5f6-7890-1234-567890abcdef
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "_id": "64a1b2c3d4e5f6789012346",
    "vehiclePlate": "ABC123", 
    "verificationDate": "2024-03-15T10:30:00.000Z",
    "verificationCenter": {
      "name": "Verificentro MH01 - Centro",
      "code": "MH01",
      "location": {
        "address": "Av. Insurgentes Sur 1234, Col. Roma Norte",
        "city": "Ciudad de México"
      }
    },
    "status": "pending_customer",
    "customerEvidence": null
  }
}
```

### Paso 3: Customer Evidence Upload
```http
PUT /vendor-platform/emissions-verification/customer/a1b2c3d4-e5f6-7890-1234-567890abcdef/evidence
Content-Type: application/json

{
  "verificationCertificate": "https://storage.example.com/certs/cert_abc123.pdf",
  "hologramType": "1",
  "hologramPhoto": "https://storage.example.com/holograms/holo_abc123.jpg",
  "isExempt": false,
  "customerNotes": "Verificación realizada sin problemas"
}
```

### Paso 4: Admin Dashboard
```http
GET /vendor-platform/emissions-verification/admin/dashboard
Authorization: Bearer <admin_token>
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalVerifications": 1250,
      "completedVerifications": 1180,
      "pendingCustomer": 70,
      "completionRate": 94
    },
    "recent": {
      "last30Days": 85
    },
    "alerts": {
      "upcomingDue": 23,
      "overdue": 5
    },
    "centers": {
      "total": 12,
      "active": 11
    }
  }
}
```

## 📊 Lógica de Verificación

### Tabla de Verificación por Último Dígito
| Último Dígito | Color | Meses de Verificación |
|---------------|-------|----------------------|
| 5 o 6 | Amarillo | Enero-Febrero / Julio-Agosto |
| 7 o 8 | Rosa | Febrero-Marzo / Agosto-Septiembre |
| 3 o 4 | Rojo | Marzo-Abril / Septiembre-Octubre |
| 1 o 2 | Verde | Abril-Mayo / Octubre-Noviembre |
| 9 o 0 | Azul | Mayo-Junio / Noviembre-Diciembre |

### Tipos de Holograma
- **00 (Doble Cero)**: Exento por 2 años desde fecha de verificación
- **0, 1, 2**: Verificación cada 6 meses según tabla anterior
- **Híbridos/Eléctricos**: Exentos por 8 años desde fecha de registro

### Sistema de Recordatorios
- **Customer pendiente**: Recordatorio diario hasta subir evidencias
- **Próximo vencimiento**: Notificación 30 días antes
- **Flota**: Reporte mensual de vencimientos del mes siguiente

## 🛠️ Endpoints Disponibles

### Vendor Endpoints
- `GET /vendor/search-vehicle/:plate` - Buscar vehículo
- `GET /vendor/verification-centers` - Obtener centros por estado
- `POST /vendor/verifications` - Registrar verificación
- `GET /vendor/centers/:centerId/verifications` - Verificaciones del centro
- `GET /vendor/centers/:centerId/stats` - Estadísticas del centro
- `GET /vendor/vehicles/:plate/history` - Historial por vehículo

### Customer Endpoints  
- `GET /customer/hologram-options/list` - Opciones de hologramas
- `GET /customer/:uniqueLink` - Info de verificación
- `PUT /customer/:uniqueLink/evidence` - Subir evidencias
- `GET /customer/:uniqueLink/completion-status` - Estado del proceso
- `GET /customer/:uniqueLink/next-verification` - Próxima verificación

### Admin Endpoints
- `GET /admin/dashboard` - Dashboard con estadísticas
- `GET /admin/verifications` - Todas las verificaciones (filtros)
- `GET /admin/vehicles/:plate/full-history` - Historial completo
- `POST /admin/verifications/past` - Subir verificación pasada
- `POST /admin/verification-centers` - Crear centro

## 🔒 Seguridad y Validaciones

### Middlewares de Validación
- **Formato de placa**: Validación de placas mexicanas
- **Verificación duplicada**: Previene registros duplicados en 30 días
- **Centro activo**: Valida que el verificentro esté operativo
- **Fecha válida**: No futuras, máximo 30 días atrás
- **Evidencias**: URLs válidas y formatos correctos
- **Link único**: Validación de existencia y no expiración

### Autenticación
- **Vendor**: Token JWT requerido para todas las operaciones
- **Customer**: Acceso público por link único seguro
- **Admin**: Token JWT + permisos de administrador platform

## 🚀 Configuración e Inicialización

### 1. Crear Centros de Verificación
```bash
# Desde la raíz del proyecto
npx ts-node src/vendor-platform/modules/emissions-verification/scripts/seed-verification-centers.ts
```

### 2. Ejecutar Pruebas del Sistema
```bash
npx ts-node src/vendor-platform/modules/emissions-verification/scripts/test-verification-flow.ts
```

### 3. Variables de Entorno Requeridas
```env
CUSTOMER_PORTAL_URL=https://customer.portal.com
```

## 📈 Métricas y Monitoreo

### KPIs Principales
- **Tasa de completación**: % de verificaciones completadas por customers
- **Tiempo promedio**: Desde registro vendor hasta evidencias customer
- **Centros más activos**: Ranking por volumen de verificaciones
- **Vencimientos próximos**: Alert system para recordatorios

### Logs y Auditoria
- Todos los cambios de estado se registran con timestamps
- Tracking de recordatorios enviados
- Historial completo por vehículo
- Métricas de uso por centro de verificación

## 🔧 Mantenimiento

### Tareas Automáticas (Cron Jobs)
```javascript
// Ejecutar diariamente a las 9:00 AM
cron.schedule('0 9 * * *', () => {
  NotificationService.processReminders();
});
```

### Limpieza de Datos
- Expirar verificaciones pendientes después de 30 días
- Archivar verificaciones completadas antiguas
- Limpiar links únicos expirados

## 🎯 Próximos Pasos y Mejoras

### Integraciones Pendientes
- [ ] Sistema de WhatsApp para recordatorios
- [ ] API de email para notificaciones
- [ ] Integración con sistema de pagos
- [ ] OCR automático para documentos

### Funcionalidades Adicionales
- [ ] Dashboard con gráficos en tiempo real
- [ ] Reportes en PDF para administradores
- [ ] API pública para consultas de verificación
- [ ] Mobile app para vendors y customers

---

**Desarrollado para OneCarNow Platform** 🚗💨