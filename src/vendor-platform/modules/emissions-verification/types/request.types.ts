// Tipos para el sistema de verificaciones vehiculares
export interface VendorUser {
  userId: string;
  role: string;
  organizationId: string;
  email: string;
  isAdminPlatform?: boolean;
}

export interface AdminUser {
  userId: string;
  role: string;
  organizationId: string;
  email: string;
  isAdminPlatform?: boolean;
}

// Extiende la interfaz Request para incluir middleware de validación
declare global {
  namespace Express {
    interface Request {
      verification?: any; // Para middleware de validación
    }
  }
}
