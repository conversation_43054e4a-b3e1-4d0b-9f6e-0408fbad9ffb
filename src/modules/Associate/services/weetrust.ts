import axios from 'axios';
import { WEETRUST_API_KEY, WEETRUST_URL, WEETRUST_USER_ID } from '../../../constants';
import FormData from 'form-data';
import fs from 'fs';
import { logger } from '../../../clean/lib/logger';

export default async function getWeetrustToken() {
  const response = await axios.post(
    `${WEETRUST_URL}/access/token`,
    {},
    {
      headers: {
        'user-id': WEETRUST_USER_ID,
        'api-key': WEETRUST_API_KEY,
      },
    }
  );
  return response.data.responseData.accessToken;
}

export async function deleteWeetrustDocument(token: string | undefined, documentId: string) {
  try {
    if (!token) {
      token = await getWeetrustToken();
    }

    const url = new URL(WEETRUST_URL + '/documents');

    url.searchParams.append('documentID', documentId);

    const { data } = await axios.delete(url.toString(), {
      headers: {
        'user-id': WEETRUST_USER_ID,
        token,
      },
    });
    logger.info(`[deleteWeetrustDocument]: Document deleted successfully ${documentId}`);

    return data;
  } catch (error: any) {
    // console.error(error.response.data);
    const message = error.response?.data || error.message;
    const errorObj = {
      message,
      documentId,
      error: error.response.data,
    };
    logger.error(`[deleteWeetrustDocument]: ${JSON.stringify(errorObj)}`);
    return {
      success: false,
      ...errorObj,
    };
  }
}

export async function sendDocumentToWeetrust({ file, token }: { file: Express.Multer.File; token?: string }) {
  try {
    if (!token) {
      token = await getWeetrustToken();
    }

    const url = new URL(WEETRUST_URL + '/documents');

    const language = 'es';

    const formData = new FormData();

    const fileStream = fs.createReadStream(file.path);

    formData.append('document', fileStream, {
      filename: file.originalname,
      contentType: 'application/pdf',
    });

    const { data } = await axios.post(url.toString(), formData, {
      headers: {
        'user-id': WEETRUST_USER_ID,
        ...formData.getHeaders(),
        token,
        language,
        position: 'geolocation',
        country: 'mx',
        documentSignType: 'ELECTRONIC_SIGNATURE',
      },
    });

    return data;
  } catch (error: any) {
    // console.error(error.response.data);
    console.log('error', error.response?.data || error.response || error.message);
    return null;
  }
}

// create a type or interface for the returning data by the api
/* 
  example response:
  "responseData": {
        "documentID": "6830bbc16fed27002ae6e771",
        "documentType": "OTHERS",
        "status": "COMPLETED",
        "country": "Mexico",
        "documentSignType": "ELECTRONIC_SIGNATURE",
        "addedOn": 1748024259026,
        "documentFileObj": {
            "url": "https://production-signing-files.s3.amazonaws.com/ArihanDeLaCruzLaO101453diq12q_1748024256715_2.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=82ede1ac328fdc269c7514164f089b82a4b76dd5b016261d13b74dba42471a57&X-Amz-SignedHeaders=host",
            "size": "439.9 KB"
        },
        "signatory": [
            {
                "emailID": "<EMAIL>",
                "name": "Arihan De La Cruz La O",
                "identitySessionId": "",
                "isSigned": 1,
                "signatoryID": "6830bbc36fed27002ae6e7fa",
                "signing": {
                    "url": "https://app.weetrust.mx/signatory/6830bbc16fed27002ae6e772/6830bbc36fed27002ae6e7fa/1748024259768",
                    "expiry": 1748888259768
                },
                "imageURL": "https://production-signing-files.s3.amazonaws.com/signature_1748028735322.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=c8b37ec1916cbf3dd34799d8962e7406949a88a79b51b21711c0822fcca20c98&X-Amz-SignedHeaders=host",
                "emailTracking": []
            },
            {
                "emailID": "<EMAIL>",
                "name": "ANIBAL UGALDE GUTIERREZ ",
                "identitySessionId": "",
                "isSigned": 1,
                "signatoryID": "6830bbc36fed27002ae6e7fb",
                "signing": {
                    "url": "https://app.weetrust.mx/signatory/6830bbc16fed27002ae6e772/6830bbc36fed27002ae6e7fb/1748024259768",
                    "expiry": 1748888259769
                },
                "imageURL": "https://production-signing-files.s3.amazonaws.com/signature_1748028976320.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=3462dca20acbc6ba3c23c4c1cb402ef8d065df8d6373341ffaa62305570c0524&X-Amz-SignedHeaders=host",
                "emailTracking": []
            },
            {
                "emailID": "<EMAIL>",
                "name": "Mairon Esteban Sandoval Gómez",
                "identitySessionId": "",
                "isSigned": 1,
                "signatoryID": "6830bbc36fed27002ae6e7fc",
                "signing": {
                    "url": "https://app.weetrust.mx/signatory/6830bbc16fed27002ae6e772/6830bbc36fed27002ae6e7fc/1748024259769",
                    "expiry": 1748888259769
                },
                "imageURL": "https://production-signing-files.s3.amazonaws.com/signature_1724356595076.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=718863e789af61625d86ea382a0104c76982b004ee973df4dd33bb36f0519057&X-Amz-SignedHeaders=host",
                "emailTracking": []
            }
        ],
        "sharedWith": [],
        "pscCertificate": "https://production-signing-files.s3.amazonaws.com/ArihanDeLaCruzLaO101453diq12q_1748024256715_2.asn?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=3b59a6272d5ea585bad67cb8aa0ce68a02d984b556ff256a0840329885c4080f&X-Amz-SignedHeaders=host",
        "blockchainCertificate": "https://production-signing-files.s3.amazonaws.com/certificate_ArihanDeLaCruzLaO101453diq12q_1748024256715_2.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQWUO7SMLC2XZ2EXT%2F20250701%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250701T183419Z&X-Amz-Expires=518400&X-Amz-Signature=6051b4face9a581c654dc9ae19786b08f3464829be9c3f3192df1e93cbe35d81&X-Amz-SignedHeaders=host"
    }

    only type the important fields
*/

interface WeetrustDocumentByIdResponse {
  responseData: {
    documentID: string;
    documentType: string;
    status: string;
    country: string;
    documentSignType: string;
    addedOn: number;
    documentFileObj: {
      url: string;
      size: string;
    };
    splitChildDocumentId: string;
    signatory: {
      emailID: string;
      name: string;
      identitySessionId: string;
      isSigned: number;
      signatoryID: string;
      signing: {
        url: string;
        expiry: number;
      };
      imageURL: string;
      emailTracking: any[];
    }[];
    sharedWith: any[];
    pscCertificate: string;
    blockchainCertificate: string;
  };
}

export async function getDocumentById(documentId: string, useToken?: string) {
  try {
    // const token = await getWeetrustToken();
    const token = useToken || (await getWeetrustToken());

    const url = new URL(WEETRUST_URL + '/documents/' + documentId);

    // url.searchParams.append('documentID', documentId);

    const { data } = await axios.get(url.toString(), {
      headers: {
        'user-id': WEETRUST_USER_ID,
        token,
      },
    });

    return data as WeetrustDocumentByIdResponse;
  } catch (error: any) {
    // console.error(error.response.data);
    console.log('error', error.response?.data || error.response || error.message);
    return null;
  }
}
