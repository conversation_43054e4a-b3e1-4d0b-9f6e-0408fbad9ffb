import { logger } from '@/clean/lib/logger';
import { firebaseAdmin } from '../configuration/firebaseAdmin';
import { NotificationPayload } from '../interfaces/fcmNotificationPayload';
import fcmNotificationTokenSchema from '@/models/fcmNotificationTokenSchema';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { TokenMessage } from 'firebase-admin/messaging';
import fcmNotificationSchema, { FCMNotificationType } from '@/models/fcmNotificationSchema';
import { FCMNotificationStatus, FCMNotificationUserType } from '../common/enums';

interface RegisterNotificationTokenPayload {
  userId: String;
  fcmToken: String;
  deviceDetails: Record<string, any>;
  userType: FCMNotificationUserType;
}

class FCMNotificationService {
  async registerNotificationToken(payload: RegisterNotificationTokenPayload): Promise<void> {
    try {
      // Check if the token already exists for the associateId
      const fcmNotificationDetails = await fcmNotificationTokenSchema
        .findOne({ userId: payload.userId })
        .lean();
      // If no existing tokens are found, create a new entry
      if (!fcmNotificationDetails) {
        logger.info(
          `[registerNotificationToken] - No existing FCM tokens found for user ID: ${payload.userId}`
        );
        // Create a new FCM token entry
        await fcmNotificationTokenSchema.create({
          userId: payload.userId,
          fcmTokens: [
            {
              isActive: true,
              oldToken: payload.fcmToken,
              token: payload.fcmToken,
              deviceDetails: payload.deviceDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          ],
          userType: payload.userType,
        });
        // Log the successful registration
        logger.info(`[registerNotificationToken] - FCM token registered for user ID: ${payload.userId}`);
      } else {
        // If existing tokens are found, check if the token already exists
        const existingToken = fcmNotificationDetails.fcmTokens.find(
          (token) => token.token === payload.fcmToken
        );
        // If the token already exists and is active, log and throw an AlreadyExistException
        if (existingToken) {
          if (existingToken.isActive) {
            logger.info(
              `[registerNotificationToken] - FCM token already registered for user ID: ${payload.userId}`
            );
            throw new AlreadyExistException();
          } else {
            // If the token exists but is inactive, update it to active
            await fcmNotificationTokenSchema.updateOne(
              { userId: payload.userId, 'fcmTokens.token': payload.fcmToken },
              {
                $set: {
                  'fcmTokens.$.isActive': true,
                  'fcmTokens.$.updatedAt': new Date(),
                },
              }
            );
            // Log the successful reactivation
            logger.info(`[registerNotificationToken] - FCM token reactivated for user ID: ${payload.userId}`);
          }
        } else {
          // If the token does not exist, add it to the existing tokens
          await fcmNotificationTokenSchema.updateOne(
            { userId: payload.userId },
            {
              $push: {
                fcmTokens: {
                  oldToken: payload.fcmToken,
                  token: payload.fcmToken,
                  isActive: true,
                  deviceDetails: payload.deviceDetails,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              },
            }
          );
          // Log the successful registration
          logger.info(`[registerNotificationToken] - FCM token added for user ID: ${payload.userId}`);
        }
      }
    } catch (error) {
      logger.error('[registerNotificationToken] - Error registering FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async unregisterNotificationToken(userId: String, fcmToken: String): Promise<void> {
    try {
      // Remove the FCM token for the associateId
      const result = await fcmNotificationTokenSchema.updateOne(
        { userId, 'fcmTokens.token': fcmToken },
        { $pull: { fcmTokens: { token: fcmToken } } }
      );
      // 2. Remove token entity if fcmTokens is now empty
      await fcmNotificationTokenSchema.deleteOne({
        userId,
        fcmTokens: { $size: 0 },
      });
      // Check if any tokens were modified
      if (result.modifiedCount > 0) {
        logger.info(`[unregisterNotificationToken] - FCM token unregistered for user ID: ${userId}`);
      } else {
        logger.info(
          `[unregisterNotificationToken] - No FCM token found to unregister for user ID: ${userId}`
        );
        throw new NotFoundException({ code: '404', message: 'FCM token not found for the user ID.' });
      }
    } catch (error) {
      logger.error('[unregisterNotificationToken] - Error unregistering FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async updateNotificationTokenState(userId: String, fcmToken: String, isActive: Boolean): Promise<void> {
    try {
      // Update the FCM token to set it as inactive
      const result = await fcmNotificationTokenSchema.updateOne(
        { userId, 'fcmTokens.token': fcmToken },
        { $set: { 'fcmTokens.$.isActive': isActive } }
      );
      // Check if any tokens were modified
      if (result.modifiedCount > 0) {
        logger.info(`[updateNotificationTokenState] - FCM token state is ${isActive} for user ID: ${userId}`);
      } else {
        logger.info(
          `[updateNotificationTokenState] - No FCM token found to update state to ${isActive} for user ID: ${userId}`
        );
        throw new NotFoundException({ code: '404', message: 'FCM token not found for the user ID.' });
      }
    } catch (error) {
      logger.error('[updateNotificationTokenState] - Error updating the state of FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async sendNotificationToAssociateById(userId: String, payload: NotificationPayload): Promise<void> {
    const fcmNotificationDetails = await fcmNotificationTokenSchema.findOne({ userId }).lean();
    if (!fcmNotificationDetails) {
      logger.error(`[sendNotificationToAssociateById] - No FCM token found for user ID: ${userId}`);
      throw new NotFoundException({ code: '404', message: 'FCM token not found for the user ID.' });
    }
    // Extract the first active FCM token
    const activeTokens = fcmNotificationDetails.fcmTokens.filter((token) => token.isActive);
    if (activeTokens.length === 0) {
      logger.error(`[sendNotificationToAssociateById] - No active FCM tokens found for user ID: ${userId}`);
      throw new NotFoundException({
        code: 'NO_ACTIVE_TOKENS',
        message: 'No active FCM tokens found for the user ID.',
      });
    }
    // Prepare the messages to be sent on FCM tokens
    const messages: TokenMessage[] = [];
    for (const token of activeTokens) {
      messages.push({
        token: token.token,
        notification: {
          title: payload.title,
          body: payload.body,
        },
        data: payload.data,
        android: {
          priority: 'high',
        },
      });
    }
    // Create a new notification entries in the database
    const notificationEntries: FCMNotificationType[] = [];
    for (const message of messages) {
      try {
        notificationEntries.push(
          await fcmNotificationSchema.create({
            userId,
            payload: {
              title: message.notification!.title,
              body: message.notification!.body,
              data: message.data || {},
            },
            status: FCMNotificationStatus.PENDING,
          })
        );
      } catch (error) {
        logger.error(`[sendNotificationToAssociateById] - Error creating notification entry: ${error}`);
      }
    }
    if (notificationEntries.length === 0) {
      logger.error(
        `[sendNotificationToAssociateById] - No notification entries created for user ID: ${userId}`
      );
      throw new Error('No notification entries created for the user ID.');
    }
    try {
      const response = await firebaseAdmin.messaging().sendEach(messages);
      // Log the response from FCM
      response.responses.forEach(async (resp, idx) => {
        logger.info(`[sendNotification] - Message status success:${resp.success}`);
        try {
          await fcmNotificationSchema.updateOne(
            { _id: notificationEntries[idx]._id },
            {
              status: resp.success ? FCMNotificationStatus.SENT : FCMNotificationStatus.FAILED,
              messageId: resp.success ? resp.messageId : null,
              failedDetails: resp.success
                ? null
                : {
                    code: resp.error?.code || 'Unknown error code',
                    failedAt: new Date(),
                    failedReason: resp.error?.message || 'Unknown error',
                    token: messages[idx].token,
                  },
            }
          );
          logger.info(`[sendNotification] - Notification sent successfully ${notificationEntries[idx]._id}`);
        } catch (error) {
          logger.error(`[sendNotification] - Error updating notification entry: ${error}`);
        }
      });
    } catch (error) {
      logger.error('[sendNotification] - Error sending notification:', error);
      // Update the notification status to failed in the database
      await fcmNotificationSchema.updateOne(
        { _id: notificationEntries[0]._id },
        {
          status: FCMNotificationStatus.FAILED,
          failedDetails: {
            code: '1011',
            failedAt: new Date(),
            failedReason: error instanceof Error ? error.message : 'Unknown error',
          },
        }
      );
    }
  }

  async resendFailedNotifications(): Promise<void> {
    try {
      // Fetch all notifications with FAILED status
      const failedNotifications = await fcmNotificationSchema
        .find({ status: FCMNotificationStatus.FAILED })
        .lean();

      // If no failed notifications are found, log and return
      if (failedNotifications.length === 0) {
        logger.info('[resendFailedNotifications] - No failed notifications to resend');
        return;
      }

      for (const notification of failedNotifications) {
        // Get the user's FCM token details
        const tokenDetail = await fcmNotificationTokenSchema.findOne({
          userId: notification.userId,
          'fcmTokens.isActive': true, // Only consider active tokens
        });
        // If no active token is found for the user, log and skip to the next notification
        if (!tokenDetail) {
          logger.warn(
            `[resendFailedNotifications] - No active FCM token found for user ID: ${notification.userId}`
          );
          continue; // Skip if no active token is found for this user
        }
        const failedMessageToken = notification.failedDetails?.token;
        if (!failedMessageToken) {
          logger.warn(
            `[resendFailedNotifications] - No token found in failed notification for user ID: ${notification.userId}`
          );
          continue; // Skip if no token is found in the failed notification
        }
        // Find the specific token in the user's token details
        const token = tokenDetail.fcmTokens.find((t) => t.oldToken === failedMessageToken)?.token;
        if (!token) {
          logger.warn(
            `[resendFailedNotifications] - No active token found for user ID: ${notification.userId}`
          );
          continue; // Skip if no token is found in the user's token details
        }

        // Prepare the message to be sent
        const message: TokenMessage = {
          token: token, // Use the active token
          notification: {
            title: notification.payload.title,
            body: notification.payload.body,
          },
          data: notification.payload.data,
          android: {
            priority: 'high',
          },
        };

        try {
          // Send the notification message
          const deliveryMessage = await firebaseAdmin.messaging().send(message);
          // Update the notification status to SENT after successful resend
          await fcmNotificationSchema.updateOne(
            { _id: notification._id },
            {
              status: FCMNotificationStatus.SENT,
              messageId: deliveryMessage,
              failedDetails: null, // Clear failed details on successful resend
            }
          );
          logger.info(
            `[resendFailedNotifications] - Notification resent successfully for user ID: ${notification.userId}`
          );
        } catch (error) {
          logger.error(
            `resendFailedNotifications] - Error resending notification for user ID: ${notification.userId}`,
            error
          );
        }
      }
    } catch (error) {
      logger.error('[resendFailedNotifications] - Error in resendFailedNotifications:', error);
    }
  }

  async refreshNotificationToken({
    userId,
    oldToken,
    newToken,
  }: {
    userId: String;
    oldToken: String;
    newToken: String;
  }): Promise<void> {
    try {
      // Update the FCM token for the user
      const result = await fcmNotificationTokenSchema.updateOne(
        { userId, 'fcmTokens.token': oldToken },
        {
          $set: {
            'fcmTokens.$.token': newToken,
            'fcmTokens.$.oldToken': oldToken,
            'fcmTokens.$.updatedAt': new Date(),
          },
        }
      );
      // Check if any tokens were modified
      if (result.modifiedCount > 0) {
        logger.info(`[refreshNotificationToken] - FCM token updated for user ID: ${userId}`);
      } else {
        logger.info(`[refreshNotificationToken] - No FCM token found to update for user ID: ${userId}`);
        throw new NotFoundException({ code: '404', message: 'FCM token not found for the user ID.' });
      }
    } catch (error) {
      logger.error('[refreshNotificationToken] - Error updating FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }
}

// Exporting the FCMNotificationService instance for use in other parts of the application
export const fcmNotificationService = new FCMNotificationService();
