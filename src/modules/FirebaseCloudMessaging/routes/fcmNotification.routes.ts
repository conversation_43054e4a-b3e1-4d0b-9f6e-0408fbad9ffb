import { Router } from 'express';
import {
  refreshNotificationToken,
  registerNotificationToken,
  sendNotificationToAssociateById,
  unregisterNotificationToken,
  updateNotificationTokenState,
} from '../controllers/fcmNotification.controller';

const fcmNotificationRouter = Router();

fcmNotificationRouter.post('/register', registerNotificationToken);
fcmNotificationRouter.post('/unregister', unregisterNotificationToken);
fcmNotificationRouter.patch('/updateTokenState', updateNotificationTokenState);
fcmNotificationRouter.post('/send', sendNotificationToAssociateById);
fcmNotificationRouter.post('/refreshToken', refreshNotificationToken);

export default fcmNotificationRouter;
