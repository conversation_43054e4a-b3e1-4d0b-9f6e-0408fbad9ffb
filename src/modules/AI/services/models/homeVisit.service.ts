import { logger } from '@/clean/lib/logger';
// Import the new text generation function instead of the client
import { generateTextContent } from '@/services/ocr/llmClients/geminiClient';
import { GEMINI_MAX_OUTPUT_TOKENS } from '@/constants';
import { AdmissionRequest, HomeVisitHouseInformation } from '@/clean/domain/entities';
import { calculateWeightedScore } from '../../lib/helpers';
import { HOME_VISIT_WEIGHTS } from '../../lib/weights';

/**
 * Extract home visit features from admission request for scoring
 */
export function extractHomeVisitFeatures(admissionRequest: AdmissionRequest): Record<string, any> {
  const homeVisit = admissionRequest.homeVisit || {};
  const personalData = admissionRequest.personalData || {};
  const houseInformation = homeVisit.houseInformation || ({} as HomeVisitHouseInformation);

  // Extract features - focusing only on home visit relevant data
  return {
    // Location quality (from personal data)
    city: personalData.city || '',
    state: personalData.state || '',
    municipality: personalData.municipality || '',
    neighborhood: personalData.neighborhood || '',

    // Visit verification data
    isAddressProvidedByApplicant: !!homeVisit.doesProofOfAddressMatchLocation,
    comments: homeVisit.comments || '',
    doesProofOfAddressMatchLocation: homeVisit.doesProofOfAddressMatchLocation || '',
    characteristicsOfGarage: homeVisit.characteristicsOfGarage || '',
    behaviourOfCustomerDuringCall: homeVisit.behaviourOfCustomerDuringCall || '',
    visitorObservations: homeVisit.comments || '', // Duplicate as visitorObservations for clarity

    // Housing details
    housingType: houseInformation.typeOfHousing || '',
    bedroomCount: houseInformation.noOfBedrooms || 0,
    hasLivingRoom: houseInformation.livingRoom === 'Yes',
    hasDiningRoom: houseInformation.dinningRoom === 'Yes',
    hasKitchen: houseInformation.kitchen === 'Yes',
    hasTelevision: houseInformation.television === 'Yes',
    hasStove: houseInformation.stove === 'Yes',
    hasRefrigerator: houseInformation.refrigerator === 'Yes',
    hasWashingMachine: houseInformation.washingMachine === 'Yes',
  };
}

/**
 * Create prompt for home visit analysis
 */
function createHomeVisitPrompt(features: Record<string, any>): string {
  return `You are a specialized home visit evaluator for loan underwriting.
Each feature receives a numeric score between 0 and 100 based on quality assessment.

Use the following values for scoring individual features:
city: ${features.city}
state: ${features.state}
municipality: ${features.municipality}
neighborhood: ${features.neighborhood}
isAddressProvidedByApplicant: ${features.isAddressProvidedByApplicant}
doesProofOfAddressMatchLocation: ${features.doesProofOfAddressMatchLocation}
behaviourOfCustomerDuringCall: ${features.behaviourOfCustomerDuringCall}
visitorObservations: ${features.visitorObservations}
housingType: ${features.housingType}
bedroomCount: ${features.bedroomCount}
hasLivingRoom: ${features.hasLivingRoom}
hasDiningRoom: ${features.hasDiningRoom}
hasKitchen: ${features.hasKitchen}
hasTelevision: ${features.hasTelevision}
hasStove: ${features.hasStove}
hasRefrigerator: ${features.hasRefrigerator}
hasWashingMachine: ${features.hasWashingMachine}
characteristicsOfGarage: ${features.characteristicsOfGarage}

Scoring guidelines:
- Location assessment: Higher scores for safer, more affluent neighborhoods and municipalities
- Housing quality: Higher scores for better housing conditions and more amenities
- Verification quality: Higher scores for better documentation and proof matches

For each feature, produce a 0-100 score where:
- 90-100: Excellent - Very low risk
- 75-89: Good - Low risk
- 60-74: Average - Moderate risk
- 40-59: Below average - Higher risk
- 0-39: Poor - Very high risk

Return your assessment as a structured JSON with individual scores and explanations.
RETURN ONLY THE JSON STRING Of below shape AND NO EXTRA WORDS BEFORE OR AFTER THE JSON AS THE STRING WILL BE PARSED AS JSON.
PRESENCE OF EXTRA WORDS WILL FAIL THE PARSING OF JSON STRING:
{
  "individualScores": {
    "city": score,
    "state": score,
    ...and so on for all features
  },
  "explanation": {
    "city": "Brief explanation for city score",
    "state": "Brief explanation for state score",
    ...and so on for all features,
    "final_result": "approve or reject with brief reasoning"
  }
}`;
}

/**
 * Perform home visit analysis using Gemini API via helper function
 */
export async function performHomeVisitAnalysis(admissionRequest: AdmissionRequest): Promise<{
  finalScore: number;
  individualScores: Record<string, number>;
  responseData: any;
}> {
  try {
    logger.info(`[performHomeVisitAnalysis] Starting home visit analysis for request ${admissionRequest.id}`);

    // Extract features from admission request
    const features = extractHomeVisitFeatures(admissionRequest);

    // Create prompt for analysis
    const prompt = createHomeVisitPrompt(features);

    // Configure generation parameters
    const generationConfig = {
      temperature: 0,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
    };

    // Call Gemini API using the helper function with retry logic
    // It will handle the API call, retries, and JSON extraction
    const responseJson = await generateTextContent(prompt, generationConfig);

    // Validate response (basic check, detailed handled in generateTextContent)
    if (!responseJson) {
      throw new Error('Empty or invalid JSON response from Gemini API helper');
    }

    // Get individual scores
    const individualScores = responseJson.individualScores || {};

    // Calculate final score using weighted average
    const finalScore = calculateWeightedScore(HOME_VISIT_WEIGHTS, individualScores);

    // Add final score to the response
    responseJson.finalScore = finalScore;

    logger.info(
      `[performHomeVisitAnalysis] Completed home visit analysis for request ${admissionRequest.id}`
    );

    return {
      finalScore,
      individualScores,
      responseData: responseJson,
    };
  } catch (error) {
    logger.error(`[performHomeVisitAnalysis] Error: ${error}`);
    throw error;
  }
}
