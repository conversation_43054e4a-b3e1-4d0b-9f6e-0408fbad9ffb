import { logger } from '@/clean/lib/logger';
import { generateTextContent } from '@/services/ocr/llmClients/geminiClient';
import { GEMINI_MAX_OUTPUT_TOKENS } from '@/constants';
import {
  AdmissionRequest,
  DocumentAnalysisResult,
  ModelResult,
  RequestDocument,
} from '@/clean/domain/entities';
import { AnalysisStatus, MLModels } from '@/clean/domain/enums';
import { calculateWeightedScore } from '../../lib/helpers';
import { repoSaveDocumentsResults } from '@/clean/data/mongoRepositories';
import { FINANCIAL_ASSESSMENT_WEIGHTS } from '../../lib/weights';
import { analyzeDocumentService } from '../documentsAnalysis/documentsAnalysis.service';

/**
 * Calculate weekly earnings from deposits data
 */
function calculateWeeklyEarnings(deposits: { period: string; amount: number }[]): number {
  if (!deposits || deposits.length === 0) {
    return 0;
  }

  // Calculate average weekly earnings
  const totalWeeklyEarnings = deposits.reduce((sum, deposit) => sum + deposit.amount, 0);
  return totalWeeklyEarnings / deposits.length;
}

/**
 * Create prompt for final financial analysis
 */
function createFinalAnalysisPrompt(customerId: string, weeklyEarnings: number, aggregatedData: any): string {
  return `
    - You are analyzing aggregated financial data from multiple bank statements. 
    Provide a comprehensive financial assessment based on this data.
    - Don't be too harsh in assessment as this analysis is supposed to be for workers of gig economy in mexico,
    deriving most of their revenue from ride hailing services like uber/didi/indrive etc.
    - Keep explanations/summary short and informative
    - Lesser debt means higher score, higher earnings means higher score, higher cashflow means higher score
    
    Customer ID: ${customerId}
    Provided weekly earnings: ${weeklyEarnings}
    
    Base your analysis on this aggregated data:
    ${JSON.stringify(aggregatedData, null, 2)}
    
    Return a complete financial assessment in the exact JSON format below:
    {
        "customer_id": "${customerId}",
        "earnings_cross_check": {
          "score": 0-100,
          "provided_earnings": ${weeklyEarnings},
          "calculated_earnings": ${aggregatedData.earnings_cross_check.calculated_earnings},
          "explanation": "explanation for why the earnings cross check score is what it is"
        },
        "cashflow_analysis": {
          "score": 0-100,
          "total_inflows": ${JSON.stringify(aggregatedData.cashflow_analysis.total_inflows)},
          "total_inflow": 0.0,
          "total_outflows": ${JSON.stringify(aggregatedData.cashflow_analysis.total_outflows)},
          "total_outflow": 0.0,
          "net_cashflow": 0.0,
          "explanation": "explanation for why the cashflow analysis score is what it is"
        },
        "bank_balance": ${JSON.stringify(aggregatedData.bank_balance)},
        "debt_liabilities": {
          "score": 0-100,
          "amount": 0.0,
          "sources": ["source1", "source2"],
          "explanation": "explanation for why the debt score is what it is"
        },
        "overall_score": 0-100,
        "summary": "short financial health summary of the customer",
    }
      `;
}

/**
 * Calculate standard deviation of an array
 */
function calculateStandardDeviation(values: number[]): number {
  if (values.length <= 1) return 0;

  // Calculate mean
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;

  // Calculate squared differences
  const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));

  // Calculate variance
  const variance = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;

  // Return standard deviation
  return Math.sqrt(variance);
}

/**
 * Perform final financial analysis using Gemini API
 */
async function performFinalAnalysisWithGemini(
  aggregatedData: any,
  weeklyEarnings: number,
  customerId: string
): Promise<any> {
  try {
    logger.info(`[performFinalAnalysisWithGemini] Starting final analysis for request ${customerId}`);

    const prompt = createFinalAnalysisPrompt(customerId, weeklyEarnings, aggregatedData);

    // Configure generation parameters
    const generationConfig = {
      temperature: 0,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: GEMINI_MAX_OUTPUT_TOKENS,
    };

    // Call Gemini API using the helper function
    const jsonResult = await generateTextContent(prompt, generationConfig);

    // Validate response
    if (!jsonResult) {
      throw new Error('Empty or invalid JSON response from Gemini API helper');
    }

    logger.info(`[performFinalAnalysisWithGemini] Completed final analysis for request ${customerId}`);
    return jsonResult;
  } catch (error) {
    logger.error(`[performFinalAnalysisWithGemini] Error: ${error}`);
    throw new Error(`Failed to perform final analysis: ${error}`);
  }
}

/**
 * Calculate financial metrics from the analysis result
 */
function calculateFinancialMetrics(data: any): any {
  // Make a copy to avoid modifying the original
  const result = { ...data };

  // Safely extract values
  const totalInflows = Object.values(data.cashflow_analysis?.total_inflows || {}) as number[];
  const totalOutflows = Object.values(data.cashflow_analysis?.total_outflows || {}) as number[];

  const totalInflow = totalInflows.reduce((sum, val) => sum + (val || 0), 0);
  const totalOutflow = totalOutflows.reduce((sum, val) => sum + (val || 0), 0);
  const netCashflow = totalInflow - totalOutflow;

  // Savings Rate - protect against division by zero
  const savingsRate = totalInflow > 0 ? (netCashflow / totalInflow) * 100 : 0;

  // Expense Ratio - protect against division by zero
  const expenseRatio = totalInflow > 0 ? (totalOutflow / totalInflow) * 100 : 0;

  // Debt-to-Income Ratio - handle missing data and division by zero
  let debtPayments = 0;
  if (data.debt_liabilities?.amount) {
    debtPayments = data.debt_liabilities.amount;
  }

  const debtToIncomeRatio = totalInflow > 0 ? (debtPayments / totalInflow) * 100 : 0;

  // Income Volatility - handle empty arrays
  const incomeVolatility = totalInflows.length > 1 ? calculateStandardDeviation(totalInflows) : 0;

  // Liquidity Ratio - multiple division by zero protections
  const bankBalanceValues = Object.values(data.bank_balance || {}) as number[];

  // Handle empty arrays and division by zero
  const avgMonthlyExpense = totalOutflows.length > 0 ? totalOutflow / totalOutflows.length : 1;

  const avgBankBalance =
    bankBalanceValues.length > 0
      ? bankBalanceValues.reduce((sum, val) => sum + (val || 0), 0) / bankBalanceValues.length
      : 0;

  const liquidityRatio = avgMonthlyExpense > 0 ? avgBankBalance / avgMonthlyExpense : 0;

  // Add financial metrics to result
  result.financial_metrics = {
    savings_rate: parseFloat(savingsRate.toFixed(2)),
    expense_ratio: parseFloat(expenseRatio.toFixed(2)),
    debt_to_income_ratio: parseFloat(debtToIncomeRatio.toFixed(2)),
    income_volatility: parseFloat(incomeVolatility.toFixed(2)),
    liquidity_ratio: parseFloat(liquidityRatio.toFixed(2)),
  };

  return result;
}

/**
 * Analyze bank statements that need analysis
 */
async function analyzeBankStatements(
  documentsNeedingAnalysis: RequestDocument[],
  allDocuments: RequestDocument[],
  admissionRequest: AdmissionRequest
): Promise<{ analyzedDocuments: RequestDocument[] }> {
  logger.info(
    `[analyzeBankStatements] Analyzing ${documentsNeedingAnalysis.length} bank statements for request ${admissionRequest.id}`
  );

  // Create a copy of the documents array to avoid modifying the original
  const updatedDocuments = [...allDocuments];

  // Process documents in parallel using analyzeDocumentService
  const analysisResults = await Promise.all(
    documentsNeedingAnalysis.map((doc) => analyzeDocumentService(doc, admissionRequest))
  );

  // Update document analysis results
  for (const analysisResult of analysisResults) {
    const docIndex = updatedDocuments.findIndex((doc) => doc.type === analysisResult.documentType);
    if (docIndex >= 0) {
      updatedDocuments[docIndex].analysisResult = analysisResult.result;
      logger.info(
        `[analyzeBankStatements] Updated analysis for document type ${analysisResult.documentType}`
      );
    }
  }

  // Save the analysis results to the database
  const documentResults = updatedDocuments.map((doc) => ({
    documentType: doc.type,
    result: doc.analysisResult as DocumentAnalysisResult,
  }));

  await repoSaveDocumentsResults(admissionRequest.id!, documentResults);
  logger.info(`[analyzeBankStatements] Saved analysis results for request ${admissionRequest.id}`);

  return { analyzedDocuments: updatedDocuments };
}

/**
 * Prepare financial data by analyzing bank statements and calculating weekly earnings
 */
async function prepareFinancialData(admissionRequest: AdmissionRequest): Promise<{
  updatedDocuments: RequestDocument[];
  weeklyEarnings: number;
}> {
  // Calculate weekly earnings from earnings analysis (same as getFinancialAssessmentBody)
  const earnings = admissionRequest.earningsAnalysis?.earnings;
  const validEarnings = earnings?.filter((earning) => earning?.totalAmount !== undefined) ?? [];
  const weeklyEarnings =
    validEarnings.length > 0
      ? validEarnings.reduce((sum, earning) => sum + earning.totalAmount, 0) / validEarnings.length
      : 0;

  // Identify all bank statement documents (more flexible matching)
  const bankStatementDocuments =
    admissionRequest.documentsAnalysis.documents?.filter(
      (doc) => doc.type.includes('bank_statement') && doc.mediaId
    ) || [];

  logger.info(
    `[prepareFinancialData] Found ${bankStatementDocuments.length} bank statements for request ${admissionRequest.id}`
  );

  // Check which documents need analysis
  const documentsNeedingAnalysis = bankStatementDocuments.filter((doc) => {
    return (
      !doc.analysisResult ||
      !doc.analysisResult.extractedData ||
      Object.keys(doc.analysisResult.extractedData).length === 0
    );
  });

  let updatedDocuments = [...bankStatementDocuments];

  // Analyze documents in parallel if needed
  if (documentsNeedingAnalysis.length > 0) {
    const { analyzedDocuments } = await analyzeBankStatements(
      documentsNeedingAnalysis,
      updatedDocuments,
      admissionRequest
    );
    updatedDocuments = analyzedDocuments;
  }

  return { updatedDocuments, weeklyEarnings };
}

/**
 * Aggregate financial data from bank statements
 */
function aggregateFinancialData(
  documents: RequestDocument[],
  customerId: string,
  weeklyEarnings: number
): any {
  // Initialize aggregated data structure
  const aggregatedData = {
    customer_id: customerId,
    earnings_cross_check: {
      provided_earnings: weeklyEarnings,
      deposits: [] as Array<{ period: string; amount: number }>,
      analysis: '',
      calculated_earnings: 0,
    },
    cashflow_analysis: {
      total_inflows: {} as Record<string, number>,
      total_outflows: {} as Record<string, number>,
    },
    bank_balance: {} as Record<string, number>,
    debt_liabilities: { sources: [] as string[], payments: [] as Array<{ period: string; amount: number }> },
  };

  // Assign period names to bank statements
  const periodNames = ['statement1', 'statement2', 'statement3'];

  // Process each bank statement document
  for (let i = 0; i < documents.length; i++) {
    const doc = documents[i];
    const period = i < periodNames.length ? periodNames[i] : `statement${i + 1}`;

    if (doc.analysisResult?.extractedData) {
      const extractedData = doc.analysisResult.extractedData;

      // Aggregate inflows and outflows
      aggregatedData.cashflow_analysis.total_inflows[period] = extractedData.total_inflow || 0;
      aggregatedData.cashflow_analysis.total_outflows[period] = extractedData.total_outflow || 0;

      // Aggregate bank balances
      aggregatedData.bank_balance[period] = extractedData.end_balance || 0;

      // Process debt payments
      const debtPayments = extractedData.debt_payments || [];
      let totalDebt = 0;

      for (const debt of debtPayments) {
        const source = debt.source || '';
        if (source && !aggregatedData.debt_liabilities.sources.includes(source)) {
          aggregatedData.debt_liabilities.sources.push(source);
        }
        totalDebt += debt.amount || 0;
      }

      aggregatedData.debt_liabilities.payments.push({
        period,
        amount: totalDebt,
      });

      // Process earnings deposits
      const earningsDeposits = extractedData.earnings_deposits || [];
      let totalEarnings = 0;

      for (const deposit of earningsDeposits) {
        totalEarnings += deposit.amount || 0;
      }

      // Only append if there are deposits for this period
      if (totalEarnings > 0) {
        aggregatedData.earnings_cross_check.deposits.push({
          period,
          amount: totalEarnings / 4, // Convert monthly to weekly
        });
      }
    }
  }

  // Calculate weekly earnings from deposits
  aggregatedData.earnings_cross_check.calculated_earnings = calculateWeeklyEarnings(
    aggregatedData.earnings_cross_check.deposits
  );

  return aggregatedData;
}

/**
 * Perform financial analysis using the aggregated data
 */
async function performFinancialAnalysis(
  aggregatedData: any,
  weeklyEarnings: number,
  customerId: string
): Promise<any> {
  // Perform final analysis using Gemini API
  const finalResult = await performFinalAnalysisWithGemini(aggregatedData, weeklyEarnings, customerId);

  // Calculate and add financial metrics
  return calculateFinancialMetrics(finalResult);
}

/**
 * Construct the final model result
 */
function constructFinancialModelResult(enhancedResult: any): ModelResult {
  // Map the feature scores for the response
  const featureScores = {
    earnings_cross_check: enhancedResult.earnings_cross_check.score,
    cashflow_analysis: enhancedResult.cashflow_analysis.score,
    debt_liabilities: enhancedResult.debt_liabilities.score,
  };

  const finalScore = calculateWeightedScore(FINANCIAL_ASSESSMENT_WEIGHTS, featureScores);

  // Return the model result
  return {
    modelName: MLModels.FINANCIAL_ASSESSMENT,
    status: AnalysisStatus.completed,
    modelScore: finalScore,
    modelFeatureScores: featureScores,
    modelResult: {
      customer_id: enhancedResult.customer_id,
      earnings_cross_check: enhancedResult.earnings_cross_check,
      cashflow_analysis: enhancedResult.cashflow_analysis,
      bank_balance: enhancedResult.bank_balance,
      debt_liabilities: enhancedResult.debt_liabilities,
      summary: enhancedResult.summary,
      financial_metrics: enhancedResult.financial_metrics || {},
    },
    modelWeights: FINANCIAL_ASSESSMENT_WEIGHTS,
  };
}

/**
 * Main entry point for financial assessment
 */
export async function performFinancialAssessment(admissionRequest: AdmissionRequest): Promise<ModelResult> {
  try {
    logger.info(
      `[performFinancialAssessment] Starting financial assessment for request ${admissionRequest.id}`
    );

    // Step 1: Analyze bank statements if needed
    const { updatedDocuments, weeklyEarnings } = await prepareFinancialData(admissionRequest);

    // Step 2: Aggregate financial data
    const aggregatedData = aggregateFinancialData(updatedDocuments, admissionRequest.id!, weeklyEarnings);

    // Step 3: Perform final analysis and calculate financial metrics
    const finalAnalysis = await performFinancialAnalysis(
      aggregatedData,
      weeklyEarnings,
      admissionRequest.id!
    );

    // Step 4: Construct and return the model result
    const result = constructFinancialModelResult(finalAnalysis);

    logger.info(
      `[performFinancialAssessment] Completed financial assessment for request ${admissionRequest.id}`
    );

    return result;
  } catch (error: any) {
    logger.error(`[performFinancialAssessment] Error: ${error}`);
    throw error;
  }
}
