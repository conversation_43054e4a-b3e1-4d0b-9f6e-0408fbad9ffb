import { AdmissionRequest } from '@/clean/domain/entities';
import { AdmissionRequestAdditionalDocumentType, AdmissionRequestDocumentType } from '@/clean/domain/enums';
import { formatAddress, getWeeklyEarnings } from '../../lib/helpers';

/**
 * Generate a prompt for document analysis based on document type
 * Provides necessary context from admission request for validation
 */
export const getDocumentPrompt = (documentType: string, admissionRequest: AdmissionRequest): string => {
  const basePrompt = 'Extract all relevant information from this document and return it as JSON. ';
  const personalData = admissionRequest.personalData || {};

  // Context data from the admission request to provide to the LLM for validation
  const expectedName = `${personalData.firstName || ''} ${personalData.lastName || ''}`.trim();
  const expectedCurp = personalData.nationalId || '';
  const expectedDob = personalData.birthdate || '';
  const expectedAddress = personalData
    ? formatAddress({
        postalCode: personalData.postalCode ?? undefined,
        city: personalData.city ?? undefined,
        state: personalData.state ?? undefined,
        neighborhood: personalData.neighborhood ?? undefined,
        street: personalData.street ?? undefined,
        streetNumber: personalData.streetNumber ?? undefined,
        department: personalData.department ?? undefined,
        country: personalData.country ?? undefined,
      })
    : '';

  // Get weekly earnings data if available for bank statement validation
  const weeklyEarning = getWeeklyEarnings(admissionRequest);

  // Current date for validating document expiration
  const today = new Date();
  const threeMonthsAgo = new Date();
  threeMonthsAgo.setMonth(today.getMonth() - 3);
  const formattedDate = today.toISOString().split('T')[0];
  const formattedThreeMonthsAgo = threeMonthsAgo.toISOString().split('T')[0];

  switch (documentType) {
    case AdmissionRequestDocumentType.identity_card_front:
      return `${basePrompt} This is a Mexican INE (front). 
        
Extract the full name, address, CURP, birthday, and expiration date. 

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations):
- Expected name: "${expectedName}"
- Expected CURP: "${expectedCurp}"
- Expected birthday: "${expectedDob}"
- Today's date: "${formattedDate}" (for expiration check)
- Expected address: "${expectedAddress}"

Format JSON response like: 
{
  "fullName": "",
  "address": "",
  "curp": "",
  "birthday": "YYYY-MM-DD",
  "expirationDate": "YYYY-MM-DD",
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "curpMatches": true/false, 
    "birthdayMatches": true/false,
    "isExpired": true/false,
    "addressMatches": true/false
  }
}`;

    case AdmissionRequestDocumentType.identity_card_back:
      return `${basePrompt} This is a Mexican INE (back). Extract the IDMEX number and any other relevant information. 
        
Format JSON response like: 
{
  "idMex": "",
  "otherInfo": "",
  "validation": {
    "hasValidIdMex": true/false
  }
}`;

    case AdmissionRequestDocumentType.proof_of_address:
      return `${basePrompt} This is a proof of address document. 
        
Extract the full name, complete address, and document date.

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations, 
or slight difference in the expected and actual address, even 50% match is acceptable for address as it is supposed to be fuzzy matching):
- Expected name: "${expectedName}"
- Expected address: "${expectedAddress}"
- Document should be issued within the last 3 months (after ${formattedThreeMonthsAgo})
- Today's date: "${formattedDate}"

Format JSON response like: 
{
  "name": "",
  "address": "",
  "documentDate": "YYYY-MM-DD",
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "addressMatches": true/false,
    "addressMatchConfidence": 0-1.0,
    "isWithinThreeMonths": true/false
  }
}`;

    case AdmissionRequestDocumentType.bank_statement_month_1:
    case AdmissionRequestDocumentType.bank_statement_month_2:
    case AdmissionRequestDocumentType.bank_statement_month_3:
      return `${basePrompt} This is a bank statement for a gig economy worker in Mexico. 

Extract the following key financial information:
- Account holder name
- Statement start and end dates
- Total deposits/inflows
- Total withdrawals/outflows
- End-of-period bank balance
- Identified debt payments (specify source and amount), make sure the payment is clearly identifiable as a debt payment, not a regular bill payment or purchase
  Some identifiable lenders include bank loans, 'ID Finance', 'Lana', 'Bankuish', 'Mercado Pago', 'PayJoy', 'Heru', 'Kueski', and similar sources
- Earnings deposits from ride-sharing services like Uber/Didi/InDrive(deposits from accounts mentioning the names of these service or cash deposits from any account even other than these as they are often deposited via cash from third party deposit shops too.)

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations):
- Expected account holder name: "${expectedName}"
- Reported weekly earnings: ${weeklyEarning} MXN (to compare with deposits)
- Document should be issued within the last 3 months, compare with statementEndDate (after ${formattedThreeMonthsAgo})

Format JSON response exactly like: 
{
  "accountName": "",
  "statementStartDate": "YYYY-MM-DD",
  "statementEndDate": "YYYY-MM-DD", 
  "total_inflow": 0.0,
  "total_outflow": 0.0,
  "end_balance": 0.0,
  "debt_payments": [
    {"source": "name", "amount": 0.0}
  ],
  "earnings_deposits": [
    {"source": "name", "date": "YYYY-MM-DD", "amount": 0.0}
  ],
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "isRecentStatement": true/false,
    "expensesExceedEarnings": true/false,
    "depositsMatchPlatformEarnings": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.proof_of_tax_situation:
      return `${basePrompt} This is a tax situation document from Mexico (Constancia de Situación Fiscal).

Extract the date, RFC, business/personal name, postal code, and tax regime.

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations):
- Expected name: "${expectedName}"
- RFC format validation: It should be a valid RFC format (13 characters for individuals, 12 for businesses)

Format JSON response like: 
{
  "date": "YYYY-MM-DD",
  "rfc": "",
  "name": "",
  "postalCode": "",
  "taxRegime": "",
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "rfcIsValid": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.drivers_license_front:
      return `${basePrompt} This is a driver's license (front).
        
Extract the full name, expiration date, type of license, and issuing state.

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations):
- Expected name: "${expectedName}"
- Today's date: "${formattedDate}" (for expiration check)
- Valid license types for standard vehicles: A, B, or C

Format JSON response like: 
{
  "name": "",
  "expirationDate": "YYYY-MM-DD",
  "licenseType": "",
  "state": "",
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "isExpired": true/false,
    "isValidLicenseType": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.drivers_license_back:
      return `${basePrompt} This is a driver's license (back).
        
Extract any relevant information from the back of the license.

Format JSON response like: 
{
  "additionalInfo": "",
  "restrictions": ""
}`;

    case AdmissionRequestAdditionalDocumentType.selfie_photo:
      return `You are analyzing a selfie photo and comparing it with the INE (ID card) photo.
        
Compare the face in the selfie with the face in the INE photo. The first image is the selfie, and the second image is the INE photo.

Check for:
1. Whether the face is clearly visible in the selfie
2. Whether the selfie is of good quality (well-lit, clear, not blurry)
3. Whether the face in the selfie matches the face in the INE photo

Format JSON response like: 
{
  "validation": {
    "isGoodQualityPhoto": true/false,
    "faceVisible": true/false,
    "faceMatchesINE": true/false,
    "faceMatchConfidence": 0-1.0
  }
}`;

    case AdmissionRequestAdditionalDocumentType.garage_photo:
      return `${basePrompt} This is a photo of a garage where the vehicle will be stored.
        
Validate the garage's suitability for vehicle storage.

Format JSON response like: 
{
  "validation": {
    "isSufficientSize": true/false,
    "hasAdequateProtection": true/false,
    "isSuitableForVehicle": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front:
      return `${basePrompt} This is a guarantor's Mexican INE (front).
        
Extract the full name, address, CURP, birthday, and expiration date.

Also validate the following information:
- Today's date: "${formattedDate}" (for expiration check)

Format JSON response like: 
{
  "fullName": "",
  "address": "",
  "curp": "",
  "birthday": "YYYY-MM-DD",
  "expirationDate": "YYYY-MM-DD",
  "validation": {
    "isExpired": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back:
      return `${basePrompt} This is a guarantor's Mexican INE (back).
        
Extract the IDMEX number and any other relevant information.

Format JSON response like: 
{
  "idMex": "",
  "otherInfo": "",
  "validation": {
    "hasValidIdMex": true/false
  }
}`;

    case AdmissionRequestAdditionalDocumentType.curp:
      return `${basePrompt} This is a CURP document.
        
Extract the full name and CURP number.

Also validate the following information (be lenient with minor differences like typos, accents, or abbreviations):
- Expected name: "${expectedName}"
- Expected CURP: "${expectedCurp}"

Format JSON response like: 
{
  "name": "",
  "curp": "",
  "validation": {
    "nameMatches": true/false,
    "nameMatchConfidence": 0-1.0,
    "curpMatches": true/false
  }
}`;

    default:
      return `${basePrompt} Extract all text and relevant information from this document into a structured JSON format.`;
  }
};
