import { logger } from '@/clean/lib/logger';
import {
  AllowedMimeType,
  parseTextFromSource,
  parseTextFromMultipleSources,
} from '@/services/ocr/llmClients/geminiClient';
import { repoGetDocumentData } from '@/clean/data/s3Repositories';
import { AdmissionRequest, RequestDocument, DocumentAnalysisResult } from '@/clean/domain/entities';
import {
  DocumentAnalysisStatus,
  AdmissionRequestAdditionalDocumentType,
  AdmissionRequestDocumentType,
} from '@/clean/domain/enums';
import { getDocumentPrompt } from './prompts';
import { processLlmValidation } from './validations';

/**
 * Find INE front document from admission request
 */
const findIneDocument = (admissionRequest: AdmissionRequest): RequestDocument | null => {
  const documents = admissionRequest.documentsAnalysis.documents || [];
  return (
    documents.find((doc) => doc.type === AdmissionRequestDocumentType.identity_card_front && doc.media) ||
    null
  );
};

/**
 * Analyze a document using LLM and return extracted information with validation
 */
export const analyzeDocumentService = async (
  documentMedia: RequestDocument,
  admissionRequest: AdmissionRequest
): Promise<{ documentType: string; result: DocumentAnalysisResult }> => {
  try {
    logger.info(
      `[analyzeDocument] Processing document ${documentMedia.type} for request ${admissionRequest.id}`
    );

    // Create the appropriate prompt based on document type, now including validation requirements
    const prompt = getDocumentPrompt(documentMedia.type, admissionRequest);

    // Get the document data
    const documentData = await repoGetDocumentData(documentMedia.media!.path);

    // Ensure we have valid document data
    if (!documentData) {
      throw new Error(`Could not retrieve data for document ${documentMedia.type}`);
    }

    // Special handling for selfie image that requires comparison with INE
    if (documentMedia.type === AdmissionRequestAdditionalDocumentType.selfie_photo) {
      const ineDocument = findIneDocument(admissionRequest);

      if (!ineDocument || !ineDocument.media) {
        logger.warn(
          `[analyzeDocument] Cannot validate selfie against INE for request ${admissionRequest.id} - INE document not found`
        );
        // Continue with single image processing but note the lack of comparison
        const source = {
          data: documentData,
          media_type: documentMedia.media!.mimeType as AllowedMimeType,
          prompt: prompt + '\nNote: INE document not available for comparison.',
          filename: documentMedia.media!.fileName,
        };

        const extractedData = await parseTextFromSource(source);

        // Add warning about missing comparison
        const validationResult = processLlmValidation(
          documentMedia.type,
          extractedData,
          admissionRequest.personalData.country
        );
        validationResult.warnings.push('Unable to compare with INE photo - INE document not found');
        validationResult.confidence *= 0.7; // Reduce confidence due to missing comparison

        return {
          documentType: documentMedia.type,
          result: {
            status: validationResult.isValid ? DocumentAnalysisStatus.valid : DocumentAnalysisStatus.invalid,
            extractedData,
            validationErrors: validationResult.errors,
            validationWarnings: validationResult.warnings,
            confidence: validationResult.confidence,
          },
        };
      }

      // Get INE image data
      const ineImageData = await repoGetDocumentData(ineDocument.media.path);

      if (!ineImageData) {
        logger.warn(
          `[analyzeDocument] Cannot validate selfie against INE for request ${admissionRequest.id} - INE image data not found`
        );
        // Continue with single image processing
        const source = {
          data: documentData,
          media_type: documentMedia.media!.mimeType as AllowedMimeType,
          prompt: prompt + '\nNote: INE document data not available for comparison.',
          filename: documentMedia.media!.fileName,
        };

        const extractedData = await parseTextFromSource(source);

        // Add warning about missing comparison
        const validationResult = processLlmValidation(
          documentMedia.type,
          extractedData,
          admissionRequest.personalData.country
        );
        validationResult.warnings.push('Unable to compare with INE photo - INE image data not available');
        validationResult.confidence *= 0.7; // Reduce confidence due to missing comparison

        return {
          documentType: documentMedia.type,
          result: {
            status: validationResult.isValid ? DocumentAnalysisStatus.valid : DocumentAnalysisStatus.invalid,
            extractedData,
            validationErrors: validationResult.errors,
            validationWarnings: validationResult.warnings,
            confidence: validationResult.confidence,
          },
        };
      }

      // Process with both images
      logger.info(
        `[analyzeDocument] Processing selfie with INE comparison for request ${admissionRequest.id}`
      );

      const sources = [
        {
          data: documentData,
          media_type: documentMedia.media!.mimeType as AllowedMimeType,
          filename: documentMedia.media!.fileName,
        },
        {
          data: ineImageData,
          media_type: ineDocument.media.mimeType as AllowedMimeType,
          filename: ineDocument.media.fileName,
        },
      ];

      // Call Gemini API with multiple images
      const extractedData = await parseTextFromMultipleSources(sources, prompt);

      // Process the LLM validation results
      const validationResult = processLlmValidation(
        documentMedia.type,
        extractedData,
        admissionRequest.personalData.country
      );

      logger.info(
        `[analyzeDocument] Completed selfie validation with INE comparison for request ${admissionRequest.id}`
      );

      return {
        documentType: documentMedia.type,
        result: {
          status: validationResult.isValid ? DocumentAnalysisStatus.valid : DocumentAnalysisStatus.invalid,
          extractedData,
          validationErrors: validationResult.errors,
          validationWarnings: validationResult.warnings,
          confidence: validationResult.confidence,
        },
      };
    }

    // Standard processing for other document types
    const source = {
      data: documentData,
      media_type: documentMedia.media!.mimeType as AllowedMimeType,
      prompt,
      filename: documentMedia.media!.fileName,
    };

    // Call Gemini API to extract document data
    const extractedData = await parseTextFromSource(source);

    // Log the extracted data for debugging
    logger.info(
      `[analyzeDocument] Document ${documentMedia.type} for request ${
        admissionRequest.id
      } - Extracted data validation: ${
        extractedData?.validation ? JSON.stringify(extractedData.validation) : 'No validation data'
      }`
    );

    // Process the LLM validation results
    const validationResult = processLlmValidation(
      documentMedia.type,
      extractedData,
      admissionRequest.personalData.country
    );

    // Log validation results
    if (!validationResult.isValid) {
      const errorString = validationResult.errors.join('; ');
      logger.warn(
        `[analyzeDocument] Validation failed for ${documentMedia.type}, request ${admissionRequest.id}: ${errorString}`
      );
    }

    if (validationResult.warnings.length > 0) {
      const warningString = validationResult.warnings.join('; ');
      logger.info(
        `[analyzeDocument] Validation warnings for ${documentMedia.type}, request ${admissionRequest.id}: ${warningString}`
      );
    }

    return {
      documentType: documentMedia.type,
      result: {
        status: validationResult.isValid ? DocumentAnalysisStatus.valid : DocumentAnalysisStatus.invalid,
        extractedData,
        validationErrors: validationResult.errors,
        validationWarnings: validationResult.warnings,
        confidence: validationResult.confidence,
      },
    };
  } catch (error: any) {
    logger.error(
      `[analyzeDocument] Error analyzing document ${documentMedia.type} for admission request ${admissionRequest.id}`,
      error
    );

    // Return an error result
    return {
      documentType: documentMedia.type,
      result: {
        status: DocumentAnalysisStatus.error,
        extractedData: {},
        validationErrors: [error.message],
        validationWarnings: [],
        confidence: 0,
      },
    };
  }
};

/**
 * Analyze multiple documents in parallel
 */
export const analyzeMultipleDocuments = async (
  documents: RequestDocument[],
  admissionRequest: AdmissionRequest
): Promise<{ documentType: string; result: DocumentAnalysisResult }[]> => {
  try {
    logger.info(
      `[analyzeMultipleDocuments] Processing ${documents.length} documents for request ${admissionRequest.id}`
    );

    // Verify all documents are in pending review status
    const invalidDocuments = documents.filter(
      (doc) =>
        doc.analysisResult?.status === DocumentAnalysisStatus.valid ||
        doc.analysisResult?.status === DocumentAnalysisStatus.invalid
    );
    if (invalidDocuments.length > 0) {
      const invalidTypes = invalidDocuments
        .map((doc) => `${doc.type} (status: ${doc.status || 'undefined'})`)
        .join(', ');
      logger.error(
        `[analyzeMultipleDocuments] Some documents for request ${admissionRequest.id} are not in pending review status: ${invalidTypes}`
      );
      throw new Error(
        `Some documents for request ${admissionRequest.id} are not in pending review status: ${invalidTypes}`
      );
    }

    // Use Promise.all for parallel processing
    const results = await Promise.all(documents.map((doc) => analyzeDocumentService(doc, admissionRequest)));

    return results;
  } catch (error) {
    logger.error(
      `[analyzeMultipleDocuments] Error processing documents for admission request ${admissionRequest.id}`,
      error
    );
    throw error;
  }
};
