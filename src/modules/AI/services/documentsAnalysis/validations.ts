import {
  AdmissionRequestDocumentType,
  AdmissionRequestAdditionalDocumentType,
  Country,
} from '@/clean/domain/enums';

// Define validation messages for different languages
const validationMessages = {
  en: {
    // Identity card front
    nameNotMatch: 'Name on ID does not match application',
    curpNotMatch: 'CURP on ID does not match application',
    birthdayNotMatch: 'Birthday on ID does not match application',
    idExpired: 'ID is expired',
    addressNotMatch: 'Address on ID does not match application',
    lowNameMatchConfidence: 'Low confidence in name match',

    // Identity card back
    invalidIdMex: 'ID MEX number is invalid or missing',

    // Proof of address
    addressProofNotMatch: 'Address on proof of address does not match application address',
    addressProofTooOld: 'Proof of address is older than 3 months',
    addressProofNameNotMatch: 'Name on proof of address does not match application',

    // Bank statements
    accountNameNotMatch: 'Account name does not match applicant name',
    noRideshareDeposits: 'No ride-sharing deposits found in statement',
    notRecentStatement: 'Bank statement is not from recent period',
    expensesExceedEarnings: 'Expenses exceed earnings in statement',
    depositsNotMatchEarnings: 'Deposits do not match declared platform earnings',

    // Tax situation
    taxNameNotMatch: 'Name on tax document does not match application',
    invalidRfc: 'Invalid RFC format on tax document',

    // Driver's license
    licenseNameNotMatch: 'Name on license does not match application',
    invalidLicenseType: 'License type is not appropriate for driving (A, B, or C required)',
    licenseExpired: "Driver's license is expired",

    // Selfie
    faceNotVisible: 'Face not clearly visible in selfie',
    faceNotMatchIne: 'Face in selfie does not match the face in INE photo',
    lowQualitySelfie: 'Low quality selfie photo',
    lowFaceMatchConfidence: 'Low confidence in face match',

    // Garage
    garageTooSmall: 'Garage may not be sufficiently sized for vehicle',
    garageInadequateProtection: 'Garage may not provide adequate protection for vehicle',
    garageNotSuitable: 'Garage is not suitable for vehicle storage',

    // Solidarity obligor
    guarantorIdExpired: 'Guarantor ID is expired',
    guarantorInvalidIdMex: 'Guarantor ID MEX number is invalid or missing',

    // CURP
    curpNameNotMatch: 'Name on CURP document does not match application',
    curpNumberNotMatch: 'CURP number does not match application',

    // Misc
    noValidation: 'No validation performed',
  },
  mx: {
    // Identity card front
    nameNotMatch: 'El nombre en la identificación no coincide con la solicitud',
    curpNotMatch: 'El CURP en la identificación no coincide con la solicitud',
    birthdayNotMatch: 'La fecha de nacimiento en la identificación no coincide con la solicitud',
    idExpired: 'La identificación está vencida',
    addressNotMatch: 'La dirección en la identificación no coincide con la solicitud',
    lowNameMatchConfidence: 'Baja confianza en la coincidencia del nombre',

    // Identity card back
    invalidIdMex: 'El número de ID MEX es inválido o falta',

    // Proof of address
    addressProofNotMatch:
      'La dirección en el comprobante de domicilio no coincide con la dirección de la solicitud',
    addressProofTooOld: 'El comprobante de domicilio tiene más de 3 meses de antigüedad',
    addressProofNameNotMatch: 'El nombre en el comprobante de domicilio no coincide con la solicitud',

    // Bank statements
    accountNameNotMatch: 'El nombre de la cuenta no coincide con el nombre del solicitante',
    noRideshareDeposits: 'No se encontraron depósitos de plataformas de transporte en el estado de cuenta',
    notRecentStatement: 'El estado de cuenta no es del período reciente',
    expensesExceedEarnings: 'Los gastos superan los ingresos en el estado de cuenta',
    depositsNotMatchEarnings: 'Los depósitos no coinciden con los ingresos declarados de la plataforma',

    // Tax situation
    taxNameNotMatch: 'El nombre en el documento fiscal no coincide con la solicitud',
    invalidRfc: 'Formato de RFC inválido en el documento fiscal',

    // Driver's license
    licenseNameNotMatch: 'El nombre en la licencia no coincide con la solicitud',
    invalidLicenseType: 'El tipo de licencia no es apropiado para conducir (se requiere A, B o C)',
    licenseExpired: 'La licencia de conducir está vencida',

    // Selfie
    faceNotVisible: 'El rostro no es claramente visible en la selfie',
    faceNotMatchIne: 'El rostro en la selfie no coincide con la foto en la INE',
    lowQualitySelfie: 'Foto selfie de baja calidad',
    lowFaceMatchConfidence: 'Baja confianza en la coincidencia facial',

    // Garage
    garageTooSmall: 'El garaje puede no tener el tamaño suficiente para el vehículo',
    garageInadequateProtection: 'El garaje puede no proporcionar protección adecuada para el vehículo',
    garageNotSuitable: 'El garaje no es adecuado para almacenar el vehículo',

    // Solidarity obligor
    guarantorIdExpired: 'La identificación del obligado solidario está vencida',
    guarantorInvalidIdMex: 'El número de ID MEX del obligado solidario es inválido o falta',

    // CURP
    curpNameNotMatch: 'El nombre en el documento CURP no coincide con la solicitud',
    curpNumberNotMatch: 'El número CURP no coincide con la solicitud',

    // Misc
    noValidation: 'No se realizó validación',
  },
};

/**
 * Process the LLM validation results into our system's format
 * Separates errors (hard validations) from warnings (soft validations)
 */
export const processLlmValidation = (
  documentType: string,
  extractedData: any,
  country: keyof typeof Country | null | undefined = 'mx'
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  confidence: number;
} => {
  // Default state
  let isValid = true;
  const errors: string[] = [];
  const warnings: string[] = [];
  let confidence = 0.9; // Start with high confidence, adjust down as needed

  // Default to 'mx' if country is null or undefined, otherwise use 'en' for non-mx countries
  const languageKey = !country || country === 'mx' ? 'mx' : 'en';
  const messages = validationMessages[languageKey];

  // If no validation field, return with default (this shouldn't happen)
  if (!extractedData.validation) {
    return { isValid, errors, warnings, confidence: confidence - 0.1 };
  }

  const validation = extractedData.validation;

  // Process document-specific validations based on CSV requirements
  switch (documentType) {
    case AdmissionRequestDocumentType.identity_card_front:
      // Hard validations (rejection points)
      if (validation.nameMatches === false) {
        errors.push(messages.nameNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.curpMatches === false) {
        errors.push(messages.curpNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.birthdayMatches === false) {
        errors.push(messages.birthdayNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.isExpired === true) {
        errors.push(messages.idExpired);
        confidence -= 0.2;
        isValid = false;
      }
      // Soft validations
      if (validation.addressMatches === false) {
        warnings.push(messages.addressNotMatch);
        confidence -= 0.1;
      }
      if (validation.nameMatchConfidence && validation.nameMatchConfidence < 0.7) {
        warnings.push(messages.lowNameMatchConfidence);
        confidence -= 0.1;
      }
      break;

    case AdmissionRequestDocumentType.identity_card_back:
      // Hard validation
      if (validation.hasValidIdMex === false) {
        errors.push(messages.invalidIdMex);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestDocumentType.proof_of_address:
      // Hard validations
      if (validation.addressMatches === false) {
        errors.push(messages.addressProofNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.isWithinThreeMonths === false) {
        errors.push(messages.addressProofTooOld);
        confidence -= 0.2;
        isValid = false;
      }
      // Soft validation
      if (validation.nameMatches === false) {
        warnings.push(messages.addressProofNameNotMatch);
        confidence -= 0.1;
      }
      if (validation.nameMatchConfidence && validation.nameMatchConfidence < 0.7) {
        warnings.push(messages.lowNameMatchConfidence);
        confidence -= 0.1;
      }
      break;

    case AdmissionRequestDocumentType.bank_statement_month_1:
    case AdmissionRequestDocumentType.bank_statement_month_2:
    case AdmissionRequestDocumentType.bank_statement_month_3:
      // Hard validations
      if (validation.nameMatches === false) {
        errors.push(messages.accountNameNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      // Check earnings_deposits array instead of validation.hasRideshareDeposits
      if (
        !extractedData.earnings_deposits ||
        !Array.isArray(extractedData.earnings_deposits) ||
        extractedData.earnings_deposits.length === 0
      ) {
        errors.push(messages.noRideshareDeposits);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.isRecentStatement === false) {
        errors.push(messages.notRecentStatement);
        confidence -= 0.2;
        isValid = false;
      }
      // Soft validations
      if (validation.expensesExceedEarnings === true) {
        warnings.push(messages.expensesExceedEarnings);
        confidence -= 0.1;
      }
      if (validation.depositsMatchPlatformEarnings === false) {
        warnings.push(messages.depositsNotMatchEarnings);
        confidence -= 0.1;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.proof_of_tax_situation:
      // Hard validations
      if (validation.nameMatches === false) {
        errors.push(messages.taxNameNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.rfcIsValid === false) {
        errors.push(messages.invalidRfc);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.drivers_license_front:
      // Hard validations
      if (validation.nameMatches === false) {
        errors.push(messages.licenseNameNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.isValidLicenseType === false) {
        errors.push(messages.invalidLicenseType);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.isExpired === true) {
        errors.push(messages.licenseExpired);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.selfie_photo:
      // Hard validation
      if (validation.faceVisible === false) {
        errors.push(messages.faceNotVisible);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.faceMatchesINE === false) {
        errors.push(messages.faceNotMatchIne);
        confidence -= 0.3;
        isValid = false;
      }
      // Soft validation
      if (validation.isGoodQualityPhoto === false) {
        warnings.push(messages.lowQualitySelfie);
        confidence -= 0.1;
      }
      if (validation.faceMatchConfidence && validation.faceMatchConfidence < 0.7) {
        warnings.push(
          `${messages.lowFaceMatchConfidence} (${Math.round(validation.faceMatchConfidence * 100)}%)`
        );
        confidence -= 0.15;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.garage_photo:
      // Soft validations
      if (validation.isSufficientSize === false) {
        warnings.push(messages.garageTooSmall);
        confidence -= 0.1;
      }
      if (validation.hasAdequateProtection === false) {
        warnings.push(messages.garageInadequateProtection);
        confidence -= 0.1;
      }
      // Hard validation
      if (validation.isSuitableForVehicle === false) {
        errors.push(messages.garageNotSuitable);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front:
      // Hard validation
      if (validation.isExpired === true) {
        errors.push(messages.guarantorIdExpired);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back:
      // Hard validation
      if (validation.hasValidIdMex === false) {
        errors.push(messages.guarantorInvalidIdMex);
        confidence -= 0.2;
        isValid = false;
      }
      break;

    case AdmissionRequestAdditionalDocumentType.curp:
      // Hard validations
      if (validation.nameMatches === false) {
        errors.push(messages.curpNameNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      if (validation.curpMatches === false) {
        errors.push(messages.curpNumberNotMatch);
        confidence -= 0.2;
        isValid = false;
      }
      break;
  }

  // Ensure confidence stays within 0-1 range
  confidence = Math.max(0, Math.min(1, confidence));

  return { isValid, errors, warnings, confidence };
};
