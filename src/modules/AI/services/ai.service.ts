import { logger } from '@/clean/lib/logger';
import { SOCIAL_SCORING_URL } from '@/constants/onboarding';
import { AdmissionRequest, ModelResult } from '@/clean/domain/entities';
import { MLModels, AnalysisStatus } from '@/clean/domain/enums';
import { getPreApprovalBody } from '../lib/helpers';

import { HOME_VISIT_WEIGHTS, PERSONAL_INFORMATION_WEIGHTS } from '../lib/weights';
import { performHomeVisitAnalysis } from './models/homeVisit.service';
import { performPersonalInfoAnalysis } from './models/personalInfo.service';
import { performFinancialAssessment } from './models/financialAssessment.service';

/**
 * Calculate Rideshare Performance score
 */
export const calculateRideshareScoreService = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    const body = await getPreApprovalBody(admissionRequest.id!);
    const response = await fetch(`${SOCIAL_SCORING_URL}/risk-scoring`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.SOCIAL_SCORING_API_SECRET}`,
      },
      body: JSON.stringify(body),
    });
    const data = await response.json();
    if (response.status !== 200) {
      throw new Error(data.message);
    }
    const { score, feature_scores: featureScores, model_result: modelResult, weights } = data;
    return {
      modelName: MLModels.RIDESHARE_PERFORMANCE,
      status: AnalysisStatus.completed,
      modelScore: score * 100,
      modelFeatureScores: featureScores,
      modelResult: modelResult,
      modelWeights: weights,
    };
  } catch (error: any) {
    logger.error(
      `[calculateRideshareScoreService] Error calculating rideshare performance for admission request ${admissionRequest.id}`,
      error
    );
    return {
      modelName: MLModels.RIDESHARE_PERFORMANCE,
      status: AnalysisStatus.error,
      modelScore: 0,
      modelFeatureScores: {},
      modelResult: { message: error.message },
      modelWeights: {},
    };
  }
};

/**
 * Calculate Financial Assessment score
 */
export const calculateFinancialScoreService = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    // Use our specialized financial assessment service
    return await performFinancialAssessment(admissionRequest);
  } catch (error: any) {
    logger.error(
      `[calculateFinancialScoreService] Error performing financial assessment for admission request ${admissionRequest.id}`,
      error
    );
    return {
      modelName: MLModels.FINANCIAL_ASSESSMENT,
      status: AnalysisStatus.error,
      modelScore: 0,
      modelFeatureScores: {},
      modelResult: { message: error.message },
      modelWeights: {},
    };
  }
};

/**
 * Calculate Personal Information Analysis score
 */
export const calculatePersonalInformationScoreService = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    // Use our custom personal info analysis implementation instead of API call
    const { finalScore, individualScores, responseData } =
      await performPersonalInfoAnalysis(admissionRequest);

    // Format the response to match the expected structure
    return {
      modelName: MLModels.PERSONAL_INFORMATION,
      status: AnalysisStatus.completed,
      modelScore: finalScore,
      modelFeatureScores: individualScores,
      modelResult: responseData,
      modelWeights: PERSONAL_INFORMATION_WEIGHTS,
    };
  } catch (error: any) {
    logger.error(
      `[calculatePersonalInformationScoreService] Error performing personal info analysis for admission request ${admissionRequest.id}`,
      error
    );
    return {
      modelName: MLModels.PERSONAL_INFORMATION,
      status: AnalysisStatus.error,
      modelScore: 0,
      modelFeatureScores: {},
      modelResult: { message: error.message },
      modelWeights: {},
    };
  }
};

/**
 * Calculate Home Information Analysis score
 */
export const calculateHomeInformationScoreService = async (
  admissionRequest: AdmissionRequest
): Promise<ModelResult> => {
  try {
    // Use our custom home visit analysis implementation instead of API call
    const { finalScore, individualScores, responseData } = await performHomeVisitAnalysis(admissionRequest);

    // Format the response to match the expected structure
    return {
      modelName: MLModels.HOMEVISIT_INFORMATION,
      status: AnalysisStatus.completed,
      modelScore: finalScore,
      modelFeatureScores: individualScores,
      modelResult: responseData,
      modelWeights: HOME_VISIT_WEIGHTS,
    };
  } catch (error: any) {
    logger.error(
      `[calculateHomeInformationScoreService] Error performing home info analysis for admission request ${admissionRequest.id}`,
      error
    );

    return {
      modelName: MLModels.HOMEVISIT_INFORMATION,
      status: AnalysisStatus.error,
      modelScore: 0,
      modelFeatureScores: {},
      modelResult: { message: error.message },
      modelWeights: {},
    };
  }
};

/**
 * Calculate model score for a specific ML model
 * Returns both the model name and result to match expected interface
 */
export const calculateModelScoreService = async (
  modelName: MLModels,
  admissionRequest: AdmissionRequest
): Promise<{ modelName: MLModels; result: ModelResult }> => {
  try {
    let modelResult: ModelResult;

    switch (modelName) {
      case MLModels.RIDESHARE_PERFORMANCE:
        modelResult = await calculateRideshareScoreService(admissionRequest);
        break;
      case MLModels.FINANCIAL_ASSESSMENT:
        modelResult = await calculateFinancialScoreService(admissionRequest);
        break;
      case MLModels.HOMEVISIT_INFORMATION:
        modelResult = await calculateHomeInformationScoreService(admissionRequest);
        break;
      case MLModels.PERSONAL_INFORMATION:
        modelResult = await calculatePersonalInformationScoreService(admissionRequest);
        break;
      default:
        throw new Error(`Unsupported model: ${modelName}`);
    }

    // Return both modelName and result to match the wrapper's return format
    return { modelName, result: modelResult };
  } catch (error: any) {
    logger.error(
      `[calculateModelScoreService] Error calculating ${modelName} for request ${admissionRequest.id}`,
      error
    );

    // Return error result with same structure as success case
    return {
      modelName,
      result: {
        modelName,
        status: AnalysisStatus.error,
        modelScore: 0,
        modelFeatureScores: {},
        modelResult: { message: error.message },
        modelWeights: {},
      },
    };
  }
};
