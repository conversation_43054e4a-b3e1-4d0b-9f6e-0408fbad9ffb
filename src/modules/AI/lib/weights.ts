import { Weights } from './types';

// Define the RIDESHARE_PERFORMANCE_WEIGHTS constant
export const RIDESHARE_PERFORMANCE_WEIGHTS: Weights = {
  approved_docs_count: 0.04,
  pending_docs_count: -0.04,
  weekly_income: 0.4,
  total_earnings: 0.2,
  platform_count: 0.02,
  platform_deposits: 0.14,
  acceptanceRate: 0.05,
  cancellationRate: -0.05,
  rating: 0.05,
  lifetimeTrips: 0.05,
  timeSinceFirstTrip: 0.09,
  age: 0.05,
};

// Define the FINANCIAL_ASSESSMENT_WEIGHTS constant
export const FINANCIAL_ASSESSMENT_WEIGHTS: Weights = {
  cashflow_analysis: 2,
  debt_liabilities: 2,
  earnings_cross_check: 1,
};

// Define the HOME_VISIT_WEIGHTS constant
export const HOME_VISIT_WEIGHTS: Weights = {
  // Location quality - 25%
  city: 5,
  state: 3,
  municipality: 7,
  neighborhood: 10,
  // Visit verification - 30%
  isAddressProvidedByApplicant: 5,
  doesProofOfAddressMatchLocation: 10,
  behaviourOfCustomerDuringCall: 7,
  visitorObservations: 8,
  // Housing details - 45%
  housingType: 10,
  bedroomCount: 7,
  characteristicsOfGarage: 6,
  hasLivingRoom: 4,
  hasDiningRoom: 3,
  hasKitchen: 5,
  hasTelevision: 2,
  hasStove: 3,
  hasRefrigerator: 3,
  hasWashingMachine: 2,
};

// Define the PERSONAL_INFORMATION_WEIGHTS constant
export const PERSONAL_INFORMATION_WEIGHTS: Weights = {
  // Demographic Profile - 25%
  age: 5,
  maritalStatus: 5,
  dependentsCount: 10,
  occupation: 5,
  // Residence Stability - 15%
  timeInResidencyMonths: 15,
  // Location Quality - 25%
  neighborhood: 12,
  municipality: 8,
  city: 5,
  // Financial Indicators - 15%
  weeklyEarnings: 10,
  hasDebt: 5,
  // Asset Ownership - 15%
  ownsProperty: 8,
  ownsCar: 7,
  // Reference Quality - 5%
  hasValidReferences: 5,
};
