import { DocumentAnalysisStatus, MLModels } from '@/clean/domain/enums';
import { getAdmissionRequestByIdOrThrow } from '@/clean/domain/usecases';
import { AsyncController } from '@/types&interfaces/types';
import { calculateModelScoreService } from '../services/ai.service';
import { logger } from '@/clean/lib/logger';
import { analyzeDocumentService } from '../services/documentsAnalysis/documentsAnalysis.service';

/**
 * Controller for analyzing a specific model
 */
export const analyzeModel: AsyncController = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { modelName } = req.body;

    if (!Object.values(MLModels).includes(modelName)) {
      return res.status(400).send({
        message: `Invalid model name: ${modelName}`,
      });
    }

    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId);
    const result = await calculateModelScoreService(modelName, admissionRequest);

    return res.status(200).send(result);
  } catch (error) {
    logger.error(`[analyzeModel] Error: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while analyzing model',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Controller for document analysis
 */
export const analyzeDocument: AsyncController = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { documentMediaId } = req.body;

    const admissionRequest = await getAdmissionRequestByIdOrThrow(requestId);

    // Find the document in the request
    const documentMedia = admissionRequest.documentsAnalysis.documents.find(
      (doc) => doc.mediaId === documentMediaId
    );

    if (!documentMedia || !documentMedia.media) {
      return res.status(404).send({
        message: 'Document not found',
      });
    }

    // Verify the document analysis is pending
    if (
      documentMedia.analysisResult?.status === DocumentAnalysisStatus.valid ||
      documentMedia.analysisResult?.status === DocumentAnalysisStatus.invalid
    ) {
      logger.error(
        `[analyzeDocument] Document ${documentMedia.type} for request ${
          admissionRequest.id
        } is not in pending review status. Current status: ${documentMedia.status || 'undefined'}`
      );
      return res.status(400).send({
        message: `Document ${documentMedia.type} analysis is not in pending status.`,
      });
    }

    const result = await analyzeDocumentService(documentMedia, admissionRequest);

    return res.status(200).send(result);
  } catch (error) {
    logger.error(`[analyzeDocument] Error: ${error}`);
    return res.status(500).send({
      message: 'Internal server error while analyzing document',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
