import { slackTexts } from '@/constants';

export type IHomeVisitAppointmentScheduleLinkEmailTemplate = {
  link: string;
};

export function homeVisitAppointmentScheduleLinkEmailTemplate({
  link,
}: IHomeVisitAppointmentScheduleLinkEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            ¡Felicidades! 🎉 ¡Tu solicitud ha sido pre-aprobada! 🚗✨
          </p>
          <p class="text">
            El siguiente paso es muy sencillo: agenda tu visita domiciliaria virtual.
          </p>
          <p class="text" >
            <span>Información importante:</span>
            <br />
            Las visitas tienen una duración aproximada de 15 a 20 minutos, aunque los horarios disponibles son de 30 minutos.
            <br />
            Puedes agendar tu cita de lunes a viernes, de 9:00 a.m. a 6:00 p.m.
            <br />
            <br />
            Los espacios son limitados, así que te recomendamos agendar lo antes posible para asegurar tu lugar.
          </p>
          <p>Es indispensable que te encuentres en tu domicilio durante la visita.</p>
          <p>
            ¡Estamos listos para ayudarte a completar este último paso! ✅
          </p>
          <a href=${link} >Agendar visita ahora</a>
          
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export type IhomeVisitAppointmentScheduledEmailTemplate = {
  rescheduleLink: string;
  meetingLink: string;
  date: string;
  startTime: string;
};

export function homeVisitAppointmentScheduledEmailTemplate({
  rescheduleLink,
  meetingLink,
  date,
  startTime,
}: IhomeVisitAppointmentScheduledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            ¡Tu visita domiciliaria ha sido confirmada! 🎉
          </p>
          <p class="text">
            🗓 Fecha: ${date}
            <br />
            ⏰ Hora: ${startTime}
          </p>
          <p class="text" >
            <span>⚠️ Documentos necesarios:</span>
            <br />
            <br />
            ✅ INE (identificación oficial)
            <br />
            <br />
            ✅ Propietarios: Comprobante de domicilio + Recibo de predial o Certificado de residencia (CDMX).
            <br />
            <br />
            ✅ Inquilinos: Referencias de vecinos + Comprobante de domicilio + INE del propietario o Certificado de residencia (CDMX)
          </p>
          <p>📸 Fotos: 3 ángulos distintos de la fachada donde aparezcas</p>
          <p>📅 Duración: 15-20 minutos</p>
          <p>🏠 Importante: Estar en tu domicilio durante la cita</p>
          <p>¡Contáctanos en caso de que tengas alguna duda!</p>

          <p><a href=${meetingLink}>Enlace de reunión</a></p>
          <p><a href=${rescheduleLink}>Reagendar visita</a></p>

          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function homeVisitAppointmentFinishEmailTemplate() {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            ¡Gracias por tu tiempo! 🙌
          </p>

          <p>
            Queremos agradecerte por recibirnos durante la visita domiciliaria.
            Este paso es clave en el proceso, y valoramos tu disposición.
          </p>
          <p class="text">
            <span>¿Qué sigue ahora?</span>
            <br />
            <br />
            ✅ Estamos esperando la resolución final.
            <br />
            ✅ En caso de ser necesario, te notificaremos si se requiere el
            envío de documentación adicional.
          </p>
          <p>
            ⚠️ Te pedimos que, de ser el caso, envíes los documentos solicitados
            lo antes posible para evitar retrasos.
          </p>
          <p>
            Si tienes dudas o necesitas apoyo, no dudes en contactarnos. Estamos
            aquí para ayudarte. 😊
          </p>
          <p>¡Gracias por confiar en nosotros!</p>

          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function homeVisitAppointmentNoShowEmailTemplate({
  customerWebAppLink,
}: {
  name: string;
  customerWebAppLink: string;
}) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola
          </p>

          <p>
            Nos hemos dado cuenta de que no pudiste asistir a la visita
            domiciliaria programada para hoy. Esperamos que te encuentres bien.
          </p>

          <p>
            Para reagendar tu cita, simplemente haz clic en el siguiente enlace:
          </p>

          <a href="${customerWebAppLink}">Reagendar visita</a>

          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
  `;
}

export function homeVisitAppointmentApologyEmailTemplate({
  customerWebAppLink,
}: {
  customerWebAppLink: string;
}) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
  <html xmlns="http://www.w3.org/1999/xhtml">
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Appointment Scheduling</title>
      <style>
        body {
          font-family: Helvetica, Arial, sans-serif;
          margin: 0;
          padding: 0;
          background-color: #ffffff;
        }
        .email-container {
          max-width: 600px;
          margin: 0 auto;
          padding: 1rem 2rem;
          background-color: #ffffff;
        }
        .logo {
          text-align: left;
          margin-bottom: 24px;
        }
        .logo img {
          max-width: 150px;
          height: 32px;
        }
        .card {
          background-color: #ffffff;
        }
        .title {
          margin: 1rem 0;
          font-size: 36px;
          color: #344054;
        }
        .text {
          padding-bottom: 16px;
          color: #344054;
          font-size: 16px;
          line-height: 1.5;
        }
        .otp-code {
          font-size: 130%;
          font-weight: bold;
        }
        p {
          font-size: 16px;
          color: #344054;
        }
        .gracias-box {
          padding-top: 2rem;
        }
        .footer {
          background-color: #6600fa;
          color: #ffffff;
          text-align: center;
          height: 24px;
          line-height: 24px;
          font-size: 12px;
          margin-top: 20px;
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <div class="logo">
          <img
            src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
            alt="OneCarNow Logo"
          />
        </div>
        <div class="card">
          <div style="color: #000000; text-align: left">
            <p>
             ¡Hola!, Esperamos que estés muy bien.
            </p>
            <p>
              Lamentablemente, por causas de fuerza mayor, el visitador no podrá realizar la visita domiciliaria programada. 
              Te pedimos disculpas por cualquier inconveniente que esto pueda causarte. 🙏
            </p>
  
            <p>Por favor, te solicitamos reagendar la cita en el siguiente enlace:</p>
            <p>Gracias por tu comprensión, y quedamos atentos a cualquier duda o consulta que tengas. 😊</p>    
            <a href="${customerWebAppLink}">Reagendar visita</a>        
            <div class="gracias-box">
              <p>Gracias</p>
              <p>Team OCN</p>
            </div>
          </div>
        </div>
        <!-- Footer Section -->
        <div class="footer">© Copyright ${year} OCN</div>
      </div>
    </body>
  </html>
    `;
}

export function homeVisitAppointmentCancelEmailTemplate({
  customerWebAppLink,
}: {
  customerWebAppLink: string;
}) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>¡Hola!</p>
          <p>
            Notamos que cancelaste la visita a domicilio que habías programado.
            Esperamos que te encuentres bien.
          </p>
          <p>
            Reprograma fácilmente una nueva haciendo click en el siguiente
            enlace:
          </p>
          <a href="${customerWebAppLink}">Reagendar visita</a>
          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function homeVisitAppointmentReminderMessageOneNightAgoEmailTemplate({
  rescheduleLink,
  meetingLink,
  date,
  startTime,
}: IhomeVisitAppointmentScheduledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>¡Hola! 👋</p>
          <p>
            Te recordamos que hoy tienes agendada tu visita domiciliaria
            virtual.
          </p>
          <p class="text">
            🗓 Fecha: ${date}
            <br />
            ⏰ Hora: ${startTime}
          </p>

          <p>
            ⚠️ Por favor, asegúrate de tener listos los siguientes documentos
            para la reunión:
            <br />
            <br />
            ✅ Identificación oficial (INE).
          </p>
          <p>
            Uno de los siguientes comprobantes de propiedad (según tu
            situación):
            <br />
            <br />
            ✅ Si la vivienda es de tu propiedad:
            <br />
            ✅ Comprobante de domicilio a nombre del titular.
            <br />
            ✅ Recibo de predial.
            <br />
            ✅ Certificado de residencia (emitido en la página oficial del
            gobierno, solo para CDMX).
          </p>
          <p>
            Si rentas la vivienda:
            <br />
            ✅ Referencias de vecinos + Comprobante de domicilio + INE.
            <br />
            ✅ Comprobante de domicilio a nombre del propietario + INE del
            propietario.
            <br />
            ✅ Certificado de residencia (emitido en la página oficial del
            gobierno, solo para CDMX).
          </p>

          <p>
            Fotos desde 3 ángulos distintos de la fachada de tu casa donde
            aparezcas.
          </p>

          <p>
            📍 Recuerda: Es indispensable que te encuentres en tu domicilio
            durante la cita.
          </p>

          <p><a href="${meetingLink}">Enlace para reunión</a></p>
          <p><a href="${rescheduleLink}">Reagendar visita</a></p>
          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function homeVisitAppointmentReminderMessageAboutFiveMinutesAgoEmailTemplate({
  rescheduleLink,
  meetingLink,
  startTime,
}: IhomeVisitAppointmentScheduledEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>¡Hola! 👋
            <br />
            Tu visita domiciliaria virtual comienza en 5 minutos.
          </p>
          <p class="text">⏰ Hora: ${startTime}</p>

          <p>
            ⚠️ Documentos necesarios:
            <br />
            <br />
            ✅ INE (identificación oficial).
            <br />
            <br />
            ✅ Propietarios: Comprobante de domicilio + Recibo de predial o
            Certificado de residencia (CDMX).

            <br />
            <br />
            ✅ Inquilinos: Referencias de vecinos + Comprobante de domicilio +
            INE del propietario o Certificado de residencia (CDMX).
            <br />
            <br />
            📸 Fotos: 3 ángulos distintos de la fachada donde aparezcas.
          </p>

          <p>
            📍 Recuerda estar en tu domicilio.
            <br />
            <br />
            ¿Dudas o necesitas ayuda? Contáctanos. 😊
          </p>

          <p><a href="${meetingLink}">Enlace para reunión</a></p>
          <p><a href="${rescheduleLink}">Reagendar visita</a></p>
          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

interface IHomeVisitLocationVerificationEmailTemplate {
  name: string;
  link: string;
}

export function homeVisitLocationVerificationEmailTemplate({
  link,
  name,
}: IHomeVisitLocationVerificationEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            Haga clic en el siguiente enlace para verificar su ubicación.
          </p>

          <a href=${link} >Sitio web</a>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}

export function homeVisitFormApprovalEmailTemplate(customerWebAppLink: string) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box {
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>¡Hola!</p>
          <p>
            Nos alegra informarte que tu visita domiciliaria ha sido aprobada.
          </p>
          <p>
            🎉 Para continuar con tu solicitud y proceder con la entrega de tu
            auto, es necesario que subas la siguiente documentación en el enlace
            que te compartimos:
          </p>
          <p>
            📌 Documentos requeridos:
            <br />
            ✅ Constancia de situación fiscal
            <br />
            ✅ Licencia de conducir (frente)
            <br />
            ✅ Licencia de conducir (reverso)
            <br />
            ✅ CURP (pdf)
            <br />
            ✅ Foto selfie
            <br />
            ✅ Foto de tu garage
            <br />
            ✅ INE del Obligado Solidario (frontal)
            <br />
            ✅ INE del Obligado Solidario (reverso)
          </p>
          <p>
            Es importante que subas estos documentos lo antes posible, ya que
            son obligatorios para completar el proceso de entrega de tu auto.
            🚗💨
          </p>
          <p>
            Si tienes alguna duda o necesitas ayuda con la carga de documentos,
            no dudes en escribirnos. ¡Estamos aquí para apoyarte! 😊
          </p>
          <a href="${customerWebAppLink}">Sitio web</a>

          <div class="gracias-box">
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>`;
}

export type ISlackFallbackEmailTemplate = {
  userName: string;
  message: string;
  isError: boolean;
  fileContent?: string;
  fileName?: string;
  formattedDate: string;
  currentYear: number;
};

export function slackFallbackEmailTemplate({
  userName,
  message,
  isError,
  fileContent,
  fileName,
  formattedDate,
  currentYear,
}: ISlackFallbackEmailTemplate) {
  return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden;">
        <!-- Encabezado -->
        <div style="background-color: #0056b3; padding: 20px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px;">OCN - Resumen de Creación Masiva de Vehículos</h1>
        </div>
        
        <div style="padding: 25px 25px 5px 25px; background-color: white;">
          <p style="margin-top: 15px; font-size: 16px; color: #333; line-height: 1.5;">
            ${slackTexts.summaryTitle(userName)}
          </p>
        </div>

        <div style="background-color: ${isError ? '#fff8f8' : '#f8fff8'}; padding: 25px;">
          <h2 style="color: ${
            isError ? '#d32f2f' : '#2e7d32'
          }; margin: 0 0 15px 0; border-bottom: 1px solid #eaeaea; padding-bottom: 10px;">
            ${`Resumen del Lote Cargado el ${formattedDate}`}
          </h2>
          
          <div style="color: #333; line-height: 1.6; background-color: white; padding: 15px; border-radius: 5px; border-left: 4px solid ${
            isError ? '#d32f2f' : '#2e7d32'
          };">
            ${message
              .split('\n')
              .map((line) => `<p style="margin: 8px 0;">${line}</p>`)
              .join('')}
          </div>
          
          ${
            fileContent
              ? `
          <div style="margin-top: 15px; padding: 12px; background-color: #f5f5f5; border-radius: 5px; border-left: 4px solid #0277bd;">
            <p style="margin: 0; font-size: 14px; color: #333;">
              <strong>Archivo adjunto:</strong> Se ha adjuntado un archivo CSV con el registro detallado de los errores encontrados durante el proceso.
            </p>
          </div>
          `
              : ''
          }
        </div>
        
        <div style="background-color: #f5f5f5; padding: 15px; font-size: 14px; color: #555;">
          <p style="margin: 5px 0;">
            <strong>Fecha de carga:</strong> ${formattedDate}
          </p>
          ${fileName ? `<p style="margin: 5px 0;"><strong>Nombre del archivo:</strong> ${fileName}</p>` : ''}
        </div>
        
        <!-- Cierre y despedida -->
        <div style="padding: 20px 25px; background-color: white;">
          <p style="margin: 0; font-size: 16px; color: #333; line-height: 1.5;">
            Si detecta alguna inconsistencia en los datos o requiere información adicional sobre este proceso, por favor contacte al administrador del sistema a través de los canales habituales.
          </p>
          
          <p style="margin-top: 20px; font-size: 16px; color: #333;">
            Atentamente,
          </p>
          
          <p style="margin: 5px 0; font-size: 16px; font-weight: bold; color: #0056b3;">
            Equipo OCN
          </p>
        </div>
        
        <!-- Pie de página -->
        <div style="background-color: #eeeeee; padding: 15px; text-align: center; font-size: 12px; color: #666;">
          <p style="margin: 5px 0;">Este correo fue enviado automáticamente como respaldo porque la notificación de Slack falló.</p>
          <p style="margin: 5px 0;">© ${currentYear} Equipo OCN. Todos los derechos reservados.</p>
          <p style="margin: 5px 0;">Por favor no responda a este correo electrónico.</p>
        </div>
      </div>
    `;
}

interface IHomeImageUploadEmailTemplate {
  name: string;
  link: string;
}

export function homeImageUploadEmailTemplate({ link, name }: IHomeImageUploadEmailTemplate) {
  const year = new Date().getFullYear();

  return `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Appointment Scheduling</title>
    <style>
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 1rem 2rem;
        background-color: #ffffff;
      }
      .logo {
        text-align: left;
        margin-bottom: 24px;
      }
      .logo img {
        max-width: 150px;
        height: 32px;
      }
      .card {
        background-color: #ffffff;
      }
      .title {
        margin: 1rem 0;
        font-size: 36px;
        color: #344054;
      }
      .text {
        padding-bottom: 16px;
        color: #344054;
        font-size: 16px;
        line-height: 1.5;
      }
      .otp-code {
        font-size: 130%;
        font-weight: bold;
      }
      p {
        font-size: 16px;
        color: #344054;
      }
      .gracias-box{
        padding-top: 2rem;
      }
      .footer {
        background-color: #6600fa;
        color: #ffffff;
        text-align: center;
        height: 24px;
        line-height: 24px;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="logo">
        <img
          src="https://www.onecarnow.com/_next/static/media/logo.8ca06db0.webp"
          alt="OneCarNow Logo"
        />
      </div>
      <div class="card">
        <div style="color: #000000; text-align: left">
          <p>
            Hola ${name}
            <br />
            Haz clic en el enlace de abajo para subir las imágenes de tu hogar.
          </p>

          <a href=${link} >Sitio web</a>
          <div class="gracias-box" >
            <p>Gracias</p>
            <p>Team OCN</p>
          </div>
        </div>
      </div>
      <!-- Footer Section -->
      <div class="footer">© Copyright ${year} OCN</div>
    </div>
  </body>
</html>
`;
}
