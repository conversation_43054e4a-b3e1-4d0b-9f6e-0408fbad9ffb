import StockVehicle, { VehicleSubCategory, VehicleSubCategoryType } from '../../models/StockVehicleSchema';
import User from '../../models/userSchema';
import UserVehicleRestrictions from '../../models/userVehicleRestriction';
import { MiddlewareController } from '../../types&interfaces/types';

export const getVehiclesByRol: MiddlewareController = async (req, res, next) => {
  if (req.userReq.role !== 'auditor') return next();
  const user = await User.findById(req.userReq.userId);
  if (!user) return res.status(404).send({ message: 'User not found' });

  const hasRestrictions = await UserVehicleRestrictions.findOne({ userId: user._id });
  if (!hasRestrictions) {
    return res.status(200).send({
      stock: [],
      totalCount: 0,
      message: 'Vehiculos encontrados',
    });
  }

  const { search } = req.query;

  const restrictionsIds = hasRestrictions.stockRestrictions.map((item) => item._id);

  if (search) {
    const filter = {
      $or: [
        { brand: { $regex: search, $options: 'i' } },
        { model: { $regex: search, $options: 'i' } },
        { carNumber: { $regex: search, $options: 'i' } },
        { vin: { $eq: search } },
        { 'carPlates.plates': { $eq: search } },
      ],
      _id: { $in: restrictionsIds },
    };
    const stock = await StockVehicle.find(filter);
    return res.status(200).send(stock);
  }
  if (hasRestrictions) {
    const { page, limit } = req.query;
    const vehiclesPermitted = await StockVehicle.find(
      {
        _id: { $in: restrictionsIds },
      },
      {
        carNumber: 1,
        brand: 1,
        model: 1,
        status: 1,
        vehicleStatus: 1,
        category: 1,
        vin: 1,
        subCategory: 1,
        newCar: 1,
        step: 1,
        vehicleDocsComplete: 1,
        extensionCarNumber: 1,
        dischargedData: 1,
        color: 1,
        createdAt: 1,
        vehicleState: 1,
        carPlates: 1,
        isElectric: 1,
        contract: 1,
      }
    )
      .populate({ path: 'contract', select: 'contractNumber' })
      .skip(Number(page || '0') * Number(limit))
      .limit(Number(limit))
      .sort({ carNumber: 1, createdAt: -1 })
      .collation({ locale: 'en_US', numericOrdering: true })
      .exec();

    const filter = {
      _id: { $in: restrictionsIds },
    };
    const stock = await StockVehicle.find(filter, { subCategory: 1 }).lean();

    const subcategoryCounts: Record<VehicleSubCategoryType, number> = Object.values(
      VehicleSubCategory
    ).reduce(
      (acc, subcat) => {
        acc[subcat] = stock.filter((item) => item.subCategory === subcat).length;
        return acc;
      },
      {} as Record<VehicleSubCategoryType, number>
    );

    const totalCount = await StockVehicle.countDocuments(filter);
    // return res.status(200).send({ message: 'Vehicles found', totalCount, stock: vehiclesPermitted });
    return res.status(200).send({
      stock: vehiclesPermitted.map((item) => ({
        ...item.toObject(), // Convert Mongoose document to plain object
        subcategoryCounts,
      })),
      totalCount,
      message: 'Vehiculos encontrados',
    });
  }

  try {
    return next();
  } catch (error: any) {
    return res.status(500).send({ message: error.message });
  }
};
