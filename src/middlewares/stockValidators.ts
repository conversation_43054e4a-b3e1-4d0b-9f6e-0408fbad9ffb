import { validationResult } from 'express-validator';
import { NextFunction, Response } from 'express';
import { MyRequest } from '../types&interfaces/interfaces';
import fs from 'fs';
import { genericMessages, stockVehiclesText } from '../constants';
import StockVehicle from '../models/StockVehicleSchema';
import { Types } from 'mongoose';
import { PhysicalVehicleStatus } from '../models/StockVehicleSchema';

type Controller = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => void | Response<any, Record<string, any>>;

export const stockPostValidator: Controller = (req, res, next) => {
  const errors = validationResult(req);
  if (!req.files || Object.keys(req.files).length < 1 || Object.keys(req.body).length === 0) {
    return res.status(400).send({ message: stockVehiclesText.errors.missingBody });
  }
  if (!errors.isEmpty()) {
    const files: any = req.files;
    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(400).json({
      errors: errors.array(),
    });
  }
  return next();
};

type ControllerNextAsync = (
  req: MyRequest,
  res: Response,
  next: NextFunction
) => Promise<void | Response<any, Record<string, any>>>;

export const stockEditValidator: ControllerNextAsync = async (req, res, next) => {
  const { vehicleId } = req.params;
  //todo : fix this please
  try {
    const stockVehicle = await StockVehicle.findById(vehicleId);
    if (!stockVehicle) {
      const files: any = req.files;
      for (let file in files) {
        fs.unlink(files[file][0].path, (err) => {
          if (err) {
            // Manejar el error si ocurre al eliminar el archivo
          }
        });
      }
      return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
    }
    const errors = validationResult(req);
    if (!req.body) {
      const files: any = req.files;
      for (let file in files) {
        fs.unlink(files[file][0].path, (err) => {
          if (err) {
            // Manejar el error si ocurre al eliminar el archivo
          }
        });
      }
      return res.status(400).send({ msg: stockVehiclesText.errors.missingBody });
    }
    if (!errors.isEmpty()) {
      const files: any = req.files;
      for (let file in files) {
        fs.unlink(files[file][0].path, (err) => {
          if (err) {
            // Manejar el error si ocurre al eliminar el archivo
          }
        });
      }
      return res.status(400).json({
        errors: errors.array(),
      });
    }
    return next();
  } catch (error) {
    return res.status(500).send({ message: genericMessages.errors.somethingWentWrong, error });
  }
};

export const stockPatchValidate: ControllerNextAsync = async (req, res, next) => {
  const { vehicleId } = req.params;
  //todo : fix this please
  const stockVehicle = await StockVehicle.findById(vehicleId);
  if (!stockVehicle) {
    const files: any = req.files;
    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  }
  const errors = validationResult(req);
  if (!req.body) {
    const files: any = req.files;
    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(400).send({ msg: stockVehiclesText.errors.missingBody });
  }
  if (!errors.isEmpty()) {
    const files: any = req.files;
    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(400).json({
      errors: errors.array(),
    });
  }
  return next();
};

export const stockPatchValidateSingleDoc: ControllerNextAsync = async (req, res, next) => {
  const { vehicleId } = req.params;
  //todo : fix this please
  const stockVehicle = await StockVehicle.findById(vehicleId);
  if (!stockVehicle) {
    const files: any = req.file;
    fs.unlink(files.path, (err) => {
      if (err) {
        // Manejar el error si ocurre al eliminar el archivo
      }
    });

    return res.status(404).send({ message: stockVehiclesText.errors.vehicleNotFound });
  }
  const errors = validationResult(req);
  if (!req.body) {
    const files: any = req.file;
    fs.unlink(files.path, (err) => {
      if (err) {
        // Manejar el error si ocurre al eliminar el archivo
      }
    });

    return res.status(400).send({ msg: stockVehiclesText.errors.missingBody });
  }
  if (!errors.isEmpty()) {
    const files: any = req.files;
    for (let file in files) {
      fs.unlink(files[file][0].path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    }
    return res.status(400).json({
      errors: errors.array(),
    });
  }
  return next();
};

/**
 * Middleware to validate admin update of physical status request
 */
export const validateAdminUpdatePhysicalStatus = (req: MyRequest, res: Response, next: NextFunction) => {
  const { vehicleId } = req.params;
  const { newPhysicalStatus, notes } = req.body;

  // Validate vehicleId
  if (!Types.ObjectId.isValid(vehicleId)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid vehicle ID format.',
    });
  }

  // Validate newPhysicalStatus
  if (!newPhysicalStatus) {
    return res.status(400).json({
      success: false,
      message: 'newPhysicalStatus is required.',
    });
  }

  if (!Object.values(PhysicalVehicleStatus).includes(newPhysicalStatus as PhysicalVehicleStatus)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid physical status.',
    });
  }

  // Validate notes (if present)
  if (notes && typeof notes !== 'string') {
    return res.status(400).json({
      success: false,
      message: 'Notes must be a string.',
    });
  }

  return next();
};
