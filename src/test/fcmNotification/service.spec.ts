import fcmSchema from '@/models/fcmNotificationTokenSchema';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { fcmNotificationService } from '@/modules/FirebaseCloudMessaging/services/fcmNotification.service';
import {
  FCMNotificationStatus,
  FCMNotificationUserType,
} from '@/modules/FirebaseCloudMessaging/common/enums';
import fcmNotificationSchema from '@/models/fcmNotificationSchema';
import fcmNotificationTokenSchema from '@/models/fcmNotificationTokenSchema';

jest.mock('@/models/fcmNotificationTokenSchema', () => ({
  find: jest.fn(),
  create: jest.fn(),
  findOne: jest.fn(),
  updateOne: jest.fn(),
  deleteOne: jest.fn(),
}));

jest.mock('@/models/fcmNotificationSchema', () => ({
  find: jest.fn(),
  create: jest.fn(),
  updateOne: jest.fn(),
}));

jest.mock('@/clean/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

const mockSend = jest.fn();
const mockSendEach = jest.fn();

jest.mock('@/modules/FirebaseCloudMessaging/configuration/firebaseAdmin', () => ({
  firebaseAdmin: {
    messaging: () => ({
      sendEach: mockSendEach,
      send: mockSend,
    }),
  },
}));

describe('FCMNotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('registerNotificationToken', () => {
    it('should create a new FCM token entry if none exists', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue(null),
      });
      (fcmSchema.create as jest.Mock).mockResolvedValue({});

      const deviceDetails = { os: 'android', version: '23.0' };

      await fcmNotificationService.registerNotificationToken({
        userId: 'id',
        fcmToken: 'token',
        deviceDetails,
        userType: FCMNotificationUserType.ASSOCIATE,
      });

      expect(fcmSchema.create).toHaveBeenCalledWith({
        userId: 'id',
        fcmTokens: [
          expect.objectContaining({
            token: 'token',
            isActive: true,
            deviceDetails: deviceDetails,
          }),
        ],
        userType: FCMNotificationUserType.ASSOCIATE,
      });
    });

    it('should throw AlreadyExistException if token already exists and in active state', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token', isActive: true }],
        }),
      });

      await expect(
        fcmNotificationService.registerNotificationToken({
          userId: 'id',
          fcmToken: 'token',
          deviceDetails: { os: 'android', version: '23.0' },
          userType: FCMNotificationUserType.ASSOCIATE,
        })
      ).rejects.toThrow(AlreadyExistException);
    });

    it('should add token if not already present', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'other-token' }],
        }),
      });
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.registerNotificationToken({
        userId: 'id',
        fcmToken: 'token',
        deviceDetails: { os: 'android', version: '23.0' },
        userType: FCMNotificationUserType.ASSOCIATE,
      });

      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { userId: 'id' },
        expect.objectContaining({
          $push: expect.any(Object),
        })
      );
    });

    it('should update token to active if it exists but is inactive', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token', isActive: false }],
        }),
      });

      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });
      await fcmNotificationService.registerNotificationToken({
        userId: 'id',
        fcmToken: 'token',
        deviceDetails: { os: 'android', version: '23.0' },
        userType: FCMNotificationUserType.ASSOCIATE,
      });
      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { userId: 'id', 'fcmTokens.token': 'token' },
        {
          $set: {
            'fcmTokens.$.isActive': true,
            'fcmTokens.$.updatedAt': expect.any(Date),
          },
        }
      );
    });
  });

  describe('unregisterNotificationToken', () => {
    it('should unregister token if found', async () => {
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });

      await fcmNotificationService.unregisterNotificationToken('id', 'token');

      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { userId: 'id', 'fcmTokens.token': 'token' },
        { $pull: { fcmTokens: { token: 'token' } } }
      );
    });

    it('should throw NotFoundException if token not found', async () => {
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 });

      await expect(fcmNotificationService.unregisterNotificationToken('id', 'token')).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('updateNotificationTokenState', () => {
    it('should update token state if found', async () => {
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });

      await fcmNotificationService.updateNotificationTokenState('id', 'token', false);

      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { userId: 'id', 'fcmTokens.token': 'token' },
        { $set: { 'fcmTokens.$.isActive': false } }
      );
    });

    it('should throw NotFoundException if token not found', async () => {
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 });

      await expect(fcmNotificationService.updateNotificationTokenState('id', 'token', false)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe('FCMNotificationService - sendNotificationToAssociateById', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    const payload = {
      title: 'Test Title',
      body: 'Test Body',
      data: { foo: 'bar' },
    };

    it('should throw NotFoundException if no FCM tokens found', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue(null),
      });

      await expect(fcmNotificationService.sendNotificationToAssociateById('id', payload)).rejects.toThrow(
        NotFoundException
      );
    });

    it('should throw NotFoundException if no active tokens', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token1', isActive: false }],
        }),
      });

      await expect(fcmNotificationService.sendNotificationToAssociateById('id', payload)).rejects.toThrow(
        NotFoundException
      );
    });

    it('should call sendEach with active tokens and log success', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [
            { token: 'token1', isActive: true, deviceDetails: 'iPhone' },
            { token: 'token2', isActive: false, deviceDetails: 'Android' },
            { token: 'token3', isActive: true, deviceDetails: 'Web' },
          ],
        }),
      });

      (fcmNotificationSchema.create as jest.Mock)
        .mockResolvedValueOnce({ _id: 'notif1' })
        .mockResolvedValueOnce({ _id: 'notif2' });

      mockSendEach.mockResolvedValue({
        responses: [
          { success: true, messageId: 'msg1' },
          { success: true, messageId: 'msg2' },
        ],
      });

      (fcmNotificationSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.sendNotificationToAssociateById('id', payload);

      expect(mockSendEach).toHaveBeenCalledWith([
        {
          token: 'token1',
          notification: { title: 'Test Title', body: 'Test Body' },
          data: { foo: 'bar' },
        },
        {
          token: 'token3',
          notification: { title: 'Test Title', body: 'Test Body' },
          data: { foo: 'bar' },
        },
      ]);
    });

    it('should update notification status to failed if sendEach throws', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token1', isActive: true, deviceDetails: { os: 'iPhone' } }],
        }),
      });

      (fcmNotificationSchema.create as jest.Mock).mockResolvedValue({ _id: 'notif1' });

      mockSendEach.mockRejectedValue(new Error('FCM error'));

      (fcmNotificationSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.sendNotificationToAssociateById('id', payload);

      expect(fcmNotificationSchema.updateOne).toHaveBeenCalledWith(
        { _id: 'notif1' },
        expect.objectContaining({
          status: expect.any(String),
          failedDetails: expect.any(Object),
        })
      );
    });
  });

  describe('FCMNotificationService - resendFailedNotifications', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should log and return if there are no failed notifications', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([]),
      });

      await fcmNotificationService.resendFailedNotifications();

      expect(fcmNotificationSchema.find).toHaveBeenCalledWith({ status: FCMNotificationStatus.FAILED });
    });

    it('should log and return if there are no token details', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: { token: 'token1' },
            payload: { title: 't', body: 'b', data: {} },
          },
        ]),
      });
      (fcmNotificationTokenSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue(null),
      });

      await fcmNotificationService.resendFailedNotifications();

      expect(fcmNotificationTokenSchema.findOne).toHaveBeenCalled();
    });

    it('should skip notifications without failedMessageToken', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: {},
            payload: { title: 't', body: 'b', data: {} },
          },
        ]),
      });
      (fcmNotificationTokenSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            userId: 'user1',
            fcmTokens: [{ token: 'token1', oldToken: 'token1', isActive: true }],
          },
        ]),
      });

      await fcmNotificationService.resendFailedNotifications();

      expect(mockSend).not.toHaveBeenCalled();
    });

    it('should skip if no tokenDetail found for user', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: { token: 'token1' },
            payload: { title: 't', body: 'b', data: {} },
          },
        ]),
      });
      (fcmNotificationTokenSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            userId: 'user2',
            fcmTokens: [{ token: 'token2', oldToken: 'token2', isActive: true }],
          },
        ]),
      });

      await fcmNotificationService.resendFailedNotifications();

      expect(mockSend).not.toHaveBeenCalled();
    });

    it('should skip if no active token found for user', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: { token: 'token1' },
            payload: { title: 't', body: 'b', data: {} },
          },
        ]),
      });
      (fcmNotificationTokenSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            userId: 'user1',
            fcmTokens: [{ token: 'token2', oldToken: 'token2', isActive: true }],
          },
        ]),
      });

      await fcmNotificationService.resendFailedNotifications();

      expect(mockSend).not.toHaveBeenCalled();
    });

    it('should resend and update status to SENT on success', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: { token: 'token1' },
            payload: { title: 't', body: 'b', data: { foo: 'bar' } },
          },
        ]),
      });
      (fcmNotificationTokenSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          userId: 'user1',
          fcmTokens: [{ token: 'token1', oldToken: 'token1', isActive: true }],
        }),
      });

      mockSend.mockResolvedValue('deliveryMessageId');
      (fcmNotificationSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.resendFailedNotifications();

      expect(mockSend).toHaveBeenCalledWith({
        token: 'token1',
        notification: { title: 't', body: 'b' },
        data: { foo: 'bar' },
      });

      expect(fcmNotificationSchema.updateOne).toHaveBeenCalledWith(
        { _id: 'notif1' },
        {
          status: FCMNotificationStatus.SENT,
          messageId: 'deliveryMessageId',
          failedDetails: null,
        }
      );
    });

    it('should log error if resend fails', async () => {
      (fcmNotificationSchema.find as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue([
          {
            _id: 'notif1',
            userId: 'user1',
            failedDetails: { token: 'token1' },
            payload: { title: 't', body: 'b', data: {} },
          },
        ]),
      });
      (fcmNotificationTokenSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          userId: 'user1',
          fcmTokens: [{ token: 'token1', oldToken: 'token1', isActive: true }],
        }),
      });

      mockSend.mockRejectedValue(new Error('Send failed'));
      (fcmNotificationSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.resendFailedNotifications();

      expect(mockSend).toHaveBeenCalled();
      // Optionally, check that updateOne was not called with status SENT
      expect(fcmNotificationSchema.updateOne).not.toHaveBeenCalledWith(
        { _id: 'notif1' },
        expect.objectContaining({ status: FCMNotificationStatus.SENT, failedDetails: expect.any(Object) })
      );
    });
  });

  describe('FCMNotificationService - refreshNotificationToken', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should update the FCM token if found', async () => {
      (fcmNotificationTokenSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });

      await expect(
        fcmNotificationService.refreshNotificationToken({
          userId: 'user1',
          oldToken: 'oldToken',
          newToken: 'newToken',
        })
      ).resolves.toBeUndefined();

      expect(fcmNotificationTokenSchema.updateOne).toHaveBeenCalledWith(
        { userId: 'user1', 'fcmTokens.token': 'oldToken' },
        {
          $set: {
            'fcmTokens.$.token': 'newToken',
            'fcmTokens.$.oldToken': 'oldToken',
            'fcmTokens.$.updatedAt': expect.any(Date),
          },
        }
      );
    });

    it('should throw NotFoundException if no token is updated', async () => {
      (fcmNotificationTokenSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 });

      await expect(
        fcmNotificationService.refreshNotificationToken({
          userId: 'user1',
          oldToken: 'oldToken',
          newToken: 'newToken',
        })
      ).rejects.toThrow(NotFoundException);
    });

    it('should log and re-throw errors', async () => {
      (fcmNotificationTokenSchema.updateOne as jest.Mock).mockRejectedValue(new Error('DB error'));

      await expect(
        fcmNotificationService.refreshNotificationToken({
          userId: 'user1',
          oldToken: 'oldToken',
          newToken: 'newToken',
        })
      ).rejects.toThrow('DB error');
    });
  });
});
