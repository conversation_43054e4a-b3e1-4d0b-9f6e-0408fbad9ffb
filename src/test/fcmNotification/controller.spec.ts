import request from 'supertest';
import express from 'express';

import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import {
  registerNotificationToken,
  sendNotificationToAssociateById,
  unregisterNotificationToken,
  updateNotificationTokenState,
} from '@/modules/FirebaseCloudMessaging/controllers/fcmNotification.controller';
import { fcmNotificationService } from '@/modules/FirebaseCloudMessaging/services/fcmNotification.service';

jest.mock('@/modules/FirebaseCloudMessaging/services/fcmNotification.service');

const app = express();
app.use(express.json());
app.post('/register', registerNotificationToken);
app.post('/unregister', unregisterNotificationToken);
app.post('/update-state', updateNotificationTokenState);
app.post('/send', sendNotificationToAssociateById);

describe('registerNotificationToken', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should register a new FCM token and return 200', async () => {
    (fcmNotificationService.registerNotificationToken as jest.Mock).mockResolvedValue(undefined);

    const res = await request(app)
      .post('/register')
      .send({
        associateId: '507f1f77bcf86cd799439011',
        fcmToken: 'test-token',
        deviceDetails: {
          os: 'android',
          version: '23.0',
        },
      });

    expect(res.status).toBe(200);
    expect(res.body.message).toBe('FCM token registered successfully.');
    expect(fcmNotificationService.registerNotificationToken).toHaveBeenCalled();
  });

  it('should return 400 for invalid request data', async () => {
    const res = await request(app).post('/register').send({
      // Missing required fields
      fcmToken: 'test-token',
    });

    expect(res.status).toBe(400);
    expect(res.body.error).toBe('Invalid request data.');
  });

  it('should return 409 if token already exists', async () => {
    (fcmNotificationService.registerNotificationToken as jest.Mock).mockImplementation(() => {
      throw new AlreadyExistException();
    });

    const res = await request(app)
      .post('/register')
      .send({
        associateId: '507f1f77bcf86cd799439011',
        fcmToken: 'test-token',
        deviceDetails: {
          os: 'android',
          version: '23.0',
        },
      });

    expect(res.status).toBe(409);
    expect(res.body.error).toBe('AlreadyExists');
  });

  it('should return 500 for other errors', async () => {
    (fcmNotificationService.registerNotificationToken as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const res = await request(app)
      .post('/register')
      .send({
        associateId: '507f1f77bcf86cd799439011',
        fcmToken: 'test-token',
        deviceDetails: {
          os: 'android',
          version: '23.0',
        },
      });

    expect(res.status).toBe(500);
    expect(res.body.error).toBe('Error registering FCM token.');
  });
});

describe('unregisterNotificationToken', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should unregister a FCM token and return 200', async () => {
    (fcmNotificationService.unregisterNotificationToken as jest.Mock).mockResolvedValue(undefined);

    const res = await request(app).post('/unregister').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
    });

    expect(res.status).toBe(200);
    expect(res.body.message).toBe('FCM token unregistered successfully.');
    expect(fcmNotificationService.unregisterNotificationToken).toHaveBeenCalled();
  });

  it('should return 404 if token not found', async () => {
    (fcmNotificationService.unregisterNotificationToken as jest.Mock).mockImplementation(() => {
      throw new NotFoundException({ code: '404', message: 'FCM token not found for the associateId.' });
    });

    const res = await request(app).post('/unregister').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
    });

    expect(res.status).toBe(404);
    expect(res.body.error).toBe('FCM token not found for the associateId.');
  });

  it('should return 500 for other errors', async () => {
    (fcmNotificationService.unregisterNotificationToken as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const res = await request(app).post('/unregister').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
    });

    expect(res.status).toBe(500);
    expect(res.body.error).toBe('Error unregistering FCM token.');
  });
});

describe('updateNotificationTokenState', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should update token state and return 200', async () => {
    (fcmNotificationService.updateNotificationTokenState as jest.Mock).mockResolvedValue(undefined);

    const res = await request(app).post('/update-state').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
      isActive: false,
    });

    expect(res.status).toBe(200);
    expect(res.body.message).toBe('FCM token inactivated successfully.');
    expect(fcmNotificationService.updateNotificationTokenState).toHaveBeenCalled();
  });

  it('should return 400 for invalid request data', async () => {
    const res = await request(app).post('/update-state').send({
      // Missing required fields
      fcmToken: 'test-token',
    });

    expect(res.status).toBe(400);
    expect(res.body.error).toBe('Invalid request data.');
  });

  it('should return 404 if token not found', async () => {
    (fcmNotificationService.updateNotificationTokenState as jest.Mock).mockImplementation(() => {
      throw new NotFoundException({
        code: '404',
        message: 'FCM token not found for the associateId.',
      });
    });

    const res = await request(app).post('/update-state').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
      isActive: false,
    });

    expect(res.status).toBe(404);
    expect(res.body.error).toBe('FCM token not found for the associateId.');
  });

  it('should return 500 for other errors', async () => {
    (fcmNotificationService.updateNotificationTokenState as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const res = await request(app).post('/update-state').send({
      associateId: '507f1f77bcf86cd799439011',
      fcmToken: 'test-token',
      isActive: false,
    });

    expect(res.status).toBe(500);
    expect(res.body.error).toBe('Error inactivating FCM token.');
  });
});

describe('sendNotificationToAssociateById', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const validBody = {
    associateId: '507f1f77bcf86cd799439011',
    payload: {
      title: 'Test Title',
      body: 'Test Body',
      data: { foo: 'bar' },
    },
  };

  it('should send notification and return 200', async () => {
    (fcmNotificationService.sendNotificationToAssociateById as jest.Mock).mockResolvedValue(undefined);

    const res = await request(app).post('/send').send(validBody);

    expect(res.status).toBe(200);
    expect(res.body.message).toBe('Notification sent successfully.');
    expect(fcmNotificationService.sendNotificationToAssociateById).toHaveBeenCalledWith(
      validBody.associateId,
      validBody.payload
    );
  });

  it('should return 400 for invalid request data', async () => {
    const res = await request(app).post('/send').send({
      // Missing payload
      associateId: '507f1f77bcf86cd799439011',
    });

    expect(res.status).toBe(400);
    expect(res.body.error).toBe('Invalid request data.');
  });

  it('should return 404 if token not found', async () => {
    (fcmNotificationService.sendNotificationToAssociateById as jest.Mock).mockImplementation(() => {
      throw new NotFoundException({ code: '404', message: 'FCM token not found for the associateId.' });
    });

    const res = await request(app).post('/send').send(validBody);

    expect(res.status).toBe(404);
    expect(res.body.error).toBe('FCM token not found for the associateId.');
  });

  it('should return 500 for other errors', async () => {
    (fcmNotificationService.sendNotificationToAssociateById as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const res = await request(app).post('/send').send(validBody);

    expect(res.status).toBe(500);
    expect(res.body.error).toBe('Error sending notification.');
  });
});
